<?php

namespace App\Modules\AccountCredit\Services;

use App\Exceptions\FailedRequestException;
use Illuminate\Support\Facades\DB;

class AccountCreditService
{
    public static function instance(): self
    {
        $accountCreditService = new self;

        return $accountCreditService;
    }

    // ACCOUNT CREDIT BLOCKS

    public function createGenesis(int $userId)
    {
        $previousBlock = $this->getLatestBlock($userId);

        if ($previousBlock) {
            throw new FailedRequestException(400, 'Genesis block already exists.', 'Bad request');
        }

        AccountCreditMaker::genesis($userId);
    }

    public function addBlock(array $data, int $userId)
    {
        $previousBlock = $this->getLatestBlock($userId);

        if (! $previousBlock) {
            throw new FailedRequestException(400, 'No previous blocks found.', 'Bad request');
        }

        return AccountCreditMaker::add($previousBlock, $userId, $data);
    }

    public function query(int $userId, string $sortByIndex = 'desc')
    {
        return DB::table('account_credits')
            ->where('user_id', $userId)
            ->orderBy('block_index', $sortByIndex);
    }

    public function getLatestBlock(int $userId)
    {
        return $this->query($userId)->first() ?? null;
    }

    public function getBlockById(int $id, int $userId)
    {
        return $this->query($userId)->where('id', $id)->get()->first() ?? null;
    }
}
