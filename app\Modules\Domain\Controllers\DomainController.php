<?php

namespace App\Modules\Domain\Controllers;

use App\Exceptions\UserDomainException;
use App\Http\Controllers\Controller;
use App\Modules\Domain\Requests\DomainAuthCodeRequest;
use App\Modules\Domain\Requests\Nameserver\NameserverCheckRequest;
use App\Modules\Domain\Requests\Nameserver\NameserverSelectRequest;
use App\Modules\Domain\Requests\Nameserver\NameserverUpdateEppRequest;
use App\Modules\Domain\Requests\UpdateEppDomainRequest;
use App\Modules\Stripe\Services\StripeLimiter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DomainController extends Controller
{
    public function update(UpdateEppDomainRequest $request): Response
    {
        $request->update();

        return Inertia::render('Notice/ConfirmationMessage', ['message' => 'Update of domain is in process.']);
    }

    public function paymentFailed(Request $request): UserDomainException
    {
        throw new UserDomainException(400, $request->message, 'Bad request');
    }

    public function limitStripeError(): JsonResponse
    {
        return StripeLimiter::instance()->addAttempt();
    }

    public function requestAuthcode(DomainAuthCodeRequest $request): Response
    {
        $request->sendAuthCode();

        return Inertia::render('Notice/ConfirmationMessage', ['message' => 'Your request for authentication code is in process.']);
    }

    public function selectNameservers(NameserverSelectRequest $request): Response
    {
        $data = $request->selectNameservers();

        // throw new UserDomainException(404, 'Page maintenance in progress.', '');

        return Inertia::render('Domain/DomainNameserverForm', $data);
    }

    public function updateNameservers(NameserverUpdateEppRequest $request): Response
    {
        $request->update();

        return Inertia::render('Notice/ConfirmationMessage', ['message' => 'Updating nameservers is in process.']);
    }

    public function checkNameserver(NameserverCheckRequest $request): JsonResponse
    {
        $request->check();

        return response()->json(['success' => true], 200);
    }
}
