<?php

namespace App\Modules\Contact\Services;

use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\JobRetry\Services\RetryJobService;
use App\Modules\Notification\Services\ContactNotificationService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use Exception;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class JobContactService
{
    use UserLoggerTrait;

    private const STORE = 'store';

    private const UPDATE = 'update';

    public static function instance()
    {
        $jobContactService = new self;

        return $jobContactService;
    }

    public function store(array $params): void
    {
        $this->processEppRequest($params['eppData'], $params['registry'], $params['email'], self::STORE);
        ContactService::instance()->setDefaultContact($params['userContactId'], $params['userId'], $params['registryId'], $params['defaultContacts']);
        $this->updateContactStatus($params['userContactId']);
        $this->sendNotif($params['userId'], $params['eppData']['id'], $params['registry'], $params['email'], self::STORE);
        ContactService::instance()->updateContactSetup($params['userId']);
        $this->addRetryLogs(QueueConnection::CONTACT_REGISTRATION, ContactStatus::REGISTERED, $params, $params['userContactId']);
    }

    public function update(array $params): void
    {
        $this->processEppRequest($params['eppData'], $params['registry'], $params['email'], self::UPDATE);
        $this->updateLocal($params['contactId'], $params['localData']);
        $this->sendNotif($params['userId'], $params['eppData']['id'], $params['registry'], $params['email'], self::UPDATE);
        $this->addRetryLogs(QueueConnection::CONTACT_UPDATE, ContactStatus::REGISTERED, $params, $params['contactId']);
    }

    public function addRetryLogs(string $type, string $status, array $record, string $jobId): void
    {
        RetryJobService::instance()->store([
            'job_id' => $jobId,
            'type' => $type,
            'status' => $status,
            'record' => $record,
        ]);
    }

    // PRIVATE Functions

    private function processEppRequest(array $eppData, string $registry, string $email, string $requestType): void
    {
        $payload = $this->generateEppPayload($eppData);

        switch ($requestType) {
            case self::STORE:
                $response = EppContactService::instance()->callContactStore($payload, $registry, $email);
                break;
            case self::UPDATE:
                $response = EppContactService::instance()->callContactUpdate($payload, $registry, $email);
                break;
        }

        $this->evaluateEppResponse($response, $email, $requestType);
    }

    private function generateEppPayload(array $data): array
    {
        return [
            'id' => $data['id'],
            'voiceNumber' => $data['voice_number'],
            'faxNumber' => $data['fax_number'],
            'email' => $data['email'],
            'postal' => [
                'street' => $data['street'],
                'city' => $data['city'],
                'stateProvince' => $data['state_province'],
                'postalCode' => $data['postal_code'],
                'countryCode' => $data['country_code'],
                'name' => $data['name'],
                'organization' => $data['organization_name'],
            ],
        ];
    }

    private function evaluateEppResponse(array $response, string $email, string $requestType): void
    {
        if (in_array($response['status'], [Config::get('contact.response.created'), Config::get('contact.response.ok')])) {
            return;
        }

        $message = $requestType === self::STORE
            ? 'failed to register contact. Added to retry logs.'
            : 'failed to update contact. Added to retry logs.';

        app(AuthLogger::class)->error($this->fromWho($message, $email));
        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function updateContactStatus(string $userContactId): void
    {
        $contact_id = DB::table('user_contacts')->where('id', $userContactId)->value('contact_id');
        DB::table('contacts')->where('id', $contact_id)->update(['status' => ContactStatus::REGISTERED]);
    }

    public function updateLocal(string $contactId, array $localData): void
    {
        $localData['status'] = ContactStatus::REGISTERED;
        DB::table('contacts')->where('id', $contactId)->update($localData);
    }

    private function sendNotif(string $userId, string $contactId, string $registry, string $email, string $requestType): void
    {
        $payload = ['user_id' => $userId, 'contact' => $contactId, 'registry' => $registry, 'email' => $email];

        switch ($requestType) {
            case self::STORE:
                ContactNotificationService::instance()->sendContactCreatedNotif($payload);
                break;
            case self::UPDATE:
                ContactNotificationService::instance()->sendContactUpdatedNotif($payload);
                break;
        }
    }
}
