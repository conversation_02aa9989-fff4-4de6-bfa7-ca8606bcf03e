<?php

require_once 'app\Modules\Contact\Tests\Helper.php';

use Illuminate\Support\Facades\Validator;
use App\Modules\Contact\Requests\SetDefaultContactRequest;
use App\Modules\Contact\Tests\Datasets\ContactValidationDataset;

it('passes validation with valid data', function () {
    $data = ContactValidationDataset::validSetDefaultContactData();
    $request = new SetDefaultContactRequest;

    $contact_name = 'test';
    $registry = createTestRegistry('verisign');
    $contact = createTestContact(['name' => $contact_name]);
    $userContact = createTestUserContact($this->user->id, $contact->id, $registry->id, []);

    $data['userContactId'] = (string) $userContact->id;
    $data['userId'] = (string) $this->user->id;
    $data['registryId'] = (string) $registry->id;

    $validator = Validator::make($data, $request->rules());

    expect($validator->passes())->toBeTrue(json_encode($validator->errors()));
});

testInvalidContactData(new SetDefaultContactRequest(), ContactValidationDataset::invalidSetDefaultContactData());