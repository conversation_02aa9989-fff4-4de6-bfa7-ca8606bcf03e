<?php

namespace App\Modules\Auth\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Auth\Services\RegisterUserService;
use App\Rules\PasswordConfirmed;
use App\Rules\ValidFormat;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Password;

class StoreNewAccountRequest extends FormRequest
{
    // protected $redirect = 'bad-request';
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (strcmp(ClientIp::getClientIp($this), $this->ip) == 0) {
            return true;
        }

        throw new FailedRequestException(403, 'This action is not authorized.', 'Unauthorized');
    }

    public function rules(): array
    {
        return [
            'ip' => ['required', 'string', 'min:1', 'max:16'],
            'first_name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s]*$/'],
            'last_name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s]*$/'],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:users', "regex:/^[0-9a-zA-Z.@_-]*$/"],
            'password' => ['required', 'string', Password::defaults()],
            'password_confirmation' => ['required', 'string', new PasswordConfirmed($this->password)],
            'street' => ['required', 'string', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'city' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'state_province' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'postal_code' => ['required', 'string', 'min:3', 'max:8', "regex:/^[0-9a-zA-Z\s\-]*$/"],
            'country_code' => ['required', 'string', 'size:2', "regex:/^[a-zA-Z]*$/"],
        ];
    }

    public function store()
    {
        RegisterUserService::setUserIp($this->email, $this->ip);
        Auth::login(RegisterUserService::store($this));
    }

    public function messages()
    {
        return [
            'first_name.regex' => 'The first name field must only contain letters.',
            'last_name.regex' => 'The last name field must only contain letters.',
            'state_province.required' => 'The state/province field is required.',
            'password.confirmed' => 'The password confirmation field does not match.',
            'country_code.required' => 'The country field is required.',
        ];
    }
}
