<?php

namespace App\Exceptions;

use Exception;
use Inertia\Inertia;

class UnauthorizedRequestException extends Exception
{
    public function __construct(int $code, string $help, string $error)
    {
        $this->code = $code;
        $this->message = $help;
        $this->file = $error;
    }

    public function render()
    {
        return Inertia::render('Errors/LoginRequiredPage', [
            'code' => $this->code,
            'help' => $this->message,
            'error' => $this->file,
        ]);
    }
}
