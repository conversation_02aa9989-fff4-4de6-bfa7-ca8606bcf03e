<?php

namespace App\Util\Helper;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\Crypt;

class CryptHelper
{
    public static function generate(string $prefix = '')
    {
        return $prefix.substr(bin2hex(random_bytes(10)), 0, 13);
    }

    public static function encrypt(?string $value): ?string
    {
        if (empty($value)) {
            return null;
        }

        return Crypt::encryptString($value);
    }

    public static function decrypt(string $value): string
    {
        try {
            return Crypt::decryptString($value);
        } catch (\Throwable $th) {
            app(AuthLogger::class)->error($th->getMessage());
        }

        return 'NO_ID';
    }
}
