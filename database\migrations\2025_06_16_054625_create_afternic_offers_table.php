<?php

use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('afternic_offers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users');
            $table->string('domain_name');
            $table->integer('offer_price');
            $table->integer('counter_offer_price')->default(0)->nullable();
            $table->string('offer_status')->default(AfternicOfferConstants::WAITING);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('afternic_offers');
    }
};
