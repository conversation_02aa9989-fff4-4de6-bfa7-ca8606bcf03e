<?php

namespace App\Modules\MarketPlace\Requests;

use App\Events\MyCartCountEvent;
use App\Modules\MarketPlace\Services\MarketCartService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CartAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'tld_id' => 'required|exists:tlds,id',
            'name' => 'required|string',
            'price' => 'required|numeric',
            'is_fast_transfer' => 'required|numeric',
            'vendor' => 'required|string',
        ];
    }

    public function add()
    {
        MarketCartService::instance()->addToCart($this->user_id, $this->tld_id, $this->name, $this->price, $this->is_fast_transfer, $this->vendor);
        MyCartCountEvent::dispatch(Auth::user()->id);
    }
}
