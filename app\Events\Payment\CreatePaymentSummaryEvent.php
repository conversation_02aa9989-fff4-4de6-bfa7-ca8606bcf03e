<?php

namespace App\Events\Payment;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CreatePaymentSummaryEvent
{
    use Dispatchable, SerializesModels;

    public int $userId;

    public array $data;

    public string $type;

    public function __construct(array $data, int $userId, string $type)
    {
        $this->userId = $userId;
        $this->type = $type;
        $this->data = $data;
    }
}
