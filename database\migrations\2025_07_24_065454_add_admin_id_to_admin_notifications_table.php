<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_notifications', function (Blueprint $table) {
            Schema::table('admin_notifications', function (Blueprint $table) {
                $table->unsignedBigInteger('admin_id')->nullable()->after('id');
            });

            // Update existing records where admin_id is null
            DB::table('admin_notifications')->whereNull('admin_id')->update(['admin_id' => 1]);

            // Optionally, make the column NOT NULL after updating
            Schema::table('admin_notifications', function (Blueprint $table) {
                $table->unsignedBigInteger('admin_id')->nullable(false)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_notifications', function (Blueprint $table) {
            $table->dropColumn('admin_id');
        });
    }
};
