<?php

namespace App\Modules\Auth\Services;

use App\Events\EmailSent;
use App\Mail\EmailOtpMail;
use App\Models\EmailOtp;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use <PERSON><PERSON><PERSON>\Agent\Agent;

class EmailOtpService
{
    /* TRAITS */
    // ?...

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Generate Entry
     */
    public function generateEntry(User $user, string $ipAddress): array
    {
        $agent = new Agent;

        // ! Turned into CronJob due to conflicts
        // EmailOtp::query()
        //     ->where('user_id', '=', $user->id)
        //     ->where('session_valid_until', '=', null)
        //     ->delete();

        $code = random_int(100000, 999999);
        $username = $user->first_name;
        $userAgent = $agent->browser();
        $codeValidUntil = now()->addMinutes(5);
        $sessionToken = Str::random(32);

        EmailOtp::create(
            [
                'user_id' => $user->id,
                'ip' => $ipAddress,
                'session_token' => $sessionToken,
                'user_agent' => $agent->browser(),
                'code' => $code,
                'code_valid_until' => $codeValidUntil,
            ]
        );

        return compact('code', 'userAgent', 'codeValidUntil', 'sessionToken', 'username');
    }

    /**
     * Regenerate Entry
     */
    public function regenerateEntry(string $token): array
    {
        $entry = EmailOtp::query()
            ->with('user')
            ->where('session_token', '=', $token)
            ->first();

        $userAgent = $entry->user_agent;
        $sessionToken = $token;
        $username = $entry->user->first_name;
        $code = random_int(100000, 999999);
        $codeValidUntil = now()->addMinutes(5);
        $userEmail = $entry->user->email;

        $entry->update(
            [
                'code' => $code,
                'code_valid_until' => $codeValidUntil,
            ]
        );

        return compact('code', 'userAgent', 'codeValidUntil', 'sessionToken', 'userEmail', 'username');
    }

    /**
     * Verify Code
     */
    public function verifyCode(string $token, array $data): int
    {
        $emailOtp = EmailOtp::where('session_token', '=', $token)
            ->first();

        if ($emailOtp->code_valid_until <= now()) {
            return false;
        }

        if (Hash::check($data['code'], $emailOtp->code) == false) {
            return false;
        }

        $emailOtp->session_valid_until = now()->addDays(30);
        $emailOtp->save();

        return $emailOtp->user_id;
    }

    /**
     * Send Email
     */
    public function sendEmail(string $addressTo, array $data): void
    {
        $payload = json_encode([
            'code' => $data['code'],
            'username' => $data['username'],
            'userAgent' => $data['userAgent'],
            'codeValidUntil' => $data['codeValidUntil'],
            'email' => $addressTo,
        ]);

        Mail::to($addressTo)
            ->send(new EmailOtpMail($data));

        $userId = EmailOtp::where('session_token', $data['sessionToken'])->value('user_id');

        event(new EmailSent(
            $userId,
            $data['username'],
            $addressTo,
            'Email Otp Mail',
            'OTP Verification',
            $payload,
            null
        ));
    }
}
