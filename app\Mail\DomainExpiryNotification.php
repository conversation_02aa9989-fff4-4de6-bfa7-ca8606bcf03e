<?php

namespace App\Mail;

use App\Modules\Client\Constants\ScheduleType;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DomainExpiryNotification extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $payload;

    private $list;

    private $noticeId;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public $uniqueFor = 120; // 2 minutes

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct(array $payload)
    {
        $this->payload = $payload;
        $this->list = $this->getExpiredDomains($this->payload['domains']);
        $this->noticeId = $payload['notice_id'];
    }

    public function uniqueId(): int
    {
        return $this->noticeId;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address($this->payload['sender_address'], $this->payload['sender_name']),
            subject: 'Urgent: '.config('app.name').' Expiration Notice',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = $this->getView();

        return new Content(
            markdown: $view,
            with: [
                'greeting' => $this->payload['greeting'],
                'expired_domains_count' => count($this->list['expired']),
                'expiring_domains_count' => count($this->list['expiring']),
                'sender_name' => $this->payload['sender_name'],
                'url' => $this->payload['url'],
                'notice_id' => $this->noticeId,
                'filename' => $this->payload['filename'],
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [
            Attachment::fromPath($this->payload['attachment'])
                ->as($this->payload['filename'])
                ->withMime('text/csv'),
        ];
    }

    public function failed($e)
    {
        app(AuthLogger::class)->error('DomainExpiryNotification: '.$e->getMessage());
        $this->fail();
    }

    private function getExpiredDomains($domains): array
    {
        $today = now()->timestamp * 1000;

        $list['expired'] = [];
        $list['expiring'] = [];

        foreach ($domains as $domain) {
            if ($domain->expiry < $today) {
                $list['expired'][] = $domain;
            } else {
                $list['expiring'][] = $domain;
            }
        }

        return $list;
    }

    private function getView()
    {
        switch ($this->payload['type']) {
            case ScheduleType::FIRST:
                return 'Mails.ExpiryNotices.FirstNotice';
            case ScheduleType::SECOND:
                return 'Mails.ExpiryNotices.SecondNotice';
            case ScheduleType::THIRD:
                return 'Mails.ExpiryNotices.ThirdNotice';
            default:
                return 'Mails.ExpiryNotices.FirstNotice';
        }
    }
}
