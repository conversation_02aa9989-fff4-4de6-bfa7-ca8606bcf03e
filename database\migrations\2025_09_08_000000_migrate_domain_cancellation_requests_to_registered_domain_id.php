<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('registered_domain_id')->nullable()->after('id');
        });

        $this->migrateDataToRegisteredDomainId();

        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('registered_domain_id')->nullable(false)->change();
            $table->foreign('registered_domain_id')->references('id')->on('registered_domains');
        });

        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->dropColumn(['domain_id', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->dropForeign(['registered_domain_id']);
            $table->unsignedBigInteger('domain_id')->nullable()->after('id');
            $table->unsignedBigInteger('user_id')->nullable()->after('domain_id');
        });

        $this->restoreDataFromRegisteredDomainId();

        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id')->nullable(false)->change();
            $table->unsignedBigInteger('user_id')->nullable(false)->change();
            $table->dropColumn('registered_domain_id');
        });
    }

    private function migrateDataToRegisteredDomainId(): void
    {
        $cancellationRequests = DB::table('domain_cancellation_requests')
            ->whereNull('registered_domain_id')
            ->get(['id', 'domain_id', 'user_id']);

        foreach ($cancellationRequests as $request) {
            $registeredDomain = DB::table('registered_domains')
                ->where('domain_id', $request->domain_id)
                ->first(['id']);

            if ($registeredDomain) {
                DB::table('domain_cancellation_requests')
                    ->where('id', $request->id)
                    ->update(['registered_domain_id' => $registeredDomain->id]);
            } else {
                throw new Exception("No registered domain found for domain_id: {$request->domain_id} in cancellation request ID: {$request->id}");
            }
        }
    }

    private function restoreDataFromRegisteredDomainId(): void
    {
        $cancellationRequests = DB::table('domain_cancellation_requests')
            ->join('registered_domains', 'domain_cancellation_requests.registered_domain_id', '=', 'registered_domains.id')
            ->join('user_contacts', 'registered_domains.user_contact_registrar_id', '=', 'user_contacts.id')
            ->get([
                'domain_cancellation_requests.id as request_id',
                'registered_domains.domain_id',
                'user_contacts.user_id'
            ]);

        foreach ($cancellationRequests as $request) {
            DB::table('domain_cancellation_requests')
                ->where('id', $request->request_id)
                ->update([
                    'domain_id' => $request->domain_id,
                    'user_id' => $request->user_id
                ]);
        }
    }
};
