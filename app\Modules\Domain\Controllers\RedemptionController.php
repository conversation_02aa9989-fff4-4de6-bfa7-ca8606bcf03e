<?php

namespace App\Modules\Domain\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Domain\Requests\Redemption\RedemptionConfirmRequest;
use App\Modules\Domain\Requests\Redemption\RedemptionPayRequest;
use App\Modules\Domain\Requests\Redemption\RedemptionDomainRequest;
use Inertia\Inertia;
use Inertia\Response;

class RedemptionController extends Controller
{
    public function confirm(RedemptionConfirmRequest $request): Response
    {
        return Inertia::render('Domain/Redemption', $request->getData());
    }

    public function pay(RedemptionPayRequest $request): Response
    {
        return Inertia::render('Domain/RedemptionPayment', $request->getRedemptionPaymentData());
    }

    public function restore(RedemptionDomainRequest $request): Response
    {
        $summaryId = $request->update();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment Successful. Domain redemption is in process.',
            'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
        ]);
    }
}
