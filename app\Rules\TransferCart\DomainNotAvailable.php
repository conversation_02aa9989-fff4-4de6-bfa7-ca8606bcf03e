<?php

namespace App\Rules\TransferCart;

use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Transfer\Constants\TransferRequest;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Closure;


class DomainNotAvailable implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->isInTransferRequest($value)) {
            $fail('This domain has already been requested for transfer.');
            return;
        }

        if ($this->isInRegisteredDomains($value)) {
            $fail('This domain is already registered under StrangeDomains.');
            return;
        }

        if ($this->isInTransferCart($value)) {
            $fail('Domain was already in the cart.');
            return;
        }
    }

    // PRIVATE Functions

    private function isInTransferRequest(string $value): bool
    {
        return DB::table('transfer_domains')
            ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.name', $value)
            ->whereIn('transfer_domains.status', [
                TransferRequest::PENDING_REQUEST,
                TransferRequest::PENDING_APPROVAL,
                EppDomainStatus::TRANSFER_CLIENT_CONFLICT
            ])->whereNull('domains.deleted_at')->exists();
    }

    private function isInRegisteredDomains(string $value): bool
    {
        return DB::table('domains')->where('name', $value)
            ->whereNull('deleted_at')->exists();
    }

    private function isInTransferCart(string $value): bool
    {
        return DB::table('transfer_carts')->where('name', $value)
            ->where('user_id', Auth::id())->whereNull('deleted_at')->exists();
    }
}
