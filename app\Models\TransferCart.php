<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TransferCart extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'tld_id',
        'name',
        'auth_code',
        'privacy',
    ];

    public function tld()
    {
        return $this->belongsTo(Tld::class, 'tld_id');
    }
}
