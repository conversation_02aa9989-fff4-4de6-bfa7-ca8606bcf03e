<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trigger_subscribers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('threshold_subscriber_id')->constrained('threshold_subscribers');
            $table->foreignId('transaction_trigger_id')->constrained('transaction_triggers');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trigger_subscribers');
    }
};
