<?php

namespace App\Modules\Guest\Tests\Requests;

use App\Modules\Guest\Requests\ClientQueryForm;
use Illuminate\Support\Facades\Validator;

it('fails validation when required fields are missing', function () {
    $validator = Validator::make([], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue();
    expect($validator->errors()->has('message'))->toBeTrue();
});

it('fails validation with invalid name format', function () {
    $validator = Validator::make([
        'name' => '123456',
        'email' => '<EMAIL>',
        'message' => 'This is a valid message for testing purposes'
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
});

it('fails validation with name too short', function () {
    $validator = Validator::make([
        'name' => 'A',
        'email' => '<EMAIL>',
        'message' => 'This is a valid message for testing purposes'
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
});

it('fails validation with name too long', function () {
    $validator = Validator::make([
        'name' => str_repeat('A', 51),
        'email' => '<EMAIL>',
        'message' => 'This is a valid message for testing purposes'
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
});

it('fails validation with invalid email', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => 'invalid-email',
        'message' => 'This is a valid message for testing purposes'
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue();
});

it('fails validation with message too short', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'message' => 'short'
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('message'))->toBeTrue();
});

it('fails validation with message too long', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'message' => str_repeat('A', 501)
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('message'))->toBeTrue();
});

it('passes validation with valid data', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'message' => 'This is a valid message for testing purposes'
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeFalse();
});

it('passes validation with name containing special characters', function () {
    $validator = Validator::make([
        'name' => "O'Connor-Smith",
        'email' => '<EMAIL>',
        'message' => 'This is a valid message for testing purposes'
    ], (new ClientQueryForm)->rules());

    expect($validator->fails())->toBeFalse();
});