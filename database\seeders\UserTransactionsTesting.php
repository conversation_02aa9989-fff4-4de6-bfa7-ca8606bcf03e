<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserTransactionsTesting extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed transactions
        $transactions = [
            ['name' => 'Register', 'system_limit' => 20, 'user_limit' => 5, 'length' => 30, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['name' => 'Renew', 'system_limit' => 20, 'user_limit' => 5, 'length' => 30, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
        ];
        DB::table('transactions')->insert($transactions);

        // Get inserted transaction IDs
        $transactionIds = DB::table('transactions')->pluck('id', 'name');

        // Seed user_transactions
        $userTransactions = [
            ['transaction_id' => $transactionIds['Register'], 'user_id' => 1, 'custom_limit' => 3,  'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['transaction_id' => $transactionIds['Renew'], 'user_id' => 1, 'custom_limit' => 3,  'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['transaction_id' => $transactionIds['Register'], 'user_id' => 2, 'custom_limit' => 3,  'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['transaction_id' => $transactionIds['Renew'], 'user_id' => 2, 'custom_limit' => 3,  'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],

        ];
        DB::table('user_transactions')->insert($userTransactions);

        // Get inserted user_transaction IDs
        $userTransactionIds = DB::table('user_transactions')->pluck('id');

        // Seed user_daily_transactions
        $today = Carbon::now()->toDateString();
        $yesterday = Carbon::yesterday()->toDateString();

        $userDailyTransactions = [
            ['user_transaction_id' => $userTransactionIds[0], 'counter' => 2, 'date' => $today],
            ['user_transaction_id' => $userTransactionIds[1], 'counter' => 1, 'date' => $yesterday],
            ['user_transaction_id' => $userTransactionIds[1], 'counter' => 3, 'date' => $yesterday],
            ['user_transaction_id' => $userTransactionIds[2], 'counter' => 1, 'date' => $yesterday],
            ['user_transaction_id' => $userTransactionIds[2], 'counter' => 4, 'date' => $yesterday],
            ['user_transaction_id' => $userTransactionIds[3], 'counter' => 4, 'date' => $yesterday],
        ];
        DB::table('user_daily_transactions')->insert($userDailyTransactions);
    }
}
