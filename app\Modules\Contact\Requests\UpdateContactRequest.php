<?php

namespace App\Modules\Contact\Requests;

use App\Modules\Contact\Services\ContactService;
use App\Rules\ContactNameExists;
use App\Rules\ValidFormat;
use Illuminate\Foundation\Http\FormRequest;

class UpdateContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        // dd($this->registry);
        return [
            'registry' => ['required', 'string', 'in:pir,verisign'],
            'name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s]*$/', new ContactNameExists($this->registry, $this->contact_id)],
            'organization_name' => ['required', 'string', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'registry_contact' => ['required', 'string', 'min:3', 'max:15', "regex:/^[0-9a-zA-Z]*$/"],
            'voice_number' => ['required', 'string', 'min:5', 'max: 12', "regex:/[0-9]/"],
            'email' => ['required', 'string', 'email:rfc,dns', "max:100", "regex:/^[0-9a-zA-Z.@_-]*$/"],
            'unit' => ['nullable', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'street' => ['required', 'string', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'city' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'state_province' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'postal_code' => ['required', 'string', 'min:3', 'max: 8', "regex:/^[0-9a-zA-Z\s\-]*$/"],
            'country_code' => ['required', 'string', 'size:2', "regex:/^[a-zA-Z]*$/"],
            'ext_voice_number' => ['required', 'string', 'max: 3', "regex:/[0-9]/"],
            'ext_fax_number' => ['nullable', 'string', 'max:3', "regex:/[0-9]/"],
            'fax_number' => ['nullable', 'string', 'min:5', 'max: 12', "regex:/[0-9]/"],
        ];
    }

    protected function prepareForValidation(): void
    {
        $voice_number = trim($this->voice_number);
        $fax_number = trim($this->fax_number);

        $formatted_voice_number = preg_replace('/[^0-9]/', '', $voice_number);
        $formatted_fax_number = preg_replace('/[^0-9]/', '', $fax_number);

        $this->merge([
            'id' => $this->contact_id,
            'voice_number' => $formatted_voice_number,
            'fax_number' => $formatted_fax_number,
        ]);
    }

    public function messages(): array
    {
        return [
            'name.regex' => 'The name should only contain letters and spaces.',
            'state_province.required' => 'The state/province field is required.',
            'country_code.required' => 'The country field is required.',
        ];
    }

    public function attributes(): array
    {
        return [
            'voice_number' => 'phone number',
            'ext_fax_number' => 'area code',
            'ext_voice_number' => 'area code',
        ];
    }

    protected function passedValidation(): void
    {
        $this->merge([
            'country_code' => strtoupper($this->country_code),
            'voice_number' => '+' . $this->ext_voice_number . '.' . $this->voice_number,
            'id' => $this->registry_contact,
            'state_province' => ucwords(strtolower($this->state_province)),
        ]);

        //! ONLY ADD FAX_NUMBER IF IT'S NOT NULL
        if (!is_null($this->fax_number)) {
            $this->merge([
                'fax_number' => '+' . $this->ext_fax_number . '.' . $this->fax_number,
            ]);
        } else {
            $this->merge([
                'fax_number' => null,
            ]);
        }
    }

    public function update()
    {
        $eppData = $this->extractEppData();
        $localData = $this->extractLocalData();

        ContactService::instance()->update($this->contact_id, $localData, $eppData, $this->registry);
    }

    // PRIVATE Function

    private function extractEppData(): array
    {
        $exclusions = ['unit', 'registry', 'ext_fax_number', 'ext_voice_number', 'registry_contact', 'contact_id'];

        return $this->except($exclusions);
    }

    private function extractLocalData(): array
    {
        $exclusions = ['id', 'registry', 'registry_contact', 'contact_id'];

        return $this->except($exclusions);
    }
}
