<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\ClientActivityEvent;
use App\Events\DomainHistoryEvent;
use App\Events\UpdateDomainsTableEvent;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Util\Helper\Domain\DomainParser;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DomainPrivacyService
{
    use UserLoggerTrait;

    private $isJob = true;

    private $userId;

    private $userEmail;

    private $maxAttemptError = 3;

    private $dispatchTenSeconds = 10;

    public static function instance(): self
    {
        return new self;
    }

    public function getDomainById(int $id): array
    {
        $query = DB::table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->select([
                'domains.id',
                'domains.name',
                'domains.registrant',
                'domains.client_status',
                'domains.privacy_protection',
                'registered_domains.id as registered_domain_id',
                'registered_domains.status as registered_status',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
            ])
            ->where('domains.id', $id);

        $domain = $query->first();

        return (array) $domain;
    }

    public function updatePrivacy(array $request): string
    {
        $domains = $request['domains'];
        $privacy = $request['privacy'];

        if (empty($domains)) {
            return 'No domains to process.';
        }

        $updatedDomains = $this->getUpdatedDomainList($domains, $privacy);

        if (empty($updatedDomains)) {
            return 'No domains to process.';
        }

        $this->setDomainsToInProcess($updatedDomains);
        $this->dispatchJobs($updatedDomains, $privacy);
        $this->clientTrack($updatedDomains[0], $privacy);
        $this->domainTrack($updatedDomains[0], $privacy);

        return $this->getMessageNotification($updatedDomains, $privacy);
    }

    private function getUpdatedDomainList(array $domains, bool $privacy): array
    {
        $updatedDomains = [];

        foreach ($domains as $domain) {
            $domain['privacy_protection'] = $privacy;
            $updatedDomains[] = $domain;
        }

        return $updatedDomains;
    }

    private function createPayload(array $domain, bool $privacy): array
    {
        $domainPayload = $this->createDomain($domain, $privacy);
        $registeredDomain = $this->createRegisteredDomain($domain);
        $registry = DomainParser::getRegistryName($domain['name']);

        return [
            JobPayloadKeys::DOMAIN => $domainPayload,
            JobPayloadKeys::REGISTERED_DOMAIN => $registeredDomain,
            JobPayloadKeys::REGISTRY => $registry,
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_PRIVACY,
        ];
    }

    private function createDomain(array $domain, bool $privacy): array
    {
        $status = json_decode($domain['client_status'], true);

        $payload = [
            'id' => $domain['id'],
            'name' => $domain['name'],
            'registrant' => $domain['registrant'],
            'client_status' => $status,
            'privacy_protection' => $privacy,
        ];

        return $payload;
    }

    private function createRegisteredDomain(array $domain): array
    {
        $payload = [
            'id' => $domain['registered_domain_id'],
            'name' => $domain['name'],
        ];

        return $payload;
    }

    private function setDomainsToInProcess(array $updatedDomains): void
    {
        $updatedObj = collect($updatedDomains);
        $domainUpdateIds = $updatedObj->pluck('id')->toArray();
        DomainService::instance()->updateDomainStatus($domainUpdateIds, DomainStatus::IN_PROCESS);

        UpdateDomainsTableEvent::dispatch($this->getUserId());
    }

    private function dispatchJobs(array $updatedDomains, bool $privacy): void
    {
        $count = 0;
        foreach ($updatedDomains as $domain) {
            $payload = $this->createPayload($domain, $privacy);
            JobDispatchService::instance()->updateEppDispatch(
                $payload,
                $this->dispatchTenSeconds + $count,
            );
            $count += 1;
        }
    }

    private function getMessageNotification(array $updatedDomains, bool $privacy): string
    {
        $textDomain = (count($updatedDomains) == 1) ? 'domain' : 'domains';

        if ($privacy) {
            return 'Enabling privacy protection for '.$textDomain.' is in process.';
        }

        return 'Disabling privacy protection for '.$textDomain.' is in process.';
    }

    private function domainTrack(array $domains, bool $privacy): void
    {
        event(new DomainHistoryEvent([
            'domain_id' => $domains['id'],
            'type' => 'DOMAIN_UPDATED',
            'user_id' => $this->getUserId(),
            'status' => 'success',
            'message' => 'Privacy protection updated'.($privacy ? ' to enabled' : ' to disabled'),
            'payload' => $domains,
        ]));
    }

    private function clientTrack(array $domains, bool $privacy): void
    {
        event(new ClientActivityEvent(
            $this->getUserId(),
            UserTransactionType::DOMAIN_UPDATE,
            'Domain: '.$domains['name'].' | Privacy protection updated'.($privacy ? ' to enabled' : ' to disabled'),
            '',
            $domains
        ));
    }

    private function getUserId(): int
    {
        return Auth::id() ?? 0;
    }

    private function getUserEmail(): string
    {
        return Auth::user()?->email ?? 'Unauthorized';
    }
}
