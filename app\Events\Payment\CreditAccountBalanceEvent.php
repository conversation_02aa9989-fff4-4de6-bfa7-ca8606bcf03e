<?php

namespace App\Events\Payment;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CreditAccountBalanceEvent
{
    use Dispatchable, SerializesModels;

    public int $userId;

    public int $sourceId;

    public int $paymentServiceId;

    public string $sourceType;

    public function __construct(
        int $sourceId,
        int $paymentServiceId,
        int $userId,
        string $sourceType
    ) {
        $this->sourceId = $sourceId;
        $this->paymentServiceId = $paymentServiceId;
        $this->userId = $userId;
        $this->sourceType = $sourceType;
    }
}
