<?php

namespace App\Modules\Category\Requests;

use App\Modules\Category\Services\CategoryService;
use Illuminate\Foundation\Http\FormRequest;

class DeleteCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'ids'   => ['array', 'required'],
            'ids.*' => ['integer', 'exists:user_categories,id'],
        ];
    }

    public function show()
    {
        return CategoryService::instance()->getAvailableCategories($this->ids);
    }

    public function getTargets()
    {
        return CategoryService::instance()->getToBeDeletedCategoryInfo($this->ids);
    }
}
