<?php

namespace App\Modules\Contact\Services;

use App\Events\ClientActivityEvent;
use App\Events\ContactSetupEvent;
use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\Contact\Jobs\RegisterEppContact;
use App\Modules\Contact\Jobs\UpdateEppContact;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\Notification\Services\ContactNotificationService;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ContactService
{
    use CursorPaginate, UserLoggerTrait;

    private $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        $contactService = new self;

        return $contactService;
    }

    public function store(array $eppData, array $localData, array $defaultContacts, array $registries): void
    {
        $userContactsData = $this->storeLocal($localData, $registries);

        foreach ($userContactsData as $userContact) {
            $this->EppStore($userContact, $eppData, $defaultContacts);

            $contactData = [
                'name' => $localData['name'],
                'registry' => $userContact['registry'],
                'registry_contact' => $userContact['registryContact'],
            ];

            $message = "Contact created: '{$localData['name']}' for {$userContact['registry']} registry";
            $this->createContactActivityEvent(auth()->user()->id, $message, $contactData);
        }
    }

    public function updateContactSetup(string $userId): void
    {
        $registeredContactCount = $this->getNumberOfRegisteredContact($userId);

        if ($registeredContactCount > 1) {
            return;
        }

        $contactSetupIsDone = $this->setContactSetupValue($userId, $registeredContactCount);
        $this->redirectIfContactSetupIsDone($userId, $contactSetupIsDone);

        $this->createContactSetupActivityEvent($userId, $registeredContactCount, $contactSetupIsDone);
    }

    public function getUserContactForEdit(string $id): ?object
    {
        $data = $this->getUserContact($id);

        if (! $data) {
            return null;
        }

        $voice_number = $this->separateNumberFormat($data->voice_number);
        $data->voice_number = $voice_number['number'];
        $data->ext_voice_number = $voice_number['ext'];

        $fax_number = $this->separateNumberFormat($data->fax_number);
        $data->fax_number = $fax_number['number'];
        $data->ext_fax_number = $fax_number['ext'];

        return $data;
    }

    public function getUserContact(string $id): ?object
    {
        return DB::table('user_contacts')
            ->select('user_contacts.id as user_contact_id', 'user_contacts.*', 'contacts.*', 'registries.name as registry_name')
            ->join('contacts', 'contacts.id', '=', 'user_contacts.contact_id')
            ->join('registries', 'registries.id', '=', 'user_contacts.registry_id')
            ->where('user_contacts.id', $id)
            ->first();
    }

    public function setDefaultContact(string $userContactId, string $userId, string $registryId, array $defaultContacts): void
    {
        if (empty($defaultContacts)) {
            return;
        }

        $this->removeUserDefaultContacts($userId, $registryId, $defaultContacts);
        $this->setDefaultContacts($userContactId, $defaultContacts);
    }

    public function update(string $contactId, array $localData, array $eppData, string $registry): void
    {
        $this->updateStatus($contactId, ContactStatus::IN_PROCESS);
        $this->EppUpdate($contactId, $localData, $eppData, $registry);

        app(AuthLogger::class)->info($this->fromWho('edited a contact.'));
    }

    // PRIVATE Functions

    private function storeLocal(array $contactData, array $registries): array
    {
        $userContactsData = [];
        $notifData = [];

        $this->executeLocalStore($userContactsData, $notifData, $contactData, $registries);
        $this->updateContactSetup(auth()->user()->id);
        ContactNotificationService::instance()->sendContactInProcessNotif(auth()->user()->id, $notifData);

        return $userContactsData;
    }

    private function executeLocalStore(array &$userContactsData, array &$notifData, array $contactData, array $registries): void
    {
        $registriesData = $this->getRegistryData($registries);

        foreach ($registriesData as $registry) {
            $contactPayload = $this->generateContactPayload($contactData, $registry->id);
            $contactId = $this->insertContactAndGetId($contactPayload);
            $userContactId = $this->insertUserContactAndGetId($contactId, $registry->id);
            $userContactsData[] = $this->extractUserContactData($userContactId, $registry, $contactPayload['registry_contact']);
            $notifData[] = $this->extractNotifData($contactPayload['registry_contact'], $registry->name);
        }
    }

    private function removeUserDefaultContacts(string $userId, string $registryId, array $defaultContacts): void
    {
        foreach ($defaultContacts as $key => $value) {
            DB::table('user_contacts')
                ->where('user_id', $userId)
                ->where('registry_id', $registryId)
                ->where($key, true)
                ->update([$key => false]);
        }
    }

    private function setDefaultContacts(string $userContactId, array $defaultContacts): void
    {
        DB::table('user_contacts')->where('id', $userContactId)->update($defaultContacts);
    }

    private function separateNumberFormat($number): array
    {
        if ($number == null)
        {
            return [
                'number' => null,
                'ext'    => null
            ];
        }

        $contact   = preg_replace('/^\+/', '', $number);
        $separated = explode('.', $contact);

        return ['number' => $separated[1], 'ext' => $separated[0]];
    }

    private function getNumberOfRegisteredContact(string $userId): int
    {
        return DB::table('user_contacts')
            ->join('contacts', 'contacts.id', '=', 'user_contacts.contact_id')
            ->where('user_id', $userId)
            ->where('contacts.status', ContactStatus::REGISTERED)
            ->count();
    }

    private function setContactSetupValue(string $userId, int $registeredContactCount): bool
    {
        $contact_setup = $registeredContactCount == 1 ? true : false;
        DB::table('users')->where('id', $userId)->update(['contact_setup' => $contact_setup]);

        return $contact_setup;
    }

    private function redirectIfContactSetupIsDone(string $userId, bool $contactSetup): void
    {
        if ($contactSetup) {
            ContactSetupEvent::dispatch($userId);
        }
    }

    private function getRegistryData(array $registries): array
    {
        return DB::table('registries')
            ->whereIn('name', $registries)
            ->select('name', 'id')
            ->get()->toArray();
    }

    private function generateContactPayload(array $contactData, string $registryId): array
    {
        $contactData['status'] = ContactStatus::IN_PROCESS;
        $contactData['created_at'] = $this->now;
        $contactData['updated_at'] = $this->now;
        $contactData['registry_contact'] = $this->generateRegistryContact($registryId);

        return $contactData;
    }

    private function insertContactAndGetId(array $contactData): string
    {
        return DB::table('contacts')->insertGetId($contactData);
    }

    private function insertUserContactAndGetId(string $contactId, string $registryId): string
    {
        return DB::table('user_contacts')->insertGetId([
            'user_id' => auth()->user()->id,
            'contact_id' => $contactId,
            'registry_id' => $registryId,
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);
    }

    private function extractUserContactData(string $userContactId, object $registryData, string $registryContact): array
    {
        return [
            'id' => $userContactId,
            'registry' => $registryData->name,
            'registryId' => $registryData->id,
            'registryContact' => $registryContact,
        ];
    }

    private function extractNotifData(string $registryContact, string $registryName): array
    {
        return [
            'contact' => $registryContact,
            'registry' => $registryName,
        ];
    }

    private function updateStatus(string $contactId, string $status): void
    {
        DB::table('contacts')->where('id', $contactId)->update(['status' => $status, 'updated_at' => $this->now]);
    }

    private function EppStore(array $userContact, array $eppData, array $defaultContacts): void
    {
        $userId = auth()->user()->id;
        $userEmail = auth()->user()->email;
        $eppData['id'] = $userContact['registryContact'];

        RegisterEppContact::dispatch($userContact['id'], $eppData, $defaultContacts, $userContact['registry'], $userContact['registryId'], $userId, $userEmail);
    }

    private function EppUpdate(string $contactId, array $localData, array $eppData, string $registry): void
    {
        $userId = auth()->user()->id;
        $userEmail = auth()->user()->email;

        UpdateEppContact::dispatch($contactId, $localData, $eppData, $registry, $userEmail, $userId);
    }

    private function generateRegistryContact(string $registry_id): string
    {
        $first_name = auth()->user()->first_name;
        $last_name = auth()->user()->last_name;
        $now = Carbon::now()->timestamp;
        $contact_id = $first_name[0].$last_name[0].$registry_id.$now;

        return strtolower($contact_id);
    }

    private function createContactSetupActivityEvent(string $userId, int $registeredContactCount, bool $contactSetupIsDone): void
    {
        $contact = $this->getUserContact($userId);

        $contactData = [
            'name' => $contact->name ?? null,
            'registry' => $contact->registry_name ?? null,
            'registry_contact' => $contact->registry_contact ?? null,
            'registered_count' => $registeredContactCount,
            'setup_completed' => $contactSetupIsDone,
        ];

        $message = "Contact setup updated: {$registeredContactCount} registered contact(s), setup ".
                   ($contactSetupIsDone ? 'completed' : 'in progress');

        $this->createContactActivityEvent($userId, $message, $contactData);
    }

    private function createContactActivityEvent(string $userId, string $message, ?array $payload = null): void
    {
        event(new ClientActivityEvent(
            $userId,
            UserTransactionType::CONTACT_UPDATE,
            $message,
            '',
            $payload
        ));
    }
}
