<?php

namespace App\Modules\DomainEppSearch\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\DomainEppSearch\Requests\CheckMultipleDomainRequest;
use App\Modules\DomainEppSearch\Services\SearchServiceProvider;
use App\Modules\MarketPlace\Jobs\AfternicUpdateTransferFromPoll;
use App\Modules\MarketPlace\Requests\MarketBasicRequest;
use App\Modules\MarketPlace\Requests\MarketSearchRequest;
use App\Modules\MarketPlace\Requests\SearchRequest;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class SearchController extends Controller
{
    public function view(): Response
    {
        return Inertia::render('Domain/Search', SearchServiceProvider::getSearchData());
    }

    public function check(CheckMultipleDomainRequest $request)
    {
        return $request->search();
    }

    public function aiSearch(SearchRequest $request)
    {
        return $request->doAISearch();
    }

    public function marketSearch(MarketSearchRequest $request)
    {
        return $request->doMarketSearch();
    }

    public function basicSearch(MarketBasicRequest $request)
    {
        return $request->doBasicSearch();
    }
}
