<?php

namespace App\Http\Resources\UserDomainExport;

use App\Http\Resources\UserDomainExport\UserDomainExportResource; 

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class UserDomainExportCollection extends ResourceCollection
{
    /**
     * The resource that this collection collects 
     * 
     * @var string 
     */
    public $collects = UserDomainExportResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return parent::toArray($request);
    }
}
