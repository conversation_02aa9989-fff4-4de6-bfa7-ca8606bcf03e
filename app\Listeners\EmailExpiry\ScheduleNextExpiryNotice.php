<?php

namespace App\Listeners\EmailExpiry;

use App\Modules\Client\Constants\ScheduleType;
use App\Modules\Client\Constants\SourceType;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\ExpirationNoticeService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class ScheduleNextExpiryNotice implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(MessageSent $event): void
    {

        try {
            // app(AuthLogger::class)->info('ScheduleNextExpiryNotice:: '.json_encode($event->data['notice_id']));
            if (! isset($event->data['notice_id'])) {
                return;
            }

            $noticeId = $event->data['notice_id'];
            $this->handleScheduler($noticeId);
        } catch (\Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }

    private function handleScheduler($noticeId)
    {
        $notice = $this->getNotice($noticeId);

        if (! $notice) {
            return;
        }

        if ($notice->type !== ScheduleType::THIRD) {
            ScheduleDomainExpiryNotice::dispatch($notice->user_id, SourceType::SEND_MAIL, $notice->type);

            return;
        }

        $this->setDomainsToExpired($notice);
        ScheduleDomainExpiryNotice::dispatch($notice->user_id, SourceType::SEND_MAIL, $notice->type);
        // app(AuthLogger::class)->info('Schedule next expiry notice - email sent: '.$notice->type);
    }

    private function getNotice($noticeId)
    {
        return DB::table('domain_expiration_notifications')
            ->where('id', $noticeId)
            ->first() ?? null;
    }

    private function setDomainsToExpired($notice)
    {
        $domains = ExpirationNoticeService::getList(
            $notice->user_id,
            $notice->lead_expiry_date,
            DomainStatus::ACTIVE,
            true,
            false,
        );

        if ($domains->isEmpty()) {
            return;
        }

        $domainIds = $domains->pluck('id')->toArray();
        DomainService::instance()->updateDomainStatus($domainIds, DomainStatus::EXPIRED);
    }
}
