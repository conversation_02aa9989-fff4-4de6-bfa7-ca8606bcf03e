<?php

namespace App\Modules\Cart\Services;

use App\Modules\Cart\Constants\CartDeleteType;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UserCart
{
    public Collection $cart;

    public function __construct()
    {
        $this->cart = $this->get();
    }

    public function query(): Builder
    {
        return DB::table('carts')
            ->where('carts.user_id', $this->getUserId())
            ->whereNull('carts.deleted_at')
            ->orderBy('carts.id', 'ASC');
    }

    public function get(): Collection
    {
        return $this->query()->get();
    }

    public function getTotalYear(): int
    {
        return $this->cart ? $this->cart->pluck('year_length')->sum() : 0;
    }

    public function getTotalDomain(): int
    {
        return $this->cart ? $this->cart->count() : 0;
    }

    public function getAll(): array
    {
        return $this->cart ? $this->cart->all() : [];
    }

    public function getDomainsId(): array
    {
        return $this->cart ? $this->cart->pluck('id')->toArray() : [];
    }

    public function getOnCartNames(): array
    {
        return $this->cart ? $this->cart->pluck('name')->toArray() : [];
    }

    public function getDeletedCartNames(): array
    {
        return DB::table('carts')
            ->where('delete_type', CartDeleteType::CHECKED_OUT)
            ->orWhereNull('delete_type')
            ->whereNotNull('deleted_at')
            ->get()
            ->pluck('name')
            ->toArray();
    }

    public function filterDuplicateDomains(): Collection
    {
        $cartContent = $this->cart;
        $cartNames = $this->cart->pluck('name')->all();

        $notUnique = $this->query()->whereIn('name', $cartNames)->get();
        if ($notUnique->isNotEmpty()) {
            $names = $notUnique->pluck('name')->all();
            foreach ($cartContent as &$cartItem) {
                if (in_array($cartItem->name, $names)) {
                    unset($cartItem);
                }
            }
        }

        return $cartContent;
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }
}
