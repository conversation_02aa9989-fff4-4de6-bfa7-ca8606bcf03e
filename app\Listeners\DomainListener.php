<?php

namespace App\Listeners;

use App\Events\DomainHistoryEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\DomainTransactionHistory;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Modules\Histories\Constants\DomainTransactionType;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  DomainHistoryEvent  $event
     * @return void
     */
    public function handle(DomainHistoryEvent $event)
    {
        try {
            $this->createTransactionHistory($event);
            app(AuthLogger::class)->info('Domain History: '.$event->message);
        } catch (Exception $e) {
            $this->createTransactionHistory($event, DomainTransactionType::FAILED);
            app(AuthLogger::class)->error('Domain History: '.$e->getMessage());
        }
    }

    /**
     * Create transaction history record
     *
     * @param DomainHistoryEvent $event
     * @param string|null $overrideStatus
     * @return void
     */
    private function createTransactionHistory(DomainHistoryEvent $event, ?string $overrideStatus = null): void
    {
        DomainTransactionHistory::create([
            'domain_id' => $event->domainId,
            'type' => $event->type,
            'user_id' => $event->userId,
            'status' => $overrideStatus ?? $event->status,
            'message' => $event->message,
            'payload' => json_encode($event->payload),
        ]);
    }
}