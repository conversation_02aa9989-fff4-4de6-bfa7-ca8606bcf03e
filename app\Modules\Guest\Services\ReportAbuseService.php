<?php

namespace App\Modules\Guest\Services;

use App\Events\EmailSent;
use App\Mail\AcknowledgmentMessage;
use App\Mail\ReportAbuseMessage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;

class ReportAbuseService
{
    private static $instance = null;

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
        
    }

    public function sendReportAbuse($data)
    {
        $attachmentPath = null;
        if (isset($data['attachment'])) {
            $attachmentPath = $data['attachment']->store('public/attachments');
        }

        Mail::to(config('mail.from.abuse'))->send(new ReportAbuseMessage([
            'sender' => $data['email'],
            'name' => $data['name'],
            'domain' => $data['domain'],
            'category' => $data['category'],
            'body' => $data['message'],
            'attachmentPath' => $attachmentPath,
        ]));

        $mailable = new AcknowledgmentMessage([
            'name' => $data['name'],
            'domain' => $data['domain'],
        ]);

        Mail::to($data['email'])->send($mailable);

        $payload = json_encode([
            'sender' => $data['email'],
            'name' => $data['name'],
            'domain' => $data['domain'],
            'category' => $data['category'],
            'body' => $data['message'],
            'attachmentPath' => $attachmentPath,
            'ip' => request()->ip(),
        ]);

        $userId = Auth::id();

        event(new EmailSent($userId, $data['name'], $data['email'], 'Acknowledge from your Report Abuse', 'Report Abuse', $payload, null));
    }
}