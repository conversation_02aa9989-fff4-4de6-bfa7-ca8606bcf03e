import { <PERSON>m<PERSON><PERSON><PERSON><PERSON>, ImSort<PERSON>lpha<PERSON>c, ImSortAlphaDesc } from "react-icons/im";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";
import { router } from "@inertiajs/react";
import _Product from "@/Constant/_Product";
import {
    MdOutlineClose,
    MdOutlineQuestionMark,
    MdOutlineThumbUpAlt,
    MdRotateLeft,
    MdWarningAmber
} from "react-icons/md";


export default function ProductsList({ items }) {

    const params = route().params
    const orderby = params.orderby ?? _Product.SORT_TYPE.updated_desc;

    const callSortOrder = (order) => {
        let payload = route().params;
        payload.orderby = order;

        router.get(route("domains.products", payload));
    };

    const getStatusRowValue = (status) => {
        status = status.toLowerCase()
        let color = 'text-primary'

        switch (status) {
            case _Product.STATUS_TYPES.pending:
                color = 'text-yellow-500'
                break
            case _Product.STATUS_TYPES.transfer_requested:
                color = 'text-primary'
                break
            case _Product.STATUS_TYPES.completed:
                color = 'text-green-500'
                break
            case _Product.STATUS_TYPES.cancelled:
                color = 'text-red-500'
                break
            case _Product.STATUS_TYPES.pending_hold:
                color = 'text-yellow-300'
                break
            case _Product.STATUS_TYPES.pending_order:
                color = 'text-yellow-700'
                break
        }

        return <div className={`${color} font-bold capitalize`}>{status}</div>
    }

    const getStatusIcon = (status) => {
        const size = 20;
        status = status.toLowerCase();
        switch (status) {
            case _Product.TAB_TYPES.pending:
            case _Product.TAB_TYPES.transfer_requestedtransfer_requested:
                return <MdRotateLeft className="text-primary" size={size} />
            case _Product.TAB_TYPES.cancelled:
                return <MdOutlineClose className="text-danger" size={size} />
            case _Product.TAB_TYPES.completed:
                return <MdOutlineThumbUpAlt className="text-green-500" size={size} />
            default:
                return <MdOutlineQuestionMark size={size} />
        };
    };

    const DomainLabel = () => {
        return (
            <label className="flex items-center pl-2 space-x-2">
                <span>Domain</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === _Product.SORT_TYPE.domain_asc
                                ? _Product.SORT_TYPE.domain_desc
                                : _Product.SORT_TYPE.domain_asc
                        )
                    }
                >
                    {orderby === _Product.SORT_TYPE.domain_asc ? (
                        <ImSortAlphaAsc />
                    ) : (
                        <ImSortAlphaDesc />
                    )}
                </button>
            </label>
        );
    };

    const PriceLabel = () => {
        return (
            <label className="flex items-center space-x-2">
                <span className="">Price</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === _Product.SORT_TYPE.price_asc
                                ? _Product.SORT_TYPE.price_desc
                                : _Product.SORT_TYPE.price_asc
                        )
                    }
                >
                    {orderby === _Product.SORT_TYPE.price_asc ? (
                        <TbSortAscending2 />
                    ) : (
                        <TbSortDescending2 />
                    )}
                </button>
            </label>
        );
    };

    const StatusUpdatedLabel = () => {
        return (
            <label className="flex items-center space-x-2">
                <span className="">Last Status Update</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === _Product.SORT_TYPE.updated_asc
                                ? _Product.SORT_TYPE.updated_desc
                                : _Product.SORT_TYPE.updated_asc
                        )
                    }
                >
                    {orderby === _Product.SORT_TYPE.updated_asc ? (
                        <TbSortAscending2 />
                    ) : (
                        <TbSortDescending2 />
                    )}
                </button>
            </label>
        );
    };

    const CreatedLabel = () => {
        return (
            <label className="flex items-center space-x-2">
                <span className="">Created</span>
                <button
                    onClick={() =>
                        callSortOrder(
                            orderby === _Product.SORT_TYPE.created_asc
                                ? _Product.SORT_TYPE.created_desc
                                : _Product.SORT_TYPE.created_asc
                        )
                    }
                >
                    {orderby === _Product.SORT_TYPE.created_asc ? (
                        <TbSortAscending2 />
                    ) : (
                        <TbSortDescending2 />
                    )}
                </button>
            </label>
        );
    }

    const ProductList = () => {
        return (
            <>
                {
                    items.map(
                        (item, index) =>
                        (
                            <tr key={item.order_id + `- ${index}`} className="hover:bg-gray-100" >
                                <td><span>{item.order_id}</span></td>
                                <td><span>{item.domain}</span></td>
                                <td><span>$ {item.price ?? 0}</span></td>
                                <td>
                                    <div className="flex space-x-2">
                                        {getStatusRowValue(item.status)}
                                        <div className="my-auto">
                                            {getStatusIcon(item.status)}
                                        </div>
                                    </div>
                                </td>
                                <td><span>{new Date(item.updated_at + 'Z').toDateString()}</span></td>
                                <td><span>{new Date(item.created_at + 'Z').toDateString()}</span></td>
                            </tr>
                        )
                    )
                }
            </>
        );
    };

    return (
        <div>
            <table className="min-w-full text-left border-spacing-y-2.5 border-separate">
                <thead className=" bg-gray-50 text-sm">
                    <tr>
                        <th>
                            <span>Order ID</span>
                        </th>
                        <th>
                            <DomainLabel />
                        </th>
                        <th>
                            <PriceLabel />
                        </th>
                        <th className="w-1/5">
                            <span>Transfer Status</span>
                        </th>
                        <th className="w-1/5">
                            <StatusUpdatedLabel />
                        </th>
                        <th className="w-1/6">
                            <CreatedLabel />
                        </th>
                    </tr>
                </thead>
                <tbody className="text-sm">
                    <ProductList />
                </tbody>
            </table>
        </div>
    );
}