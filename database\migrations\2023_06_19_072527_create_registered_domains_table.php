<?php

use App\Models\Domain;
use App\Models\Extension;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('registered_domains', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_contact_registrar_id');
            $table->foreign('user_contact_registrar_id')->references('id')->on('user_contacts');
            $table->foreignIdFor(Extension::class);
            $table->foreignIdFor(Domain::class);
            $table->string('status');
            $table->bigInteger('locked_until')->nullable();
            $table->unsignedBigInteger('user_category_id');
            $table->foreign('user_category_id')->references('id')->on('user_categories');
            $table->text('contacts_id')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('registered_domains');
    }
};
