<?php

namespace App\Modules\AccountCredit\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Payment\Constants\CheckoutType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\StripeTransaction\Services\StripeTransactionService;
use App\Rules\Payment\ValidateOtherFees;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class StoreStripeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'other_fees' => ['required', 'array', 'min:1', new ValidateOtherFees(CheckoutType::ACCOUNT_BALANCE)],
            'intent' => ['required', 'string', 'max:300'],
        ];
    }

    public function passedValidation()
    {
        $this->captureIntent();
    }

    protected function failedValidation(Validator $validator)
    {
        $this->cancelIntent();
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    public function store()
    {
        StripeTransactionService::instance()->storeToAccountCredit($this->all());
    }

    private function cancelIntent()
    {
        if ($this->input('intent')) {
            PaymentIntentProvider::instance()->cancelIntent($this->input('intent'));
        }
    }

    private function captureIntent(): void
    {
        if ($this->input('intent')) {
            PaymentIntentProvider::instance()->captureIntent($this->input('intent'));
        }
    }
}
