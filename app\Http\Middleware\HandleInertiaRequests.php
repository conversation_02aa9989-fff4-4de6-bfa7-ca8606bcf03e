<?php

namespace App\Http\Middleware;

use App\Modules\Stripe\Services\IdentityService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Inertia\Middleware;
use Tightenco\Ziggy\Ziggy;
use Illuminate\Support\Facades\Config;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $user             = $request->user();

        return array_merge(
            parent::share($request), 
            [
                'flash' => 
                [
                    'message' => fn() => $request->session()->get('flash.message'),
                ],
                'auth' => 
                [
                    'user' => Collection::make($user)->merge(
                        [
                            'hasPinCode'     => $user ? $user->hasPinCode() : false,       // Include the result of the method
                            'hasApiKeyBodis' => $user ? $user->hasApiKeyBodis() : false,   // Include the result of the method
                        ]
                    ),
                ],
                // 'subdomains' => Config::get('subdomains'),
                'is_verified' => IdentityService::instance()->redirectToStatus(),
            ]
        );
    }
}
