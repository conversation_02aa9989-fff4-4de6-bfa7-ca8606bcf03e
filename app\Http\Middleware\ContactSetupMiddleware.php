<?php

namespace App\Http\Middleware;

use Illuminate\Support\Facades\Auth; 

use Closure;

class ContactSetupMiddleware extends Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    // public function handle(Request $request, Closure $next): Response
    // {
    //     return $next($request);
    // }

    public function handle($request, Closure $next, ...$guards)
    {
        if (is_null(Auth::user()->contact_setup)) 
        {
            return redirect()->route('user-account-setup.contact');
        }

        if (Auth::user()->contact_setup == false) 
        {
            return redirect()->route('user-account-setup.contact.finish');
        }

        return $next($request);
    }
}
