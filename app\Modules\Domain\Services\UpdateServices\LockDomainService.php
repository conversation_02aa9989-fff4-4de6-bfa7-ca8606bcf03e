<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\DomainHistoryEvent;
use App\Events\UpdateDomainsTableEvent;
use App\Models\User;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Stripe\Services\StripeLimiter;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\Domain\DomainLockHelper;
use App\Util\Helper\Domain\DomainParser;
use App\Util\Helper\RateLimit;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class LockDomainService
{
    use UserLoggerTrait;

    private $isJob = true;

    private $userId;

    private $userEmail;

    private $maxAttemptError = 3;

    private $dispatchTenSeconds = 10;

    public static function instance(): self
    {
        $lockDomainService = new self;

        return $lockDomainService;
    }

    public function getLockConfirmData(array $request)
    {
        StripeLimiter::instance()->checkAttempt();

        $domains = $request['domains'];
        $domains_list = $request['domains_list'];
        $isDisable = $request['isDisable'];
        $domain_lock_count = DomainLockHelper::countLockedDomains($domains);
        $domain_unlock_count = DomainLockHelper::countLockedDomains($domains, false);
        $domain_lockedin_count = DomainLockHelper::countLockedInDomains($domains);

        return [
            'domains' => $domains,
            'domains_list' => $domains_list,
            'lock_count' => $domain_lock_count['count'],
            'unlock_count' => $domain_unlock_count['count'],
            'lockedin_count' => $domain_lockedin_count['count'],
            'isDisable' => $isDisable,
        ];
    }

    public function authenticateUser(string $password): void
    {
        $canTry = RateLimit::attempt(RateLimiterKey::authLockAttempt(auth()->user()->id));

        if (! $canTry) {
            throw ValidationException::withMessages(['password' => 'Maximum attempt, please try again after a few minutes.']);
        }

        if (! Hash::check($password, auth()->user()->getAuthPassword())) {
            throw ValidationException::withMessages(['password' => 'Invalid user password, please try again.']);
        }

        RateLimit::clear(RateLimiterKey::authLockAttempt(auth()->user()->id));
    }

    public function getOwner(string $email): int
    {
        $receiver = User::where('email', $email)->first();

        if (is_null($receiver)) {
            throw ValidationException::withMessages(['email' => $email.' does not exist.']);
        }

        if (strcmp($email, auth()->user()->email) != 0) {
            throw ValidationException::withMessages(['email' => 'Email does not correspond to domain owner.']);
        }

        return $receiver->only('id')['id'];
    }

    public function setLockData(array $request)
    {
        $domains = $this->countLockedInDomains($request);

        if (empty($domains)) {
            return 'No domains to process.';
        }

        $updatedDomains = $this->getUpdatedDomainList($domains, $request['isDisable']);

        if (empty($updatedDomains)) {
            return 'No domains to process.';
        }

        $this->setDomainsToInProcess($updatedDomains);
        $this->dispatchJobs($updatedDomains, $request['isDisable']);

        return $this->getMessageNotification($updatedDomains, $request['isDisable']);
    }

    // PRIVATE FUNCTIONS

    private function countLockedInDomains(array $request): array
    {
        $domains = $request['domains'];

        if (! $request['isDisable']) {
            $response = DomainLockHelper::countLockedInDomains($domains);
            $domains = $response['toUnlock'];
        }

        return $domains;
    }

    private function getUpdatedDomainList(array $domains, bool $isDisable): array
    {
        $updatedDomains = [];

        foreach ($domains as $domain) {
            $status = json_decode($domain['client_status']);
            if (in_array(EppDomainStatus::CLIENT_TRANSFER_PROHIBITED, $status) != $isDisable) {
                $updatedDomains[] = $domain;
            }
        }

        return $updatedDomains;
    }

    private function setDomainsToInProcess(array $updatedDomains)
    {
        $updatedObj = collect($updatedDomains);
        $domainUpdateIds = $updatedObj->pluck('id')->toArray();
        DomainService::instance()->updateDomainStatus($domainUpdateIds, DomainStatus::IN_PROCESS);
        UpdateDomainsTableEvent::dispatch($this->getUserId());
    }

    private function dispatchJobs(array $updatedDomains, bool $isDisable): void
    {
        $count = 0;
        foreach ($updatedDomains as $domain) {
            $payload = $this->createPayload($domain, $isDisable);
            JobDispatchService::instance()->updateEppDispatch(
                $payload,
                $this->dispatchTenSeconds + $count,
            );
            $count += 1;
        }
    }

    private function createDomain(array $domain, bool $isDisable)
    {
        $status = json_decode($domain['client_status'], true);
        if (in_array(EppDomainStatus::CLIENT_TRANSFER_PROHIBITED, $status)) {
            if (! $isDisable) { // unlock
                unset($status[array_search(EppDomainStatus::CLIENT_TRANSFER_PROHIBITED, $status)]);

                event(new DomainHistoryEvent([
                    'domain_id' => $domain['id'],
                    'type' => 'DOMAIN_UNLOCKED',
                    'user_id' => $this->getUserId(),
                    'status' => 'success',
                    'message' => 'Domain "'.$domain['name'].'" unlocked by '.$this->getUserEmail().'. Transfer lock protection removed.',
                    'payload' => $domain,
                ]));
            }
        } else {
            if ($isDisable) { // lock
                $status = EppDomainStatus::CLIENT_LOCK_STATUS;

                event(new DomainHistoryEvent([
                    'domain_id' => $domain['id'],
                    'type' => 'DOMAIN_LOCKED',
                    'user_id' => $this->getUserId(),
                    'status' => 'success',
                    'message' => 'Domain "'.$domain['name'].'" locked by '.$this->getUserEmail().'. Transfer lock protection enabled.',
                    'payload' => $domain,
                ]));
            }
        }

        $payload = [
            'id' => $domain['id'],
            'name' => $domain['name'],
            'registrant' => $domain['registrant'],
            'client_status' => $status,
        ];

        return $payload;
    }

    private function createRegisteredDomain(array $domain)
    {
        $payload = [
            'id' => $domain['registered_domain_id'],
            'name' => $domain['name'],
        ];

        return $payload;
    }

    private function createPayload(array $domain, bool $isDisable): array
    {
        $domainPayload = $this->createDomain($domain, $isDisable);
        $registeredDomain = $this->createRegisteredDomain($domain);
        $registry = DomainParser::getRegistryName($domain['name']);

        return [
            JobPayloadKeys::DOMAIN => $domainPayload,
            JobPayloadKeys::REGISTERED_DOMAIN => $registeredDomain,
            JobPayloadKeys::REGISTRY => $registry,
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_RESTRICTIONS,
        ];
    }

    private function getUserId(): int
    {
        return auth()->user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        return auth()->user()->email ?? 'Unauthorized';
    }

    private function getMessageNotification(array $updatedDomains, bool $isDisable): string
    {
        $textDomain = (count($updatedDomains) == 1) ? 'domain' : 'domains';

        if ($isDisable) {
            return 'Locking '.$textDomain.' is in process.';
        }

        return 'Unlocking '.$textDomain.' is in process.';
    }
}
