<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth; 

use Closure;

use Symfony\Component\HttpFoundation\Response;

class AccountCreditInitialSetupMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        //return $next($request);

        if (Auth::user()->account_credit_setup == true)
        {
            return $next($request);
        }
        else 
        {
            return redirect()->route('user-account-setup.account-credit');
        }
    }
}
