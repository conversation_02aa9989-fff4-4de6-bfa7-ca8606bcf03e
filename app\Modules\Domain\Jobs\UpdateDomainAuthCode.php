<?php

namespace App\Modules\Domain\Jobs;

use App\Events\SystemLogEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Histories\Constants\SystemTransactionType;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\Domain\DomainParser;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Throwable;

class UpdateDomainAuthCode implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $id;

    private $payload;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct($domain_id, $payload)
    {
        $this->id = $domain_id;
        $this->payload = $payload;
        $registry = DomainParser::getRegistryName($payload['name']);

        $this->onConnection(QueueConnection::DOMAIN_AUTHCODE_UPDATE);
        $this->onQueue(QueueTypes::DOMAIN_AUTHCODE_UPDATE[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return $this->id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            $this->fail();
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('Domain auth update start...');
        $response = EppDomainService::instance()->updateEppDomain($this->payload, 'Cron:');

        if (array_key_exists('errors', $response)) {
            app(AuthLogger::class)->info('Domain auth update failed...');

            return;
        }

        DB::table('domains')->where('id', $this->id)->update([
            'auth_code_updated_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        app(AuthLogger::class)->info('Domain auth update end...');

        event(new SystemLogEvent(
            SystemTransactionType::DOMAIN_AUTH_REGENERATOR,
            "System updated auth code for domain '{$this->payload['name']}'",
            null
        ));
    }

    public function failed(Throwable $exception): void
    {
        // Send user notification of failure, etc...
    }
}
