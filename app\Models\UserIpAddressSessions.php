<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserIpAddressSessions extends Model
{
    /**
     * Manually Define Table Name.  
     * 
     * @var string 
     */
    protected $table = 'user_ip_address_sessions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable =
    [
        'user_id',
        'ip_id',
        'user_agent',
    ];

    /**
     * Define attributes that should be hidden for arrays. 
     * 
     * @var array 
     */
    protected $hidden =
    [
        //...
    ];

    /**
     * Cast Type 
     * 
     * @var array 
     */
    protected $casts =
    [
        //
    ];
}
