<?php

namespace App\Modules\Auth\Controllers;

use App\Events\ClientActivityEvent;
use App\Events\UserLoginEvent;
use App\Http\Controllers\Controller;
use App\Modules\Auth\Requests\LoginRequest;
use App\Modules\Auth\Services\AuthenticatedSessionService;
use App\Modules\Auth\Services\TwoFactorAuthenticationService;
use App\Modules\Auth\Services\UserIpAddressSessionService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\Client\ClientIp;
use App\Util\Helper\RateLimit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatedSessionController extends Controller
{
    /* TRAITS */
    use UserLoggerTrait;

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except(['destroy']);
    }

    /**
     * Display the login view.
     */
    public function create(Request $request): Response
    {
        $hasLoggedOut = $request->session()->get('hasLoggedOut') !== null ? true : false;

        return Inertia::render('Auth/Login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
            'hasLoggedOut' => $hasLoggedOut,
        ]);
    }

    /**`
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request)
    {
        $email = $request->input('email');
        $clientIp = ClientIp::getClientIp($request);

        $emailLimitKey = 'loginAttempts'.$email;
        $canTry = RateLimit::attempt(RateLimiterKey::loginAttemptEmail($email), 5);

        if (! $canTry) {
            if ((new AuthenticatedSessionService)->checkIfUserEmailExist($email)) {
                if (! Cache::has($emailLimitKey)) {
                    (new AuthenticatedSessionService)->sendLoginAttemptsExceededMail($email);

                    Cache::put($emailLimitKey, true, now()->addMinutes(5));  // Set cache expiry (e.g., 10 minutes)
                }
            }

            throw ValidationException::withMessages(['message' => 'Maximum attempts reached, try again after a minute.']);
        }

        $user = (new AuthenticatedSessionService)->getUserByEmailAndPassword($request->only('email', 'password'));

        if ($user == null) {
            throw ValidationException::withMessages(
                [
                    'message' => trans('auth.failed'),
                ]
            );
        }

        if ($user->is_active == false) {
            throw ValidationException::withMessages(
                [
                    'message' => 'account is blocked, please contact admin',
                ]
            );
        }

        if (
            (new TwoFactorAuthenticationService)->checkIfUserTwoFactorAuthenticationIsEnabled($user) == true
            && (new TwoFactorAuthenticationService)->checkIfUserIsHasAnActiveTwoFactorAuthenticatedSession($user, ClientIp::getClientIp($request)) == false
        ) {
            $url = URL::temporarySignedRoute(
                'login.2fa',
                now()->addMinutes(15),
                [
                    'user' => $user->id,
                    'rememberMe' => $request->has('remember') ? true : false,
                ]
            );

            return Inertia::location(
                $url
            );
        } else {
            if (
                ! Auth::attempt(
                    $request->only('email', 'password'),
                    $request->boolean('remember'))
            ) {
                app(AuthLogger::class)->info($request->email.' failed attempt.');

                throw ValidationException::withMessages(
                    [
                        'message' => trans('auth.failed'),
                    ]
                );
            } else {
                app(AuthLogger::class)->info($this->fromWho('successful login at '.$clientIp));

                RateLimit::clear(RateLimiterKey::loginAttemptEmail($email));

                (new UserIpAddressSessionService)->recordEntry($user->id, $clientIp);

                event(new UserLoginEvent(Auth::id(), Auth::user()->email));

                $payload = collect($request->all())->except(['password'])->toArray();

                event(new ClientActivityEvent(Auth::user()->id, UserTransactionType::SIGN_IN, 'Successfully signed in from '.$clientIp, '', $payload));

                return Inertia::location(redirect()->intended(route('domain')));
            }
        }
    }

    /**
     * Create data array for sign-in activity event
     */
    private function createSignInActivityData($user, $clientIp)
    {
        return [
            'ip_address' => $clientIp,
            'user_id' => $user->id,
            'email' => $user->email,
        ];
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request)
    {
        $clientIp = ClientIp::getClientIp($request);

        app(AuthLogger::class)->info($this->fromWho('logging out at '.$clientIp));

        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Inertia::location(route('login'));
    }
}
