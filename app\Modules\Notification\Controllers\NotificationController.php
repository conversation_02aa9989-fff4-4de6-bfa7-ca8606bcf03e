<?php

namespace App\Modules\Notification\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Notification\Requests\UpdateNotificationReadRequest;
use App\Modules\Notification\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class NotificationController extends Controller
{
    public function index()
    {
        return Inertia::render('Notification/Index', NotificationService::instance()->getAll());
    }

    public function getUnreadCount(Request $request)
    {
        return response(NotificationService::instance()->getUnreadCount($request->id), 200);
    }

    public function getDropdownData(Request $request)
    {
        // return response(NotificationService::instance()->get_dropdown($request->id), 200);
        return response(NotificationService::instance()->getAll(), 200);
    }

    public function getDropdownDatas(Request $request)
    {
        return Inertia::render('Notification', NotificationService::instance()->getAll());
    }

    public function update(UpdateNotificationReadRequest $request)
    {
        $request->read();

        return back();
    }

    public function markAllAsRead(Request $request)
    {
        $userId = $request->input('user_id', Auth::user()->id);
        $type = $request->input('type', 'all');
        
        return response()->json(
            NotificationService::instance()->markAllByType($userId, $type)
        );
    }

    public function markAllAnnouncementsAsRead(Request $request)
    {
        $userId = $request->input('user_id', Auth::user()->id);

        return response()->json([
            'success' => true,
            'result' => NotificationService::instance()->markAllAnnouncementsAsRead($userId)
        ]);
    }

    public function markSelectedAsRead(Request $request)
    {
        $ids = $request->input('ids', []);
        $result = NotificationService::instance()->markSelectedAsRead($ids);

        return response()->json([
            'success' => true,
            'result' => $result,
        ]);
    }
}
