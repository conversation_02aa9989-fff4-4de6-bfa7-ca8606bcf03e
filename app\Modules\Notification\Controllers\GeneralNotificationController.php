<?php

namespace App\Modules\Notification\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Notification\Requests\GetUserNotificationsRequest;
use App\Modules\Notification\Requests\MarkNotificationAsReadRequest;
use App\Modules\Notification\Services\GeneralNotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GeneralNotificationController extends Controller
{
    public function __construct(
        private readonly GeneralNotificationService $notificationService
    ) {}

    /**
     * Get paginated notifications for the authenticated user
     */
    public function getUserNotifications(Request $request): JsonResponse
    {
        $result = $this->notificationService->getNotifications(
            auth()->id(),
            [],
            true,
            $request->query('page', 1),
            10
        );

        return response()->json($result);
    }

    /**
     * Mark a notification as read
     */
    public function markAsRead(MarkNotificationAsReadRequest $request)
    {
        $notificationId = $request->validated('id');

        // Update the notification
        $this->notificationService->markAsRead($notificationId);

        // Get the new unread count
        $unreadCount = $this->notificationService->getUnreadCount(auth()->id());

        return response()->json([
            'success' => true,
            'unreadCount' => $unreadCount,
        ]);
    }

    /**
     * Get unread notification count
     */
    public function getAnnouncementUnreadCount(GetUserNotificationsRequest $request): JsonResponse
    {
        $count = $this->notificationService->getUnreadCount(
            $request->validated('id')
        );

        return response()->json($count);
    }

    /**
     * Mark all announcements as read for a user
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $userId = $request->input('user_id', auth()->id());
        $unreadIds = $this->notificationService->getUnreadNotificationIds($userId);
        $this->notificationService->markAllAsRead($userId);

        $newUnreadCount = $this->notificationService->getUnreadCount($userId);

        return response()->json([
            'success' => true,
            'unreadCount' => $newUnreadCount,
            'updatedIds' => $unreadIds,
        ]);
    }

    /**
     * Mark selected announcements as read
     */
    public function markSelectedAsRead(Request $request): JsonResponse
    {
        $ids = $request->input('ids', []);

        $this->notificationService->markSelectedAsRead($ids);
        $unreadCount = $this->notificationService->getUnreadCount(auth()->id());

        return response()->json([
            'success' => true,
            'unreadCount' => $unreadCount,
        ]);
    }
}
