<?php

namespace App\Modules\Domain\Requests\Nameserver;

use App\Modules\Domain\Services\UpdateServices\UpdateNameserverService;
use Illuminate\Foundation\Http\FormRequest;

class NameserverUpdateEppRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'domains' => ['required', 'array', 'min:1'],
            'nameservers' => ['required', 'array', 'min:1'],
        ];
    }

    public function messages(): array
    {
        return [];
    }

    public function update()
    {
        UpdateNameserverService::instance()->updateNameservers($this->all());
    }
}
