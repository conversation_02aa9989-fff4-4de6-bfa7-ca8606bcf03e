<?php

namespace App\Modules\Push\Requests;

use App\Modules\Push\Constants\Response;
use App\Modules\Push\Services\PushDomainService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PushDomainActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'action' => ['required', 'string', 'max:50', Rule::in(Response::VALID_ACTIONS)],
            'push_ids' => ['required', 'array'],
            'push_ids.*' => ['integer', 'exists:push_domains,id'],
            'push_type' => ['required', 'string', 'max:30', Rule::in(Response::PUSH_TYPE)],
        ];
    }

    public function pushData()
    {
        $domains = PushDomainService::instance()->getDomainNames($this->push_ids);

        return [
            'push_ids' => $this->push_ids,
            'domains' => $domains,
            'push_type' => $this->push_type,
            'action' => $this->action,
        ];
    }
}
