<?php

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use App\Models\UserContact;
use Illuminate\Support\Facades\Validator;

function testInvalidContactData(FormRequest $request, array $invalidContactData)
{
    foreach (array_keys($invalidContactData) as $dataKey) {
        it("fails validation when field has invalid data with dataset: " . $dataKey, function () use ($request, $invalidContactData, $dataKey) {
            if (isset($invalidContactData[$dataKey]['data']) && isset($invalidContactData[$dataKey]['assertion'])) {
                $data = $invalidContactData[$dataKey];
                $testData = $data['data'];
                $assertion = $data['assertion'];

                $request->registry = array_key_exists('registry', $testData) ? $testData['registry'] : "invalid";

                $validator = Validator::make($testData, $request->rules());

                foreach (array_keys((array) $testData) as $key) {
                    $errorMessage = $validator->errors()->first($key);
                    expect(str_contains($errorMessage, $assertion))->toBeTrue("Failed to meet assertion for field: " . $key . " on Dataset: " . $dataKey);
                }

                expect($validator->fails())->toBeTrue();
            } else {
                throw new Exception('Test data for dataset: ' . $dataKey . " is incomplete. It must have data and assertion key.");
            }
        });
    }
}

function createTestContact(array $extraData)
{
    $data = [
        'name' => 'test name',
        'organization_name' => 'test org',
        'registry_contact' => 'test contact',
        'voice_number' => '123',
        'email' => "<EMAIL>",
        'unit' => 'test',
        'street' => "test_street",
        'city' => 'test_city',
        'state_province' => 'test_province',
        'postal_code' => '6539',
        'country_code' => "PH",
        'ext_voice_number' => '321'
    ];
    foreach (array_keys($extraData) as $key) {
        $data[$key] = $extraData[$key];
    }

    DB::table('contacts')->insert($data);
    $contact = DB::table('contacts')
        ->where('name', '=', $data['name'])
        ->where('organization_name', '=', $data['organization_name'])
        ->where('registry_contact', '=', $data['registry_contact'])
        ->first();
    return $contact;
}

function createTestRegistry(string $registryName)
{
    $data = [
        'name' => $registryName
    ];

    $registryData = DB::table('registries')->where('name', '=', $registryName)->first();
    if (!$registryData) {
        $data['id'] = DB::table('registries')->count() + 1;
        DB::table('registries')->insert($data);
    }

    $registry = DB::table('registries')->where('name', '=', $registryName)->first();
    return $registry;
}

function createTestUserContact(int $userId, int $contactId, int $registryId, array $extraData)
{
    $data = [
        'user_id' => $userId,
        'contact_id' => $contactId,
        'registry_id' => $registryId,
        'default_registrar_contact' => True,
        'default_administrative_contact' => True,
        'default_technical_contact' => True,
        'default_billing_contact' => True
    ];
    foreach (array_keys($extraData) as $key) {
        $data[$key] = $extraData[$key];
    }
    return UserContact::factory()->create($data);
}
