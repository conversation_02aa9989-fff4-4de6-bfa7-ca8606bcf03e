<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Poll extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'server_id',
        'type',
        'name',
        'status',
        'message',
        'acknowledge',
        'body',
        'json_data',

    ];

    public function tld()
    {
        return $this->belongsTo(Tld::class, 'tld_id');
    }
}
