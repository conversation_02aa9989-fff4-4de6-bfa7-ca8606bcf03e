<?php

namespace App\Modules\AdminBackdoor\Requests;

use App\Modules\AdminBackdoor\Services\FailedQueue;
use Illuminate\Foundation\Http\FormRequest;

class FailedQueueForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'option' => ['required', 'string', 'regex:/^[A-Za-z0-9 -_]+$/'],
        ];
    }

    public function retryQueue()
    {
        FailedQueue::retry($this->option);
    }

    public function forgetQueue()
    {
        FailedQueue::flush();
    }
}
