<?php

namespace App\Modules\JobRetry\Constants;

use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\Domain\Constants\DomainStatus;

final class RetryJobStatus
{
    public const PENDING = 'PENDING';

    public const IN_PROCESS = 'JOB_IN_PROCESS';

    public const MAX_ATTEMPT = 'RETRY_MAX_ATTEMPT';

    public const STOP_RETRY_STATUS = [
        DomainStatus::ACTIVE,
        ContactStatus::REGISTERED,
        DomainStatus::NOT_AVAILABLE,
        DomainStatus::MAX_REGISTRATION_ERROR,
        DomainStatus::PARAMETER_POLICY_ERROR,
        RetryJobStatus::MAX_ATTEMPT,
    ];

    public const GET_RETRY_STATUS = [
        self::PENDING,
        DomainStatus::PENDING,
        ContactStatus::IN_PROCESS,
    ];
}
