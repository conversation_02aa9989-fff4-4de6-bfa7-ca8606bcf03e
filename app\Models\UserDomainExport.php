<?php

namespace App\Models;

use App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo; 

class UserDomainExport extends Model
{
    //** TRAITS */
    use HasFactory; 

    /**
     * Manually Define Table Name.  
     * 
     * @var string 
     */
    protected $table = 'user_domain_exports';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable =
    [
        'generated_by_user_id',
        'name',
        'path',
        'rows',
        'size',
        'last_downloaded_at'
    ];

    /**
     * Define attributes that should be hidden for arrays. 
     * 
     * @var array 
     */
    protected $hidden =
    [
        //...
    ];

    /**
     * Cast Type 
     * 
     * @var array 
     */
    protected $casts =
    [
        //
    ];

    //** Accessors & Mutators */

    //...

    //** belongsTo, belongsToMany, hasOne, hasMany relationships */ 

    /**
     * Fetch gemeratedByUser belongsTo relationship. 
     * 
     * @return BelongsTo
     */
    public function generatedByUser(): BelongsTo
    {
        return $this->belongsTo(
            related: User::class,
            foreignKey: "generated_by_user_id",
            ownerKey: "id",
        );
    }
}
