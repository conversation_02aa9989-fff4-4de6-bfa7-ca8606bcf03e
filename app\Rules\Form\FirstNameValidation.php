<?php

namespace App\Rules\Form;

use Illuminate\Contracts\Validation\Rule;

class FirstNameValidation implements Rule
{
    public function passes($attribute, $value): bool
    {
        return is_string($value)
            && mb_strlen($value) >= 2
            && mb_strlen($value) <= 50
            && preg_match('/^[a-zA-Z\s\'-]+$/u', $value);
    }

    public function message(): string
    {
        return 'The :attribute must be a string between 2 and 50 characters and may only contain letters, spaces, hyphens, and apostrophes.';
    }
}