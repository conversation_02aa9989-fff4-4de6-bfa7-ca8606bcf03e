<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EmailSent
{
    use Dispatchable, SerializesModels;

    public $userId;
    public $name;
    public $recipient_email;
    public $subject;
    public $email_body;
    public $email_type;
    public $attachment;
    

    public function __construct($userId, $name, $recipient_email, $subject, $email_type, $email_body, $attachment)
    {
        $this->userId = $userId;
        $this->name = $name;
        $this->recipient_email = $recipient_email;
        $this->subject = $subject;
        $this->email_body = $email_body;
        $this->email_type = $email_type;
        $this->attachment = $attachment;
    }
}
