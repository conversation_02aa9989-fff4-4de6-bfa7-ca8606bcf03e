<?php

namespace App\Rules\Cart;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CartNotAvailable implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->isInCart($value)) {
            $fail($this->message($value));
            return;
        }
    }

    public function passes($attribute, $value)
    {
        return !$this->isInCart($value);
    }

    public function message($value)
    {
        return $value.' is already in the cart.';
    }

    // PRIVATE FUNCTIONS

    private function isInCart(string $value): bool
    {
        return DB::table('carts')
            ->where('user_id', Auth::user()->id)
            ->where('name', $value)
            ->whereNull('deleted_at')
            ->exists();
    }
}
