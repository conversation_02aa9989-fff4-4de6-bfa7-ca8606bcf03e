<?php

namespace App\Modules\Client\Jobs;

use App\Events\EmailSent;
use App\Mail\Constants\Links;
use App\Mail\Constants\MailConstant;
use App\Mail\DomainExpiryNotification;
use App\Modules\Domain\Services\ExpirationNoticeService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class DomainExpirySender implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $userId;

    private $notice;

    public $failOnTimeout = true;

    public $uniqueFor = 900; // 15 minutes

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3000; // 50 minutes

    /**
     * Create a new job instance.
     */
    public function __construct($notice)
    {
        $this->notice = $notice;
        $this->userId = $notice->user_id;

        $this->onConnection(QueueConnection::DOMAIN_SCHEDULE_EXPIRY);
        $this->onQueue(QueueTypes::EXPIRY_SEND);
    }

    public function uniqueId(): int
    {
        return $this->userId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $fileInfo = ExpirationNoticeService::createCSVFile($this->notice);
        $payload = $this->sendNotification($fileInfo);
        $this->trackEmailSent($payload, $fileInfo['path']);
    }

    private function sendNotification(array $fileInfo)
    {
        $isExpired = Carbon::parse($this->notice->lead_expiry_date)->timestamp < now()->timestamp ? true : false;

        $payload = [
            'sender_address' => config('mail.from.address'),
            'sender_name' => config('mail.from.sd_name'),
            'greeting' => 'Dear '.$this->notice->first_name.',',
            'domains' => json_decode($this->notice->record),
            'url' => config('app.url').Links::DOMAIN_EXPIRY_LIST,
            'expired' => $isExpired,
            'type' => $this->notice->type,
            'attachment' => $fileInfo['path'],
            'filename' => $fileInfo['name'],
            'notice_id' => $this->notice->id,
        ];

        $queueMessage = (new DomainExpiryNotification($payload))
            ->onConnection(QueueConnection::MAIL_JOB)
            ->onQueue(MailConstant::DOMAIN_EXPIRY_NOTICE);

        Mail::to($this->notice->email)->send($queueMessage);

        return $payload;
    }

    private function trackEmailSent(array $payload, string $filePath)
    {
        $payloadString = json_encode($payload);
        $csvContent = file_get_contents($filePath);
        $name = $this->notice->first_name.' '.$this->notice->last_name;

        event(new EmailSent(
            $this->notice->user_id,
            $name,
            $this->notice->email,
            'Urgent: Expiration Notice from '.config('app.name'),
            ucwords(strtolower($this->notice->type)).' Expiration Notice',
            $payloadString,
            $csvContent
        ));
    }
}
