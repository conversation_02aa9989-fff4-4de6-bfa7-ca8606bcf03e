<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Services\MarketCartService;
use Illuminate\Foundation\Http\FormRequest;

class CartRemoveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|exists:market_carts,id',
        ];
    }

    public function remove()
    {
        return MarketCartService::instance()->removeFromCart($this->id);
    }
}
