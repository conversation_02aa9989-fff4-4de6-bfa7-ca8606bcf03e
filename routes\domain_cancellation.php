<?php

use App\Modules\DomainCancellationRequest\Controllers\CancellationController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(
    [
        'auth',
        'auth.active',
        'account.setup'
    ]
)
    ->prefix('domain')
    ->group(
        function () {
            Route::post('/cancel', [CancellationController::class, 'cancellationRequest'])->name('domain.cancel');
            Route::get('/cancellationSuccess', function () {
                return Inertia::render('DomainCancellation/DomainCancellationSuccess', [
                    'domain' => request('domain'),
                ]);
            })->name('domain.cancellation.success');
        }
    );
