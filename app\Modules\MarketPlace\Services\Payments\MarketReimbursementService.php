<?php

namespace App\Modules\MarketPlace\Services\Payments;

use App\Events\ClientActivityEvent;
use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Models\MarketPlaceReimbursement;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\MarketPlace\Constants\MarketPaymentStatus;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Traits\UserContact;
use Exception;

class MarketReimbursementService
{
    use UserContact, UserLoggerTrait;

    public static function instance()
    {
        $marketReimbursementService = new self;

        return $marketReimbursementService;
    }

    public function createRefundSummary($invoiceData, $domain): void
    {
        $refundDetails = $this->createRefundDetails(
            PaymentSummaryType::MARKETPLACE_REIMBURSEMENT,
            PaymentServiceType::STRIPE,
            $invoiceData->marketplace_payment_invoice_id,
        );

        $registeredDomainId = $domain->registered_domain_id;
        $userId = $domain->user_id ?? null;
        $refundDetails['description'] = RegistryTransactionType::MARKETPLACE_TRANSFER.' - '.MarketPaymentStatus::REFUNDED;
        try {
            PaymentSummaryService::instance()->createRefund($refundDetails, $registeredDomainId, $userId);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('error in execute refund: '.$e->getMessage());
        }
    }

    public function store(array $data, int $userId, string $paymentServiceType)
    {
        $invoice = $data['invoice_data'];
        $createdPaymentService = PaymentServiceHelper::instance()->refund($data, $userId, $paymentServiceType);
        $reimbursement = [
            'status' => MarketPaymentStatus::REQUESTED,
            'total_amount' => $invoice->refund_gross_amount ?? 0,
            'payment_service_id' => $createdPaymentService['id'],
            'marketplace_node_invoice_id' => $invoice->market_place_node_invoice_id,
        ];

        $createdReimbursement = MarketPlaceReimbursement::create($reimbursement);

        $this->createMarketPlaceReimbursementSummary($invoice, $createdReimbursement, $userId, $paymentServiceType);
        $this->createClientActivityEvent($invoice, $userId, $paymentServiceType, $createdReimbursement);

        return $createdReimbursement;
    }

    public function createRefundDetails(string $paymentSummaryType, string $paymentServiceType, int $invoiceId)
    {
        return [
            'payment_summary_type' => $paymentSummaryType,
            'payment_service_type' => $paymentServiceType,
            'invoice_id' => $invoiceId ?? 0,
        ];
    }

    private function createMarketPlaceReimbursementSummary(object $invoice, object $reimbursement, int $userId, string $paymentServiceType)
    {
        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_REIMBURSEMENT].' - '.$invoice->domain_name ?? '',
            'paid_amount' => $invoice->total_amount ?? 0,
            'total_amount' => $invoice->refund_gross_amount ?? 0,
            'payment_market_place_invoice_id' => $reimbursement->id,
            'source' => $paymentServiceType,
        ];

        event(new CreatePaymentSummaryEvent($data, $userId, PaymentSummaryType::MARKETPLACE_REIMBURSEMENT));
    }

    private function createClientActivityEvent(object $invoice, int $userId, string $paymentServiceType, object $reimbursement)
    {
        $refundAmount = $invoice->refund_gross_amount ?? 0;
        $message = 'Refunded $'.$refundAmount.' for '.PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_REIMBURSEMENT].' - '.$invoice->domain_name;
        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_REIMBURSEMENT].' - '.$invoice->domain_name ?? '',
            'paid_amount' => $invoice->total_amount ?? 0,
            'total_amount' => $refundAmount,
            'payment_market_place_invoice_id' => $reimbursement->id,
            'source' => $paymentServiceType,
        ];
        event(new ClientActivityEvent($userId, UserTransactionType::PAYMENT_SUMMARY, $message, '', $data));
    }
}
