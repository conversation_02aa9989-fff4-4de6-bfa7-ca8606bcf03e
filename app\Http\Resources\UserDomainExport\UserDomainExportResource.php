<?php

namespace App\Http\Resources\UserDomainExport;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserDomainExportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'               => $this->id,
            'name'             => $this->name,
            'path'             => $this->path,
            'rows'             => $this->rows,
            'size'             => $this->size,
            'lastDownloadedAt' => $this->last_downloaded_at,
            'createdAt'        => $this->created_at->format('Y-m-d H:i:s'),
            'updatedAt'        => $this->updated_at->format('Y-m-d H:i:s'),

            //! RELATIOHSHIPS 
            //"generatedByUser"   => UserResource::make($this->whenLoaded('generatedByUser'))
        ];
    }
}
