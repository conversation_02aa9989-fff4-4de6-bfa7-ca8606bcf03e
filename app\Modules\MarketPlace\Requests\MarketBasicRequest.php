<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Constants\MarketConstants;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Http;

class MarketBasicRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'domain' => 'required|string',
        ];
    }

    public function doBasicSearch()
    {
        return Http::basic()->get("basic_search?domain=$this->domain");
    }
}
