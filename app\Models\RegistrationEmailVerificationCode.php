<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash; 

class RegistrationEmailVerificationCode extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable =
    [
        'email',
        'token',
        'code',
        'valid_until',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden =
    [
        //?
    ];

    //** Accessors & Mutators */

    /**
     * Interact with the code column.
     */
    protected function code(): Attribute
    {
        return Attribute::make(
            set: fn(mixed $value) => Hash::make($value)
        );
    }

    //** belongsTo, belongsToMany, hasOne, hasMany relationships */ 
    //...
}
