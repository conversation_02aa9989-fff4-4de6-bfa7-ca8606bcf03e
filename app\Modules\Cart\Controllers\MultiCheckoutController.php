<?php

namespace App\Modules\Cart\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Cart\Constants\DomainThreshold;
use App\Modules\Cart\Requests\MultiCheckoutRequest;
use App\Modules\Cart\Services\MultiCheckout\MultiCartService;
use App\Modules\Setting\Constants\FeeType;
use Inertia\Inertia;

class MultiCheckoutController extends Controller
{
    public function getCheckoutSummary()
    {
        $threshold = MultiCartService::instance()->getDomainThreshold();
        $DomainThresholdCount = MultiCartService::instance()->getRegistrationThresholdData();

        $data = ($DomainThresholdCount >= $threshold->threshold_limit)
            ? MultiCartService::instance()->displayThresholdNotif()
            : MultiCartService::instance()->getMultiCartSummary();

        return Inertia::render('Domain/MultiCheckout', $data);
    }

    public function store(MultiCheckoutRequest $request)
    {
        $summaryId = $request->store();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment successful. Domain acquisition is in process.',
            'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
        ]);
    }
}
