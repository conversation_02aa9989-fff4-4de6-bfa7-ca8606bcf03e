import React from "react";

export default function NotFoundPage({ code, error, help }) {

    return (
        <section className="flex items-center h-screen p-16 ">
            <div className="container flex flex-col items-center justify-center px-5 mx-auto my-8">
                <div className="max-w-md text-center">
                    <h2 className="mb-8 font-extrabold text-9xl text-primary">
                        {code}
                    </h2>
                    <p className="text-2xl font-semibold md:text-3xl text-gray-700">
                        {error}
                    </p>
                    <p className="mt-4 mb-8 dark:text-gray-400">{help}</p>
                    <div className="flex justify-center">
                         <button
                            onClick={() => window.history.back()}
                            className="px-4 py-2 text-white bg-primary rounded"
                        >
                            Go back
                        </button>

                    </div>
                </div>
            </div>
        </section>
    );
}
