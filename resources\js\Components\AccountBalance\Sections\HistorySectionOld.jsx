import React from 'react';
import CursorPaginate from '../../Util/CursorPaginate';

export const HistorySectionOld = ({ list = [] }) => {
    // Check if list is an object with items property or just an array
    const items = Array.isArray(list) ? list : (list.items || []);
    const hasPagination = !Array.isArray(list) && list.items;

    return (
        <div>
            <div className="pt-2">
                <span className="text-2xl text-gray-700 font-semibold">Transaction History</span>
            </div>
            <div className="mx-auto container max-w-[1200px] mt-10">
                {/* <div className="flex items-center space-x-2 flex-wrap min-h-[2rem] mb-4">
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">Filter: </span>
                    </label>
                    <Filter />
                </div> */}
                
                {items.length === 0 ? (
                    <div className="flex justify-center">
                        <span className="text-xl">No transactions yet.</span>
                    </div>
                ) : (
                    <div className="w-full">
                        <table className="min-w-full text-left border-spacing-y-2.5 border-separate">
                            <thead className="bg-gray-50 text-sm">
                                <tr>
                                    <th className="w-[25%] py-3 pl-2">Amount</th>
                                    <th className="w-[25%] py-3">Status</th>
                                    <th className="w-[25%] py-3">Date</th>
                                    <th className="w-[25%] py-3">Time</th>
                                </tr>
                            </thead>
                            <tbody className="text-sm">
                                {items.map((item, index) => (
                                    <tr key={"hi-" + index} className="bg-white mb-2">
                                        <td className="rounded-l pl-2">${parseFloat(item.amount).toFixed(2)}</td>
                                        <td>
                                            {item.status || 
                                             (item.type === 'CREDIT' ? 'Rejected' : 
                                              item.type === 'DEBIT' ? 'Verified' : 'Unverified')}
                                        </td>
                                        <td>
                                            {new Date(item.created_at).toLocaleDateString('en-US', {
                                                month: '2-digit',
                                                day: '2-digit',
                                                year: 'numeric'
                                            })}
                                        </td>
                                        <td>
                                            {new Date(item.created_at).toLocaleTimeString('en-US', {
                                                hour: '2-digit',
                                                minute: '2-digit',
                                                hour12: true
                                            })}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                        
                        {hasPagination && (
                            <div className="mt-4">
                                <CursorPaginate
                                    onFirstPage={list.onFirstPage}
                                    onLastPage={list.onLastPage}
                                    nextPageUrl={list.nextPageUrl}
                                    previousPageUrl={list.previousPageUrl}
                                    itemCount={list.itemCount}
                                    total={list.total}
                                    itemName={list.itemName}
                                    shouldPreserveState={true}
                                />
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};
