export default function SubTotalItem({ label, amount }) {
    const formatAmount = (amount) => {
        return amount ? parseFloat(amount).toFixed(2) : "0.00";
    }

    return (
        <>
            <div className="flex items-center justify-between text-lg text-gray-700">
                <span className=" text-inherit">{label}</span>
                <span className=" text-inherit">${formatAmount(amount)}</span>
            </div>
        </>
    );
}
