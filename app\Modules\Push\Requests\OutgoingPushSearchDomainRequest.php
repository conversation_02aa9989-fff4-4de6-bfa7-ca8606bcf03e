<?php

namespace App\Modules\Push\Requests;

use App\Modules\Push\Services\OutgoingPushDomainService;
use Illuminate\Foundation\Http\FormRequest;

class OutgoingPushSearchDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'names' => ['string', 'min:1']
        ];
    }

    protected function passedValidation(): void
    {
        $names = preg_replace('/[^a-zA-Z0-9,. \n\r-]/', '', trim($this->names));
        $names = preg_replace('/[ ,\n\r ]/', ',', strtolower($this->names));
        $names = array_filter(explode(',', $names));
        $names = array_values($names);

        $this->replace(['names' => $names]);
    }

    public function search(): array
    {
        return OutgoingPushDomainService::instance()->searchByDomainName($this->names);
    }
}
