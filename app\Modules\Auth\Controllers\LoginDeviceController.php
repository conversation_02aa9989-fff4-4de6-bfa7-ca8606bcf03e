<?php

namespace App\Modules\Auth\Controllers;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Inertia\Inertia;
use <PERSON><PERSON><PERSON>\Agent\Agent;

class LoginDeviceController extends Controller
{
    /**
     * Show the login devices for the authenticated user.
     */
    public function index(Request $request)
    {
        // Initialize the Agent class
        $agent = new Agent;

        // Get the authenticated user's ID
        $userId = $request->user()->id;

        $currentSessionId = session()->getId();  // Get current session ID
        // dd($currentSessionId);
        // Fetch all sessions for the authenticated user
        $sessions = DB::table('sessions')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->orderBy('last_activity', 'desc') // Order by last_activity descending
            ->get()
            ->map(function ($session) use ($agent) {
                // Decode the session payload (base64 encoded)
                $decodedPayload = unserialize(base64_decode($session->payload));

                // Parse the user-agent string from the session
                $agent->setUserAgent($session->user_agent);

                // Get browser and version
                $browser = $agent->browser();
                $browserVersion = $agent->version($browser);

                // Get OS details
                $os = $agent->platform();

                // Get device type (Desktop, Mobile, etc.)
                $device = $agent->device();

                return [
                    'id' => $session->id,
                    'ip_address' => $session->ip_address,
                    'user_agent' => $session->user_agent,
                    'browser' => $browser,
                    'browser_version' => $browserVersion,
                    'os' => $os,
                    'device' => $device,
                    'last_activity' => $session->last_activity,
                    'data' => $decodedPayload, // Expose any other relevant data from session
                ];
            });

        return Inertia::render('Sessions/Index', [
            'sessions' => $sessions,
            'currentSessionId' => $currentSessionId,  // Pass current session ID to frontend
        ]);
    }

    public function deviceSelected(Request $request)
    {
        // Initialize the Agent class
        $agent = new Agent;

        // Get the authenticated user's ID
        $userId = $request->user()->id;

        $currentSessionId = session()->getId();  // Get current session ID
        // dd($currentSessionId);
        // Fetch all sessions for the authenticated user
        $sessions = DB::table('sessions')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->orderBy('last_activity', 'desc') // Order by last_activity descending
            ->get()
            ->map(function ($session) use ($agent) {
                // Decode the session payload (base64 encoded)
                $decodedPayload = unserialize(base64_decode($session->payload));

                // Parse the user-agent string from the session
                $agent->setUserAgent($session->user_agent);

                // Get browser and version
                $browser = $agent->browser();
                $browserVersion = $agent->version($browser);

                // Get OS details
                $os = $agent->platform();

                // Get device type (Desktop, Mobile, etc.)
                $device = $agent->device();

                return [
                    'id' => $session->id,
                    'ip_address' => $session->ip_address,
                    'user_agent' => $session->user_agent,
                    'browser' => $browser,
                    'browser_version' => $browserVersion,
                    'os' => $os,
                    'device' => $device,
                    'last_activity' => $session->last_activity,
                    'data' => $decodedPayload, // Expose any other relevant data from session
                ];
            });

        return Inertia::render('Sessions/SelectedDevices', [
            'sessions' => $sessions,
            'currentSessionId' => $currentSessionId,  // Pass current session ID to frontend
        ]);
    }

    public function deviceSessionDetails($sessionId)
    {
        // Initialize the Agent class
        $agent = new Agent;

        $currentSessionId = session()->getId();  // Get current session ID

        $sessions = DB::table('sessions')
            ->where('id', $sessionId)
            ->get()
            ->map(function ($session) use ($agent) {
                // Decode the session payload (base64 encoded)
                $decodedPayload = unserialize(base64_decode($session->payload));

                // Parse the user-agent string from the session
                $agent->setUserAgent($session->user_agent);

                // Get browser and version
                $browser = $agent->browser();
                $browserVersion = $agent->version($browser);

                // Get OS details
                $os = $agent->platform();

                // Get device type (Desktop, Mobile, etc.)
                $device = $agent->device();

                $formattedDate = Carbon::createFromTimestamp($session->last_activity)->format('F j, Y \\a\\t g:iA');

                return [
                    'id' => $session->id,
                    'ip_address' => $session->ip_address,
                    'user_agent' => $session->user_agent,
                    'browser' => $browser,
                    'browser_version' => $browserVersion,
                    'os' => $os,
                    'device' => $device,
                    'last_activity' => $session->last_activity,
                    'formattedDate' => $formattedDate,
                    'data' => $decodedPayload, // Expose any other relevant data from session
                ];
            });

        return Inertia::render('Sessions/Details', [
            'sessions' => $sessions,
            'currentSessionId' => $currentSessionId,  // Pass current session ID to frontend
        ]);
    }

    /**
     * Logout from a specific session and untrack it.
     */
    public function logoutFromSession($sessionId)
    {
        // Get the current session ID
        $currentSessionId = session()->getId();

        // Initialize the Agent class
        $agent = new Agent;

        if ($sessionId == $currentSessionId) {
            return response()->json(['message' => 'Cannot log out of the current session.'], 400);
        }

        // Fetch the session data
        $session = DB::table('sessions')
            ->where('id', $sessionId)
            ->first(); // Get only one session, not a collection

        if (! $session) {
            return response()->json(['message' => 'Session not found.'], 404);
        }

        // Parse the user-agent string from the session
        $agent->setUserAgent($session->user_agent);

        // Get browser and version
        $browser = $agent->browser();

        // Check if the session exists in authenticator_app_user_sessions
        $this->invalidateSession('authenticator_app_user_sessions', $session->ip_address, $browser);

        // Check if the session exists in email_otps
        $this->invalidateSession('email_otps', $session->ip_address, $browser);

        // Updaste the session record from the database to "untrack" it
        DB::table('sessions')
            ->where('id', $session->id)
            ->update([
                'id' => Str::random(40),
                'status' => 'inactive',
            ]);
        DB::table('users')->where('id', $session->user_id)->update(['remember_token' => null]);

        // Redirect back to the login devices page with a success message
        return redirect()->route('login.devices');
    }

    /**
     * Logout from all selected sessions, excluding the current session.
     */
    public function logoutSelectedSessions(Request $request)
    {
        $selectedSessions = $request->input('sessions');
        $currentSessionId = session()->getId();

        // Initialize the Agent class
        $agent = new Agent;

        // Exclude the current session from logout
        $selectedSessions = array_filter($selectedSessions, function ($sessionId) use ($currentSessionId) {
            return $sessionId != $currentSessionId;
        });

        if (empty($selectedSessions)) {
            return response()->json(['message' => 'No valid sessions selected.'], 400);
        }

        foreach ($selectedSessions as $sessionId) {
            // Get the current session
            $session = DB::table('sessions')->where('id', $sessionId)->first();

            // Parse the user-agent string from the session
            $agent->setUserAgent($session->user_agent);

            // Get browser and version
            $browser = $agent->browser();

            // If session exists, proceed with the update
            if ($session) {
                // Update the session to make it inactive and set a new random ID
                DB::table('sessions')
                    ->where('id', $session->id)
                    ->update([
                        'id' => Str::random(40), // Generate a new session ID
                        'status' => 'inactive', // Set status to inactive
                    ]);

                // Invalidate session in authenticator_app_user_sessions
                $this->invalidateSession('authenticator_app_user_sessions', $session->ip_address, $browser);

                // Invalidate session in email_otps
                $this->invalidateSession('email_otps', $session->ip_address, $browser);

                DB::table('users')->where('id', $session->user_id)->update(['remember_token' => null]);
            }
        }

        return redirect()->route('login.devices.selected');
    }

    /**
     * Logout from all sessions except the current one.
     */
    public function logoutFromAllSessions(Request $request)
    {
        $userId = $request->user()->id;
        $currentSessionId = session()->getId();  // Get current session ID

        // Initialize the Agent class
        $agent = new Agent;

        $sessions = DB::table('sessions')
            ->where('user_id', $userId)
            ->where('id', '!=', $currentSessionId)
            ->get();

        // Delete all sessions except the current session
        foreach ($sessions as $session) {

            // Parse the user-agent string from the session
            $agent->setUserAgent($session->user_agent);

            // Get browser and version
            $browser = $agent->browser();

            DB::table('sessions')
                ->where('id', $session->id)
                ->update([
                    'id' => Str::random(40),
                    'status' => 'inactive',
                ]);
            // Invalidate session in authenticator_app_user_sessions

            $this->invalidateSession('authenticator_app_user_sessions', $session->ip_address, $browser);

            // Invalidate session in email_otps
            $this->invalidateSession('email_otps', $session->ip_address, $browser);

            DB::table('users')->where('id', $session->user_id)->update(['remember_token' => null]);
        }

        // Optionally, invalidate the current session if needed
        // session()->invalidate();
        // session()->regenerateToken();

        return redirect()->route('login.devices')->with('message', 'Logged out from all devices.');
    }

    public function invalidateSession($tableName, $ip, $userAgent)
    {
        // Check if the session exists in the provided table
        $session = DB::table($tableName)
            ->where('ip', $ip) // Get the IP address
            ->where('user_agent', $userAgent) // Get the user agent
            ->where('session_valid_until', '>', Carbon::now())
            ->first(); // Fetch the first matching record
        if ($session) {
            // Update the session validity to the past (invalidate)
            DB::table($tableName)
                ->where('id', $session->id) // Use the actual ID
                ->update([
                    'session_valid_until' => Carbon::now()->subDay()->format('Y-m-d H:i:s'),
                ]);
        }
    }
}
