<?php

namespace App\Modules\Transfer\Services;

use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Traits\UserContact;
use Illuminate\Support\Facades\Auth;

class TransferCheckoutPaymentService
{
    use UserContact, UserLoggerTrait;

    private $maxAttemptError = 3;

    private $requestRegisterDomainsKey = 'domains';

    private $dispatchDelayInSeconds = 180; // three minutes

    public static function instance()
    {
        $transferCheckoutService = new self;

        return $transferCheckoutService;
    }

    public function createPaymentSummary(?array $request, ?array $registeredDomains)
    {
        $paymentPayload = $this->getDomainPayload($request, $registeredDomains);

        $invoiceId = PaymentSummaryService::instance()->createPayment(
            $paymentPayload,
            $this->getUserId(),
            PaymentSummaryType::PAYMENT_INVOICE,
            $request['payment_service_type'],
        );

        return $invoiceId;
    }

    public function getDomainPayload(array $request, ?array $createdDomains)
    {
        if (! $createdDomains) {
            return null;
        }
        $otherFees = $request[$this->requestRegisterDomainsKey]['other_fees'];
        $invoice = PaymentInvoiceService::instance()->createInvoicePayload(FeeType::TRANSFER, $this->getUserId(), $otherFees, $request['intent'] ?? null);
        $nodeInvoice = PaymentInvoiceService::instance()->createNodeInvoicePayload(FeeType::TRANSFER, $createdDomains['registered_domains'], $otherFees);

        return PaymentInvoiceService::instance()->createPaymentPayload($invoice, $nodeInvoice);
    }

    public function setPaymentDetails(float $bill_total): object
    {
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($bill_total);

        return PaymentIntentProvider::instance()->create($payment);
    }

    // PRIVATE Functions

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }
}
