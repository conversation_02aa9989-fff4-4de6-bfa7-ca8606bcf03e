<?php

namespace App\Modules\MarketPlace\Services;

use App\Events\DomainHistoryEvent;
use App\Exceptions\InsufficientBalanceException;
use App\Models\Domain;
use App\Models\MarketCart;
use App\Modules\Cart\Services\MultiCheckout\MultiCheckoutService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\MarketPlace\Constants\AfternicErrorTypes;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Constants\MarketPaymentStatus;
use App\Modules\MarketPlace\Jobs\AfternicDomainHold;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Stripe\Services\StripeLimiter;
use App\Traits\UserContact;
use App\Util\Helper\Domain\DomainTld;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class MarketPaymentService
{
    use UserContact, UserLoggerTrait;

    private $dispatchDelayInSeconds = 60;

    public static function instance()
    {
        $marketPaymentService = new self;

        return $marketPaymentService;
    }

    public function storeSingleCheckout(array $request): void
    {
        StripeLimiter::instance()->clearAttempt();

        $otherFees = $request['other_fees'];
        $domains = $request['domains'];

        $transferPrice = floatval($otherFees['transfer_total'] + $otherFees['icann_fee']);
        $this->checkRegistryBalance($transferPrice, $domains[0]['tld_id']); // NEW
        app(AuthLogger::class)->info($this->fromWho('checkRegistryBalance'));
        $marketDomains = $this->createMarketPlaceDomains($request);
        app(AuthLogger::class)->info($this->fromWho('createMarketPlaceDomains'));
        $invoiceId = $this->createPaymentSummary($request, $marketDomains);
        app(AuthLogger::class)->info($this->fromWho('createPaymentSummary'));
        $this->callMarketRequestJob($marketDomains);
        app(AuthLogger::class)->info($this->fromWho('callMarketRequestJob'));
    }

    public function placeHold(string $domain)
    {
        try {
            AfternicMiddleware::instance()::placeHold($domain);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new Exception(AfternicErrorTypes::PLACE_HOLD_ERROR.' '.$e->getMessage());
        }
    }

    // public function getOrderID(array $domain): string
    // {
    //     $res = AfternicMiddleware::instance()::createOrder(strtolower($domain['domain']), $domain['price']);

    //     return $res->orderId;
    // }

    public function getOrderID(string $domain, float $price)
    {
        try {
            $res = AfternicMiddleware::instance()::createOrder(strtolower($domain), $price);

            return $res->orderId;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new Exception(AfternicErrorTypes::CREATE_ORDER_ERROR.' '.$e->getMessage());
        }
    }

    // PRIVATE functions

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function createMarketPlaceDomains(array $request)
    {
        $domains = $request['domains'];
        $cartContent = MarketCartService::instance()->getCollectionById($domains[0]['id']);

        $registeredDomains = MultiCheckoutService::instance()->getCreatedDomains($cartContent);
        $this->removeFromCart($request['domains'], (int) $request['user_id']);

        return $registeredDomains;
    }

    private function removeFromCart(array $domains, int $userId): void
    {
        MarketCart::where('name', strtolower($domains[0]['domain']))
            ->where('user_id', $userId)->delete();
    }

    private function checkRegistryBalance(float $price, int $tld_id)
    {
        $tlds = DomainTld::getTldsById();
        $registryId = $tlds[$tld_id]->registry_id;
        $balance = RegistryAccountBalanceService::balance($registryId);

        if ($price > $balance->balance) {
            throw new InsufficientBalanceException(503, "We're currently undergoing maintenance. Marketplace transactions are temporarily unavailable. Please bear with us. Thank you for your patience.", 'System Maintenance in Progress');
        }

        RegistryAccountBalanceService::credit($balance, $price, RegistryTransactionType::SUB_FUND, RegistryTransactionType::MARKETPLACE_TRANSFER);
    }

    private function createPaymentSummary(array $request, Collection $registeredDomains)
    {
        $userId = $request['user_id'] ?? $this->getUserId();

        $paymentPayload = $this->createPaymentPayload($request, $registeredDomains); // MULTI CHECKOUT createPaymentSummary
        $invoiceId = PaymentSummaryService::instance()->createPayment(
            $paymentPayload,
            $userId,
            PaymentSummaryType::MARKETPLACE_INVOICE,
            $request['payment_service_type']
        );

        return $invoiceId;
    }

    private function createPaymentPayload(array $request, Collection $registeredDomains): array
    {
        $userId = $request['user_id'] ?? $this->getUserId();
        $invoice = $this->createInvoicePayload($request);
        $otherFees = $request['other_fees'];

        $nodeInvoice = $this->createNodeInvoicePayload($userId, $registeredDomains, $otherFees);

        return [
            'invoice' => $invoice, // create MarketPlacePaymentInvoice
            'node_invoice' => $nodeInvoice, // StoreMarketPlaceDomainNode MarketPlaceDomains
            'domain' => $registeredDomains,
        ];
    }

    private function createInvoicePayload(array $request)
    {
        $otherFees = $request['other_fees'];
        $transferPrice = floatval($otherFees['transfer_total'] + $otherFees['icann_fee']);

        return [
            'user_id' => $request['user_id'] ?? $this->getUserId(),
            'total_amount' => $transferPrice ?? 0, // for epp
            'paid_amount' => $otherFees['bill_total'] ?? 0, // from afternic + epp
            'gross_amount' => $otherFees['bill_total'] ?? 0,
            'bill_amount' => $otherFees['bill_total'] ?? 0,
            'net_amount' => 0,
            'service_fee' => 0,
            'status' => MarketPaymentStatus::PAID,
            'total_payment_node' => $otherFees['domain_count'] ?? 1,
            'payment_intent' => $request['intent'] ?? null,
        ];
    }

    private function createNodeInvoicePayload(int $userId, Collection $registeredDomains, array $otherFees)
    {

        // event(new DomainHistoryEvent([
        //     'domain_id' => $domainId,
        //     'type' => 'MARKET_TRANSFER_PENDING',
        //     'user_id' => $userId,
        //     'status' => 'success',
        // ]));

        return [
            'user_id' => $userId,
            'registered_domains' => $registeredDomains,
            'status' => MarketConstants::DOMAIN_PENDING_HOLD,
            'order_id' => 'order_id',
            'other_fees' => $otherFees,
        ];
    }

    private function callMarketRequestJob(?Collection $marketDomains)
    {
        // hold afternic domains
        // get order id
        foreach ($marketDomains as $domain) {
            AfternicDomainHold::dispatch($domain, $this->getUserId());
        }
    }
}
