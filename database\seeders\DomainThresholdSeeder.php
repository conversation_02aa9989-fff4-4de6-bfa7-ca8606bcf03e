<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DomainThresholdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('domain_threshold')->insert([
            'threshold_limit' => 50,
            'action' => 'notify',
            'period_type' => 'monthly',
            'send_notif' => '<EMAIL>',
            'times_triggered' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
