<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->addTransactionIdColumn();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }

    private function addTransactionIdColumn()
    {
        // check new columns
        if (Schema::hasColumn('payment_summaries', 'transaction_id')) {
            echo 'Column "transaction_id" of relation "payment_summaries" already exists...'.PHP_EOL;

            return;
        }

        Schema::table('payment_summaries', function (Blueprint $table) {
            $table->string('transaction_id')->nullable();
        });
    }
};
