<?php

namespace App\Modules\Transfer\Services;

use App\Events\DomainHistoryEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\CreateServices\CreatedDomains;
use App\Modules\Domain\Services\CreateServices\CreatedRegisteredDomains;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Notification\Services\TransferNotificationService;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Jobs\TransferEppDomain;
use App\Modules\Transfer\Services\UpdateTransferCartService;
use App\Modules\Transfer\Services\UserTransferCart;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TransferDomainCreateService
{
    use UserLoggerTrait;

    private $isJob = true;

    public static function instance(): self
    {
        $createDomainService = new self;

        return $createDomainService;
    }

    public function storeDomains()
    {
        $transferCartContent = $this->getTransferCartContent();
        if (! $transferCartContent) {
            return false;
        }

        $createdDomains = $this->getCreatedDomains($transferCartContent);

        $this->notifyStoreSuccess($createdDomains, 'Domains', true);
        $this->deleteCart($transferCartContent);

        return $createdDomains;
    }

    public function storeRegisteredDomains(Collection $unregisteredDomains, string $status)
    {
        $registeredDomains = $this->getCreatedRegisteredDomains($unregisteredDomains, $status);

        $this->notifyStoreSuccess($unregisteredDomains, 'Registered Domains', false);

        return $registeredDomains;
    }

    public function storeTransferDomains(Collection $registeredDomains): void
    {
        $insertData = [];
        $now = now();

        foreach ($registeredDomains as $registeredDomain) {
            $insertData[] = [
                'registered_domain_id' => $registeredDomain->id,
                'status' => TransferRequest::PENDING_REQUEST,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        if (! empty($insertData)) DB::table('transfer_domains')->insert($insertData);
    }

    public function requestTransferToEpp(array $domainAuthCodes, array $createdDomains): void
    {
        $registeredDomains = $createdDomains['domains'];
        $userId = $this->getUserId();
        $userEmail = $this->getUserEmail();

        foreach ($registeredDomains as $regDomain) {
            $domainId = $regDomain['domain']->id;
            $domainName = $regDomain['domain']->name;
            $regDomainId = $regDomain['registered_domain']->id;
            $authCode = $domainAuthCodes[$domainName] ?? null;
            TransferEppDomain::dispatch($domainId, $domainName, $authCode, $regDomainId, $userId, $userEmail);
            event(new DomainHistoryEvent(['domain_id' => $domainId, 'type' => 'TRANSFER_INBOUND_REQUEST', 'user_id' => $userId ?? null, 'status' => 'success', 'message' => 'Domain "' . $domainName . '" inbound transfer request initiated by ' . $userEmail . ' with auth code. Pending transfer approval.', 'payload' => $regDomain]));
        }
    }

    // PRIVATE FUNCTIONS

    private function getTransferCartContent()
    {
        $transferCart = new UserTransferCart;
        $transferCartContent = $transferCart->filterDuplicateDomains();

        if ($transferCartContent->isEmpty()) {
            return false;
            // throw new FailedRequestException(404, 'Nothing to process.', 'Cart is empty or duplicate domains');
        }

        return $transferCartContent;
    }

    private function getCreatedDomains(Collection $transferCartContent)
    {
        $domains = new CreatedDomains($transferCartContent);

        return $domains->format()->store()->getDomains();
    }

    private function getCreatedRegisteredDomains(Collection $unregisteredDomains, string $status)
    {
        $registeredDomains = new CreatedRegisteredDomains($unregisteredDomains, $status);

        return $registeredDomains->format()->store()->mapByDomainId()->getDomainsArray();
    }

    private function notifyStoreSuccess(Collection $createdDomains, string $key, bool $isNotify = false): void
    {
        $domainNames = $createdDomains->pluck('name')->toArray();
        app(AuthLogger::class)->info($this->fromWho('Created ' . $key . ': ' . implode(',', $domainNames)));

        if ($isNotify) {
            TransferNotificationService::instance()->sendBulkDomainTransferPendingRequestNotif($domainNames);
        }
    }

    private function deleteCart(Collection $cartContent)
    {
        UpdateTransferCartService::instance()->softDelete([
            'id' => $cartContent->pluck('id')->toArray()
        ]);
    }

    // private function getUpdateEppDomainPayload(array $domain, string $jobType)
    // {
    //     return [
    //         JobPayloadKeys::DOMAIN => $domain['domain'],
    //         JobPayloadKeys::REGISTERED_DOMAIN => $domain['registered_domain'],
    //         JobPayloadKeys::REGISTRY => $domain['registry'],
    //         JobPayloadKeys::USER_ID => $this->getUserId(),
    //         JobPayloadKeys::EMAIL => $this->getUserEmail(),
    //         JobPayloadKeys::UPDATE_TYPE => $jobType,
    //     ];
    // }

    // private function getRegisterEppDomainPayload(array $domain, array $refundData)
    // {
    //     return [
    //         JobPayloadKeys::DOMAIN => $domain['domain'],
    //         JobPayloadKeys::REGISTERED_DOMAIN => $domain['registered_domain'],
    //         JobPayloadKeys::REGISTRY => $domain['registry'],
    //         JobPayloadKeys::USER_ID => $this->getUserId(),
    //         JobPayloadKeys::EMAIL => $this->getUserEmail(),
    //         JobPayloadKeys::REFUND_DATA => $refundData,
    //     ];
    // }

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        return Auth::user()->email ?? 'Unauthorized';
    }
}
