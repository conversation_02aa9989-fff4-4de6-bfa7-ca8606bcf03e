<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Cashier\Billable;
use Laravel\Sanctum\HasApiTokens;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Hash; 

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, Billable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'is_active',
        'last_active_at',
        'unit',
        'street',
        'city',
        'state_province',
        'postal_code',
        'country_code',
        'pin_code', 
        'verification_prompt',
        'enabled_login_auth_options',
        'secured_transactions', 
        'two_factor_setup',
        'payment_method_setup',
        'account_credit_setup',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'pin_code',
        'api_key_bodis',
        'remember_token',
        'authenticator_app_secret_key', 
        'stripe_customer_id'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_active_at'    => 'datetime',
        'password'          => 'hashed',
        'pin_code'          => 'hashed'
    ];

    //** Accessors & Mutators */

    public function hasPinCode() : bool 
    {
        return $this->pin_code != null; 
    }

    public function hasApiKeyBodis(): bool
    {
        return $this->api_key_bodis != null;
    }

    //** belongsTo, belongsToMany, hasOne, hasMany relationships */ 

    public function ips(): BelongsToMany
    {
        return $this->belongsToMany(Ip::class, 'user_ips');
    }

    public function emailAuthenticationSessions() : HasMany
    {
        return $this->hasMany(
            EmailOtp::class, 
            'user_id', 
            'id'
        );
    }

    public function authenticatorAppSessions(): HasMany
    {
        return $this->hasMany(
            AuthenticatorAppUserSessions::class,
            'user_id',
            'id'
        );
    }

    public function authenticatorAppRecoveryCodes(): HasMany
    {
        return $this->hasMany(
            AuthenticatorAppRecoveryCode::class,
            'user_id',
            'id'
        );
    }
}
