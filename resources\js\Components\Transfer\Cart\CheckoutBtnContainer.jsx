import Checkbox from "@/Components/Checkbox";
import PrimaryLink from "@/Components/PrimaryLink";

export default function CheckoutBtnContainer({
    isAgree = false,
    onHandleChangeAgree,
}) {
    return (
        <div className="mx-auto container max-w-[900px] mt-8 flex flex-col space-y-4">
            <div className="flex items-center justify-end flex-col space-y-4">
                <label className="flex items-center">
                    <Checkbox
                        name="is_agree"
                        value="is_agree"
                        checked={isAgree}
                        handleChange={onHandleChangeAgree}
                    />
                    <span className="ml-2 text-sm text-gray-600 text-center">
                        By clicking "Checkout" you confirm that you have
                        read the StrangeDomains' &nbsp;
                        <a
                            className="underline text-sm text-link"
                            href={route("refund.policy")}
                            target="_blank"
                        >
                            Refund Policy
                        </a>
                        &nbsp;and&nbsp;
                        <a
                            className="underline text-sm text-link"
                            href={route("lockin.policy")}
                            target="_blank"
                        >
                            60-Day Lock-In Policy.
                        </a>
                    </span>
                </label>
                {
                    <PrimaryLink
                        method="post"
                        href={route('transfer.inbound.summary')}
                        className="w-full"
                        processing={!isAgree}
                    >
                        Checkout
                    </PrimaryLink>
                }
            </div>
        </div>
    );
}
