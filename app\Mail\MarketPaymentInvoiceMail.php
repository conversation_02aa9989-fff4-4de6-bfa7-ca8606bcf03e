<?php

namespace App\Mail;

use App\Mail\Constants\Links;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\ThrottlesExceptions;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class MarketPaymentInvoiceMail extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $payload;

    private $termsUrl;

    private $data;

    private $domain;

    private $paid;

    private $transfer;

    private $total;

    private $name;

    private $order_id;

    private $sender_email;

    private $sender_name;

    private $backOffMinutes = 5;

    public $failOnTimeout = true;

    public $uniqueFor = 300; // 5 minutes

    public $tries = 5;

    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct($payload)
    {
        $this->termsUrl = config('app.url').Links::TERMS_AND_CONDITIONS;
        $this->sender_email = config('mail.from.address');
        $this->sender_name = config('mail.from.sd_name');

        $this->name = $payload['name'];

        $this->data = $payload['data'];

        $this->total = $payload['total'];

        $this->transfer = $payload['data']->total_amount;

        $this->paid = $payload['data']->paid_amount;

        $this->domain = $payload['domain'];

        $this->order_id = $payload['order_id'];
    }

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address($this->sender_email, $this->sender_name),
            subject: 'Payment Invoice from '.config('app.name'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.MarketInvoice',
            with: [
                'termsUrl' => $this->termsUrl,
                'data' => $this->data,
                'node_type' => 'TRANSFER',
                'domain' => $this->domain,
                'name' => $this->name,
                'paid' => $this->paid,
                'total' => $this->total,
                'transfer' => $this->transfer,
                'order_id' => $this->order_id,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
