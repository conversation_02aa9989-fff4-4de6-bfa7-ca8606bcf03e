<?php

namespace App\Rules\Form;

use App\Models\User;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class EmailValidation implements Rule
{
    /**
     * The ID of the user to ignore in unique validation.
     *
     * @var int|null
     */
    protected ?int $ignoreUserId;

    /**
     * The validation error message.
     *
     * @var string
     */
    protected string $errorMessage = '';

    /**
     * Create a new rule instance.
     *
     * @param  int|null  $ignoreUserId
     */
    public function __construct(?int $ignoreUserId = null)
    {
        $this->ignoreUserId = $ignoreUserId;
    }

    public function passes($attribute, $value): bool
    {
        $emailRules = ['string', 'email:rfc,dns', 'regex:/^[0-9a-zA-Z.@_-]*$/'];  

        $validator = Validator::make(
            ['email' => $value],
            ['email' => $emailRules]
        );

        if ($validator->fails()) {
            $this->errorMessage = 'Please provide a valid email address.';
            return false;
        }

        if ($this->ignoreUserId !== null) {
            $exists = User::where('email', $value)
                ->where('id', '!=', $this->ignoreUserId)
                ->exists();

            if ($exists) {
                $this->errorMessage = 'This email address is already in use.';
                return false;
            }
        }

        return true;
    }

    public function message(): string
    {
        return $this->errorMessage;
    }

    public static function unique(int $userId): self
    {
        return new static($userId);
    }
}