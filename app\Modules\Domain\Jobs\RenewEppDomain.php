<?php

namespace App\Modules\Domain\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\JobServices\JobRecord;
use App\Modules\Domain\Services\JobServices\JobRenewEppService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class RenewEppDomain implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use UserLoggerTrait;

    private object $domain;

    private object $registeredDomain;

    private int $id;

    private int $userId;

    private string $registry;

    private string $email;

    private $updateType;

    private int $backOffMinutes = 5; // (30 * 60)

    private JobRecord $jobRecord;

    private array $refundData;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    // public function __construct($domain, $userId, $registry, $email)
    public function __construct($payload)
    {
        $this->domain = $payload[JobPayloadKeys::DOMAIN];
        $this->registeredDomain = $payload[JobPayloadKeys::REGISTERED_DOMAIN];
        $this->userId = $payload[JobPayloadKeys::USER_ID];
        $this->registry = $payload[JobPayloadKeys::REGISTRY];
        $this->email = $payload[JobPayloadKeys::EMAIL];
        $this->updateType = $payload[JobPayloadKeys::UPDATE_TYPE];
        $this->refundData = $payload[JobPayloadKeys::REFUND_DATA];

        $this->id = $this->domain->id;
        $this->jobRecord = $this->createRecord();

        $this->onConnection(QueueConnection::DOMAIN_RENEWAL);
        $this->onQueue(QueueTypes::DOMAIN_RENEWAL[$this->registry]);
    }

    public $uniqueFor = 300; // can retry every 5 minutes

    public function uniqueId(): int
    {
        return intval(Carbon::now()->timestamp.$this->id);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            JobRenewEppService::instance()->handle($this->jobRecord);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $this->email));
            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();

                return;
            }

            $this->fail();
        }
    }

    public function failed(Throwable $exception): void
    {

        // Send user notification of failure, etc...
    }

    public function retry(): void
    {
        $this->jobRecord->setPendingJob();
    }

    private function createRecord()
    {
        return new JobRecord([
            JobPayloadKeys::DOMAIN => $this->domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $this->registeredDomain,
            JobPayloadKeys::REGISTRY => $this->registry,
            JobPayloadKeys::USER_ID => $this->userId,
            JobPayloadKeys::EMAIL => $this->email,
            JobPayloadKeys::UPDATE_TYPE => $this->updateType,
            JobPayloadKeys::QUEUE_TYPE => QueueConnection::DOMAIN_RENEWAL,
            JobPayloadKeys::REFUND_DATA => $this->refundData,
        ]);
    }
}
