<?php

namespace App\Modules\Epp\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Helper\HttpResponse;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

class PollHttpService
{
    private static $size = 5;

    private static $pageLimit = 10;

    public static function check(string $registry): array
    {
        $payload = ['size' => self::$size];

        try {
            $request = Http::poll($registry)->post(Config::get('poll.check'), $payload);
        } catch (RequestException $e) {
            return $e->response->json();
        } catch (Exception $e) {
            app(AuthLogger::class)->error('PollHttpService: '.$e->getMessage());
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public static function get(string $registry): array
    {
        $payload = [
            'size' => self::$pageLimit,
        ];

        try {
            $request = Http::poll($registry)->post(Config::get('poll.all'), $payload);
        } catch (RequestException $e) {
            throw new FailedRequestException(500, 'Connection refused', 'Internal Server Error');
        } catch (Exception $e) {
            app(AuthLogger::class)->error('PollHttpService: '.$e->getMessage());
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return HttpResponse::filled($request);
    }
}
