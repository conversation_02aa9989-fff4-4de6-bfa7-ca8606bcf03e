<?php

namespace App\Modules\Auth\Services\AuthenticatorApp;

use App\Models\User;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\RateLimit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Validation\ValidationException;

class AuthenticatorAppResetService
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Process Index Data
     *
     * @param  string  $token
     * @param  string  $userId
     */
    public function processIndexData($token, $userId): array
    {
        $secretKey = (new AuthenticatorAppService)->generateSecretKey();
        $qrCodeImageUrl = (new AuthenticatorAppService)->generateQrCodeImageURL($secretKey, $userId);

        return
        [
            'token' => $token,
            'secretKey' => $secretKey,
            'qrCodeImageUrl' => $qrCodeImageUrl,
        ];
    }

    /**
     * Verify Code
     */
    public function verifyCode(array $data, int $userId)
    {
        $rateLimitKey = RateLimiterKey::authenticatorAppSettingsEnable(Auth::user()->id);

        $executed = RateLimit::attempt($rateLimitKey, 5, 30);

        if (! $executed) {
            throw ValidationException::withMessages(['code' => 'Too many attempts. Wait for 30 seconds before trying again.']);
        }

        $isVerified = (new AuthenticatorAppService)->verifyCode($data['secretKey'], $data['code']);

        if ($isVerified == true) {
            $user = User::find($userId);

            $user->authenticator_app_secret_key = $data['secretKey'];
            $user->save();

            $user->authenticatorAppSessions()->delete();

            return Redirect::route('domain');
        } else {
            throw ValidationException::withMessages(['code' => 'Code is Invalid']);
        }
    }
}
