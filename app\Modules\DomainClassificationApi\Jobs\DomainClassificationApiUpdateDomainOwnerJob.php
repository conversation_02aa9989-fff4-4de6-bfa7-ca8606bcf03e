<?php

namespace App\Modules\DomainClassificationApi\Jobs;

use App\Modules\DomainClassificationApi\Services\DomainClassificationApiService;
use App\Util\Constant\QueueConnection;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class DomainClassificationApiUpdateDomainOwnerJob implements ShouldBeUnique, ShouldQueue
{
    use Queueable;

    private string $domainName;

    private string $newClientEmail;

    public int $uniqueFor = 3600;

    /**
     * Create a new job instance.
     */
    public function __construct(string $domainName, string $newClientEmail)
    {
        $this->domainName = $domainName;
        $this->newClientEmail = $newClientEmail;

        $this->onConnection(QueueConnection::DOMAIN_CLASSIFICATION_JOBS);
        $this->onQueue('update');
    }

    /**
     * Job Unique Id
     */
    public function uniqueId(): int
    {
        return intval(now()->timestamp.$this->newClientEmail.$this->domainName);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        (new DomainClassificationApiService)->updateDomainOwnership($this->domainName, $this->newClientEmail);
    }
}
