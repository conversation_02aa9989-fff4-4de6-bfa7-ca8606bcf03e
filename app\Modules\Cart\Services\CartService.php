<?php

namespace App\Modules\Cart\Services;

use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Stripe\Services\StripeLimiter;
use Illuminate\Support\Facades\DB;

class CartService
{
    public static function instance(): self
    {
        $cartService = new self;

        return $cartService;
    }

    public function getViewCart(): array
    {
        $myCart = $this->getMyCart();
        $unavailable = $this->getUnavailableList($myCart['domains']);
        $myCart['unavailable'] = $unavailable;
        unset($myCart['cartCollection']);

        return $myCart;
    }

    public function getMyCart(): array
    {
        StripeLimiter::instance()->checkAttempt();

        $cart = new UserCart;
        $settings = $this->getSettingsData();

        return [
            'domains' => $cart->getAll(),
            'domains_list' => $cart->getDomainsId(),
            'settings' => $settings,
            'cartCollection' => $cart,
        ];
    }

    // PRIVATE FUNCTIONS

    private function getSettingsData(): array
    {
        $registration_fees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::REGISTRATION);

        return [
            'registration_fees' => $registration_fees,
        ];
    }

    private function getUnavailableList(array $domains): array
    {
        $domains_list = [];
        $domains_not_available = [];

        if (! empty($domains)) {
            $domainNames = array_column($domains, 'name');
            $registeredDomains = $this->getRegisteredDomains($domainNames);
            foreach ($domains as $d) {
                if (in_array($d->name, $registeredDomains)) {
                    $domains_not_available[] = $d;
                    $domains_list[] = $d->id;
                }
            }
        }

        return [
            'domains' => $domains_not_available,
            'domains_list' => $domains_list,
            'type' => 'cart',
            'message' => 'not available for checkout and will be deleted from cart.',
        ];
    }

    private function getRegisteredDomains(array $domains): array
    {
        $unAvailable = DB::table('domains')
            ->whereNull('deleted_at')
            ->whereIn('name', $domains)
            ->pluck('name')->toArray();

        return $unAvailable;
    }
}
