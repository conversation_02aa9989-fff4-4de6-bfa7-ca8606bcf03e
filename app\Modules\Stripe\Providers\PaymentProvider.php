<?php

namespace App\Modules\Stripe\Providers;

use App\Exceptions\UserDomainException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Stripe\Constants\StripePrefixes;
use App\Modules\Stripe\Helpers\StripeEncryptHelper;
use Exception;

class PaymentProvider
{
    use UserLoggerTrait;

    private $stripe;

    public function __construct()
    {
        $this->stripe = StripeKeyProvider::instance()->getStripeClient();
    }

    public static function instance(): self
    {
        $paymentProvider = new self;

        return $paymentProvider;
    }

    public function refund(array $data, bool $toReturn = false): \Stripe\Refund|false
    {
        $amount = $data['amount'] * 100;
        $payment_intent = StripeEncryptHelper::getId(StripePrefixes::PAYMENT_INTENT, $data['payment_intent']);

        try {
            $stripeObj = $this->stripe->refunds->create([
                'amount' => $amount,
                'payment_intent' => $payment_intent,
            ]);
            app(AuthLogger::class)->info($this->fromWho('Created stripe refund: '.$payment_intent.' '.$amount));

            return $stripeObj;
        } catch (\Stripe\Exception\CardException $e) {
            app(AuthLogger::class)->error($e->getError()->message);
            if ($toReturn) {
                return false;
            }
            throw new UserDomainException($e->getError()->code, $e->getError()->message, 'Card error');
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            app(AuthLogger::class)->error($e->getError()->message);
            if ($toReturn) {
                return false;
            }
            throw new UserDomainException($e->getError()->code, $e->getError()->message, 'Invalid request');
        } catch (Exception $e) {
            app(AuthLogger::class)->error('Unknown error on Stripe request');
            if ($toReturn) {
                return false;
            }
            throw new UserDomainException(400, 'Unknown error', 'Error');
        }
    }

    public function retrieveRefund(string $refundId): \Stripe\Refund
    {
        $paymentRefundId = StripeEncryptHelper::getId(StripePrefixes::REFUND, $refundId);
        try {
            $stripeObj = $this->stripe->refunds->retrieve($paymentRefundId);

            return $stripeObj;
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            app(AuthLogger::class)->error($e->getError()->message);
            throw new UserDomainException($e->getError()->code, $e->getError()->message, 'Invalid request');
        } catch (Exception $e) {
            app(AuthLogger::class)->error('Unknown error on Stripe request');
            throw new UserDomainException(400, 'Unknown error', 'Error');
        }
    }

    public function retrieveCharge(string $chargeId): \Stripe\Charge
    {
        $paymentChargeId = StripeEncryptHelper::getId(StripePrefixes::CHARGE, $chargeId);
        try {
            $stripeObj = $this->stripe->charges->retrieve($paymentChargeId);

            return $stripeObj;
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            app(AuthLogger::class)->error($e->getError()->message);
            throw new UserDomainException($e->getError()->code, $e->getError()->message, 'Invalid request');
        } catch (Exception $e) {
            app(AuthLogger::class)->error('Unknown error on Stripe request');
            throw new UserDomainException(400, 'Unknown error', 'Error');
        }
    }

    public function retrieveBalanceTransaction(string $balanceId): \Stripe\BalanceTransaction
    {
        try {
            $stripeObj = $this->stripe->balanceTransactions->retrieve($balanceId);

            return $stripeObj;
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            app(AuthLogger::class)->error($e->getError()->message);
            throw new UserDomainException($e->getError()->code, $e->getError()->message, 'Invalid request');
        } catch (Exception $e) {
            app(AuthLogger::class)->error('Unknown error on Stripe request');
            throw new UserDomainException(400, 'Unknown error', 'Error');
        }
    }
}
