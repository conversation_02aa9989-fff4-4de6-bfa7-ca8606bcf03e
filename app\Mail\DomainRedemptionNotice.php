<?php

namespace App\Mail;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class DomainRedemptionNotice extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $domains;

    private $backOffMinutes = 5;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public $uniqueFor = 120; // 2 minutes

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct(array $domains)
    {
        $this->domains = $domains;
    }

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: 'Domain Redemption Notice',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $message = "We are reaching out to inform you that the following domain has been expired for over 40 days and will soon enter a 30-day redemption period:";
        $textA = "During the redemption period, you may still recover your domain before it is permanently deleted. However, additional fees may apply for restoration.";
        $textB = "To prevent losing ownership of your domain, please contact our support team as soon as possible.";

        return new Content(
            markdown: 'Mails.DomainRedemptionNotice',
            with: [
                'greeting' => 'Greetings!',
                'body' => $message,
                'textA' => $textA,
                'textB' => $textB,
                'sender' => config('mail.from.sd_name'),
                'domains' => $this->domains,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    private function failed($e)
    {
        app(AuthLogger::class)->error('DomainRedemptionNotice: ' . $e->getMessage());
        $this->fail();
    }
}
