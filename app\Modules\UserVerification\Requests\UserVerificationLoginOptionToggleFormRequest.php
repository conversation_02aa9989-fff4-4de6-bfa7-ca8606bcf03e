<?php

namespace App\Modules\UserVerification\Requests;

use App\Modules\UserVerification\Constants\UserVerificationLoginOptionConstants;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserVerificationLoginOptionToggleFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'value'        => 
            [
                'required', 
                'string',
                Rule::in(UserVerificationLoginOptionConstants::OPTIONS),
            ],
            'shouldEnable' => ['required', 'boolean'],
        ];
    }
}
