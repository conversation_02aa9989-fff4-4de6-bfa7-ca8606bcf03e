<?php

namespace App\Modules\Contact\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

class EppContactService
{
    use UserLoggerTrait;

    public static function instance()
    {
        $eppContactService = new self;

        return $eppContactService;
    }

    public function callContactStore(array $payload, string $registry, ?string $email = null): array
    {
        if (is_null($registry)) {
            return ['status' => Config::get('contact.response.error')];
        }

        try {
            $response = Http::contact($registry)->post('contact', $payload);
        } catch (RequestException $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));

            return $e->response->json();
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected Response');
        }

        return $response->json();
    }

    public function callContactUpdate(array $payload, string $registry, ?string $email = null)
    {
        if (is_null($registry)) {
            return ['status' => Config::get('contact.response.error')];
        }

        try {
            $response = Http::contact($registry)->put('/contact', $payload);
        } catch (RequestException $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));

            return $e->response->json();
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected Response');
        }

        return $response->json();
    }

    public function callContactCheck(string $contact, string $registry, ?string $email = null)
    {
        if (is_null($registry)) {
            return ['status' => Config::get('contact.response.error')];
        }

        try {
            $response = Http::contact($registry)->post('/contact/check', $contact);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));

            return true;
        }

        return ! $response->json('data.available');
    }
}
