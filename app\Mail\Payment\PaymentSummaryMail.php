<?php

namespace App\Mail\Payment;

use App\Mail\Constants\Links;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\ThrottlesExceptions;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class PaymentSummaryMail extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $payload;

    private $termsUrl;

    private $data;

    private $name;

    private $sender_email;

    private $sender_name;

    private $backOffMinutes = 5;

    private $paymentMethodDetails;

    private $paymentServiceType;

    private $paidAmount;

    private $summaryData;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public $uniqueFor = 300; // 5 minutes

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    public function middleware(): array
    {
        return [(new ThrottlesExceptions(3, 10))->backoff($this->backOffMinutes)];
    }

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct(array $payload)
    {

        $item = $payload['payload'];
        $data_ = $item['data'];
        $this->termsUrl = config('app.url').Links::TERMS_AND_CONDITIONS;
        $this->sender_email = config('mail.from.address');
        $this->sender_name = config('mail.from.sd_name');
        $this->data = $data_['data'];
        $this->name = $item['name'];
        $this->paidAmount = $item['data']['paid_amount'];

        $this->paymentServiceType = $this->getPaymentServiceType($data_['data']);
        $this->paymentMethodDetails = PaymentServiceType::TEXT[$this->paymentServiceType] ?? 'Card';

        $this->summaryData = $data_['summaryData'];

        $paymentIntent = $data_['paymentIntent'] ?? '';

        if ($this->paymentServiceType === PaymentServiceType::STRIPE) {
            $this->paymentMethodDetails = 'Card';
            $this->getStripePaymentIntent($paymentIntent);
        }

        $this->summaryData->registration_subtotal = (array_key_exists('registration_subtotal', $item['data'])) ? $item['data']['registration_subtotal'] : 0;
        $this->summaryData->premium_subtotal = (array_key_exists('premium_subtotal', $item['data'])) ? $item['data']['premium_subtotal'] : 0;
        $this->summaryData->total_fees = (array_key_exists('total_fees', $item['data'])) ? $item['data']['total_fees'] : 0;
        $this->summaryData->subtotal = (array_key_exists('subtotal', $item['data'])) ? $item['data']['subtotal'] : 0;

        // dd($this->summaryData);
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address($this->sender_email, $this->sender_name),
            subject: 'Payment Invoice from '.config('app.name'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.Payment.PaymentInvoice',
            with: [
                'termsUrl' => $this->termsUrl,
                'data' => $this->data,
                'name' => $this->name,
                'paymentIntent' => $this->paymentMethodDetails,
                'paidAmount' => $this->paidAmount,
                'summaryData' => $this->summaryData,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    // PRIVATE FUNCTIONS

    private function getPaymentServiceType($data)
    {
        $invoice = $data[0];
        $paymentServiceType = PaymentServiceHelper::instance()->getPaymentServiceType($invoice);

        return $paymentServiceType ?? 'Card';
    }

    private function getStripePaymentIntent(string $paymentIntent)
    {
        if ($paymentIntent != null) {
            try {
                $paymentIntentDetails = PaymentIntentProvider::instance()->retrieveIntent($paymentIntent);

                $paymentMethodDetails = PaymentIntentProvider::instance()->retrievePaymentMethod($paymentIntentDetails->payment_method);

                $cardDetails = $paymentMethodDetails['card'];

                $this->paymentMethodDetails = ucfirst($cardDetails['brand'])." ending in {$cardDetails['last4']}";
            } catch (Exception $e) {
                app(AuthLogger::class)->error('PaymentSummaryMail: '.$e->getMessage());
            }
        }
    }
}
