<?php

namespace App\Modules\Domain\Jobs;

use App\Events\SystemLogEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\JobServices\JobRecord;
use App\Modules\Domain\Services\JobServices\JobUpdateDomainService;
use App\Modules\Histories\Constants\SystemTransactionType;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateEppDomain implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use UserLoggerTrait;

    private object $domain;

    private int $id;

    private object $registeredDomain;

    private string $registry;

    private int $userId;

    private string $updateType;

    private string $email;

    private JobRecord $jobRecord;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct($payload)
    {
        $this->domain = $payload[JobPayloadKeys::DOMAIN];
        $this->registeredDomain = $payload[JobPayloadKeys::REGISTERED_DOMAIN];
        $this->userId = $payload[JobPayloadKeys::USER_ID];
        $this->registry = $payload[JobPayloadKeys::REGISTRY];
        $this->email = $payload[JobPayloadKeys::EMAIL];
        $this->updateType = $payload[JobPayloadKeys::UPDATE_TYPE];

        $this->id = $this->domain->id;
        $this->jobRecord = $this->createRecord();

        $this->onConnection(QueueConnection::DOMAIN_UPDATE);
        $this->onQueue(QueueTypes::DOMAIN_UPDATE[$this->registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return intval(Carbon::now()->timestamp.$this->id);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            JobUpdateDomainService::instance()->handle($this->jobRecord);

            event(new SystemLogEvent(
                SystemTransactionType::DOMAIN_UPDATE,
                "Updated domain '{$this->domain->name}' in {$this->registry} registry by {$this->email}",
                null
            ));
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $this->email));
            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();

                return;
            }

            $this->fail();
        }
    }

    public function retry(): void
    {
        $this->jobRecord->setPendingJob();
    }

    private function createRecord()
    {
        return new JobRecord([
            JobPayloadKeys::DOMAIN => $this->domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $this->registeredDomain,
            JobPayloadKeys::REGISTRY => $this->registry,
            JobPayloadKeys::USER_ID => $this->userId,
            JobPayloadKeys::EMAIL => $this->email,
            JobPayloadKeys::UPDATE_TYPE => $this->updateType,
            JobPayloadKeys::QUEUE_TYPE => QueueConnection::DOMAIN_UPDATE,
        ]);
    }
}
