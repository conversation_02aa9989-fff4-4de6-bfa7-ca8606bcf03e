<?php

namespace App\Modules\Epp\Services;

use App\Modules\Epp\Constants\RegistryTransactionType;
use Illuminate\Support\Facades\DB;

class PollService
{
    public static function instance(): self
    {
        $pollService = new self;

        return $pollService;
    }

    public function store(array $data): void
    {
        if (! empty($data)) {
            DB::table('polls')->insert($data);
        }
    }

    public function getData(array $polls): array
    {
        $items = [];
        $domain_transfer = [];

        foreach ($polls as $poll) {
            $this->getPollPayload($items, $poll);
            $this->getTransferPayload($domain_transfer, $poll);
        }

        return ['storeData' => $items, 'domainTransferData' => $domain_transfer];
    }

    // PRIVATE Functions

    private function getPollPayload(array &$items, array $poll): void
    {
        $name = array_key_exists('name', $poll['summary']) ? $poll['summary']['name'] : '';

        $items[] = [
            'server_id' => $poll['id'],
            'type' => $poll['type'],
            'name' => $name,
            'status' => $poll['status'],
            'message' => $poll['message'],
            'acknowledge' => $poll['success'],
            'body' => json_encode($poll['body']),
            'json_data' => json_encode($poll),
            'created_at' => $poll['created'],
        ];
    }

    private function getTransferPayload(array &$domain_transfer, array $poll): void
    {
        if ($poll['type'] !== RegistryTransactionType::DOMAIN_TRANSFER) {
            return;
        }

        $domain_transfer[] = [
            'id' => $poll['id'],
            'status' => $poll['status'],
            'name' => strtolower($poll['summary']['name']),
        ];
    }
}
