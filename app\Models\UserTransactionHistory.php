<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTransactionHistory extends Model
{
    use HasFactory;

    protected $fillable = ['user_id', 'type', 'message', 'link', 'payload'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
