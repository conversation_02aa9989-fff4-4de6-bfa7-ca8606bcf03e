<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Modules\JobRetry\Constants\RetryJobStatus;
use App\Modules\JobRetry\Services\RetryJobService;
use App\Util\Constant\QueueConnection;
use App\Util\Helper\Domain\DomainParser;
use Exception;

class JobRecord
{
    public object $domain;

    public object $registeredDomain;

    public array $payload;

    public array $refundDetails;

    public string $registry;

    public string $email;

    public string $updateType;

    public string $queueType;

    public string $name;

    public int $userId;

    public int $domainId;

    public int $registeredDomainId;

    public int $registryId;

    public function __construct(array $payload)
    {
        $this->payload = $payload;

        $this->initialize();
    }

    public function initialize()
    {

        $this->domain = (object) $this->getData(JobPayloadKeys::DOMAIN, true);
        $this->registeredDomain = (object) $this->getData(JobPayloadKeys::REGISTERED_DOMAIN, true);
        $this->registry = $this->getData(JobPayloadKeys::REGISTRY);
        $this->userId = $this->getData(JobPayloadKeys::USER_ID);
        $this->email = $this->getData(JobPayloadKeys::EMAIL);
        $this->updateType = $this->getData(JobPayloadKeys::UPDATE_TYPE);
        $this->queueType = $this->getData(JobPayloadKeys::QUEUE_TYPE);

        $this->domainId = $this->domain->id;
        $this->name = $this->domain->name;
        $this->registeredDomainId = $this->registeredDomain->id;
        $this->registryId = DomainParser::getRegistryId($this->name);
        $this->refundDetails = $this->getData(JobPayloadKeys::REFUND_DATA) ?? [];
    }

    public function getRegisterRecord()
    {
        return [
            JobPayloadKeys::DOMAIN => $this->domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $this->registeredDomain,
            JobPayloadKeys::REGISTRY => $this->registry,
            JobPayloadKeys::USER_ID => $this->userId,
            JobPayloadKeys::EMAIL => $this->email,
            JobPayloadKeys::QUEUE_TYPE => QueueConnection::DOMAIN_REGISTRATION,
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::REGISTRATION,
            JobPayloadKeys::REFUND_DATA => $this->getData(JobPayloadKeys::REFUND_DATA),
        ];
    }

    public function getUpdateAfterRegisterRecord()
    {
        return [
            JobPayloadKeys::DOMAIN => $this->domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $this->registeredDomain,
            JobPayloadKeys::REGISTRY => $this->registry,
            JobPayloadKeys::USER_ID => $this->userId,
            JobPayloadKeys::EMAIL => $this->email,
            JobPayloadKeys::QUEUE_TYPE => QueueConnection::DOMAIN_UPDATE,
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_AFTER_REGISTER,
        ];
    }

    public function getRecord()
    {
        return [
            JobPayloadKeys::DOMAIN => $this->domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $this->registeredDomain,
            JobPayloadKeys::REGISTRY => $this->registry,
            JobPayloadKeys::USER_ID => $this->userId,
            JobPayloadKeys::EMAIL => $this->email,
            JobPayloadKeys::UPDATE_TYPE => $this->updateType,
            JobPayloadKeys::QUEUE_TYPE => $this->queueType,
            JobPayloadKeys::REFUND_DATA => $this->refundDetails ?? null,
        ];
    }

    public function getRecordByType()
    {
        switch ($this->updateType) {
            case DomainJobTypes::UPDATE_AFTER_REGISTER:
                return $this->getUpdateAfterRegisterRecord();
            case DomainJobTypes::REGISTRATION:
                return $this->getRegisterRecord();
            default:
                return $this->getRecord();
        }
    }

    public function stopJobRetry(string $status)
    {
        $this->storeRetryLogs($status);
    }

    public function setPendingJob()
    {
        DomainService::instance()->updateDomainStatus($this->domainId, DomainStatus::PENDING, false, $this->email);
        $this->storeRetryLogs(RetryJobStatus::PENDING);
    }

    // PRIVATE FUNCTIONS
    private function getData(string $key, bool $isRequired = false)
    {
        if ($isRequired && ! array_key_exists($key, $this->payload)) {
            $this->throwEmptyError($key);
        }

        return array_key_exists($key, $this->payload) ? $this->payload[$key] : null;
    }

    private function throwEmptyError(string $key)
    {
        throw new Exception('No data for key: '.$key);
    }

    // JOB RETRY

    private function storeRetryLogs(string $status): void
    {
        $data = [
            'job_id' => $this->domainId,
            'type' => $this->queueType,
            'status' => $status,
            'record' => $this->getRecordByType(),
        ];

        RetryJobService::instance()->store($data);
    }
}
