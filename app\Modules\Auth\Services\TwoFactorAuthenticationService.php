<?php

namespace App\Modules\Auth\Services;

use App\Models\AuthenticatorAppUserSessions;
use App\Models\EmailOtp;
use App\Models\User;
use Jenssegers\Agent\Agent;

class TwoFactorAuthenticationService
{
    /* TRAITS */
    // ?...

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Check if Two Factor is Enabled
     */
    public function checkIfUserTwoFactorAuthenticationIsEnabled(User $user): bool
    {
        if ($user->enabled_login_auth_options != null) {
            $options = json_decode($user->enabled_login_auth_options);

            if (
                in_array('email', $options)
                || in_array('authApp', $options)
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if User is already two factor authenticated
     */
    public function checkIfUserIsHasAnActiveTwoFactorAuthenticatedSession(User $user, string $ipAddress): bool
    {
        $agent = new Agent;

        // ! EMAIL OTP
        $emailOtp = EmailOtp::query()
            ->where('user_id', '=', $user->id)
            ->where('ip', '=', $ipAddress)
            ->where('user_agent', '=', $agent->browser())
            ->where('session_valid_until', '>=', now())
            ->first();

        if ($emailOtp != null) {
            return true;
        }

        // ! AUTHENTICATOR APP
        $authenticatorAppSession = AuthenticatorAppUserSessions::query()
            ->where('user_id', '=', $user->id)
            ->where('ip', '=', $ipAddress)
            ->where('user_agent', '=', $agent->browser())
            ->where('session_valid_until', '>=', now())
            ->first();

        if ($authenticatorAppSession != null) {
            return true;
        }

        // ! PASSCODE
        // ...

        return false;
    }
}
