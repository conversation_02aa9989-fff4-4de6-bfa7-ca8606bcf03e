//* PACKAGES
import React from "react";
import { router } from "@inertiajs/react";
import PrimaryButton from "@/Components/PrimaryButton";

export default function Item({ item }) {
    const formatDate = (dateString) => {
        if (!dateString) return "N/A";
        const date = new Date(dateString);
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        });
    };

    const formatCurrency = (amount) => {
        return `$${parseFloat(amount).toFixed(2)}`;
    };

    const handleCheckout = () => {
        router.post(route("domain-redemption.pay"), {
            user_id: item.user_id,
            domain_id: item.id
        });
    };

    const showCheckoutButton = !["In Process", "Completed"].includes(item.status);

    return (
        <tr className="hover:bg-gray-50">
            <td className="px-6 py-4 whitespace-nowrap">
                <span>{item.domain_name}</span>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <span>{formatCurrency(item.total_amount)}</span>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <span>{item.status}</span>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <span>{formatDate(item.valid_until)}</span>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                {showCheckoutButton && (
                    <PrimaryButton
                        onClick={handleCheckout}
                    >
                        Checkout
                    </PrimaryButton>
                )}
            </td>
        </tr>
    );
}