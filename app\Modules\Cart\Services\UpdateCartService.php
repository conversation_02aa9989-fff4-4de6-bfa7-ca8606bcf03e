<?php

namespace App\Modules\Cart\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UpdateCartService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $updateCartService = new self;

        return $updateCartService;
    }

    public function update(array $request): bool
    {
        return self::updateCart($request, $this);
    }

    public function softDelete(array $request): bool
    {
        return self::deleteCart($request, $this);
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function updateCart(array $request, self $updateCartService): bool
    {
        $isUpdated = false;

        $id = $request['id'];
        $column = $request['column'];
        $value = $request['value'];

        $updatedValues = [
            $column => $value,
            'updated_at' => now(),
        ];

        $isUpdated = self::updateDB($id, $updatedValues);

        app(AuthLogger::class)->info($updateCartService->fromWho('Update cart with id '.json_encode($id).' column '.json_encode($column).' with value '.json_encode($value)));

        return $isUpdated;
    }

    private function deleteCart(array $request, self $updateCartService): bool
    {
        $id = $request['id'];
        $delete_type = $request['delete_type'];

        $updatedValues = [
            'delete_type' => $delete_type,
            'deleted_at' => now(),
            'updated_at' => now(),
        ];

        $isDeleted = self::updateDB($id, $updatedValues);
        app(AuthLogger::class)->info($updateCartService->fromWho('Deleted from cart id: '.json_encode($id)));

        return $isDeleted;
    }

    private function getQuery(): Builder
    {
        return DB::table('carts')
            ->where('carts.user_id', self::getUserId())
            ->whereNull('carts.deleted_at');
    }

    private function updateDB($id, array $updatedValues): bool
    {
        return self::getQuery()->when(is_int($id), function (Builder $query) use ($id) {
            return $query->where('id', $id);
        })->when(is_array($id), function (Builder $query) use ($id) {
            return $query->whereIn('id', $id);
        })->update($updatedValues);
    }
}
