<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->timestamp('deleted_at')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->timestamp('deleted_at')->useCurrent()->change();
        });
    }
};
