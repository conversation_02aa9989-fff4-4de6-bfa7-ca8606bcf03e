import React from 'react';

export default function StripeOrderTotal({ total = 0, service_fee = 0 }) {
    return (
        <>
            <div className="flex items-left justify-between text-gray-600 ">
                <span className="font-semibold text-[15px]"> Stripe Fee </span>
                <span className='text-sm'>${Number(parseFloat(service_fee).toFixed(2)).toLocaleString('en', { useGrouping: true, minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
            </div>
            <div className="text-lg flex items-center justify-between mb-6">

                <span className="font-semibold text-gray-800">
                    Order Total
                </span>
                <span className="font-semibold text-gray-800">
                    ${Number(parseFloat(total).toFixed(2)).toLocaleString('en', { useGrouping: true, minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
            </div>
        </>

    );
}