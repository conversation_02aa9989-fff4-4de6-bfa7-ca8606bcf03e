<?php

namespace App\Modules\Auth\Controllers\AuthenticatorApp;

use App\Http\Controllers\Controller;
use App\Modules\Auth\Requests\TwoFactorAuthenticatorAppResetVerificationFormRequest;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppResetService;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

class AuthenticatorAppResetController extends Controller
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Render Page
     */
    public function renderPage(string $token): InertiaResponse
    {
        return Inertia::render(
            'Auth/ResetAuthenticatorApp',
            (new AuthenticatorAppResetService)->processIndexData($token, Auth::user()->id)
        );
    }

    /**
     * Verify Code
     */
    public function verifyCode(TwoFactorAuthenticatorAppResetVerificationFormRequest $request, string $token): RedirectResponse
    {
        return (new AuthenticatorAppResetService)
            ->verifyCode($request->only('secretKey', 'code'), Auth::user()->id);
    }

    /**
     * Disable
     */
    public function disable()
    {
        $isDisabled = (new AuthenticatorAppService)->disable(Auth::user()->id);

        if ($isDisabled == true) {
            return Redirect::route('domain');
        }
    }
}
