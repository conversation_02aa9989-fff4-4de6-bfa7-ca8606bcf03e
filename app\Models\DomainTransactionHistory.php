<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DomainTransactionHistory extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'domain_id',
        'type',
        'user_id',
        'status',
        'message',
        'payload',
    ];

    /**
     * Get the domain that owns the transaction history.
     */
    public function domain()
    {
        return $this->belongsTo(Domain::class);
    }

    /**
     * Get the user that owns the transaction history.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
