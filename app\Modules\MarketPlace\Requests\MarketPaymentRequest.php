<?php

namespace App\Modules\MarketPlace\Requests;

use App\Exceptions\FailedRequestException;
use App\Exceptions\InsufficientBalanceException;
use App\Models\Domain;
use App\Models\MarketCart;
use App\Modules\Cart\Services\CheckoutCartService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\MarketPlace\Services\AfternicMiddleware;
use App\Modules\MarketPlace\Services\MarketPaymentService;
use App\Modules\MarketPlace\Services\Payments\MarketInvoiceService;
use App\Modules\Payment\Constants\CheckoutType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\RegisteredDomain\Services\DomainRegistrationService;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Rules\Payment\ValidateOtherFees;
use App\Traits\UserContact;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\Domain\DomainTld;
use App\Util\Helper\RateLimit;
use App\Util\Helper\Store\BulkActions;
use Carbon\Carbon;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class MarketPaymentRequest extends FormRequest
{
    use UserContact, UserLoggerTrait;

    private $dispatchDelayInSeconds = 60;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return $this->getValidationRules();
    }

    public function prepareForValidation()
    {
        $this->merge(['payment_summary_type' => PaymentSummaryType::MARKETPLACE_INVOICE]);
    }

    public function passedValidation()
    {
        if ($this->payment_service_type === PaymentServiceType::ACCOUNT_CREDIT) {
            CheckoutCartService::instance()->checkAccountCreditBalance($this->amount_to_use);
        }
    }

    protected function failedValidation(Validator $validator)
    {
        if (($this->payment_service_type === PaymentServiceType::STRIPE) && $this->input('intent')) {
            PaymentIntentProvider::instance()->cancelIntent($this->input('intent'));
        }
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    public function store(): void
    {
        MarketPaymentService::instance()->storeSingleCheckout($this->all());
        // $transferPrice = floatval($this->other_fees['transfer_total'] + $this->other_fees['icann_fee']);
        // $this->checkRegistryBalance($transferPrice, $this->domains[0]['tld_id']); // NEW

        // AfternicMiddleware::instance()::placeHold($this->domains[0]['domain']);

        // RateLimit::clear(RateLimiterKey::stripeAttempt(Auth::user()->id));

        // $this->storeTransactionInfo();
    }

    // PRIVATE functions

    private function getValidationRules(): array
    {
        $rules = [
            'user_id' => 'required',
            'domains' => ['required', 'array'],
            'other_fees' => ['required', 'array', 'min:1', new ValidateOtherFees(CheckoutType::MARKET_TRANSFER)],
            'payment_service_type' => ['required', Rule::in([PaymentServiceType::STRIPE, PaymentServiceType::ACCOUNT_CREDIT])],
            'payment_summary_type' => ['required', Rule::in(PaymentSummaryType::ALL)],
        ];

        return match ($this->payment_service_type) {
            PaymentServiceType::STRIPE => array_merge($rules, ['intent' => ['required', 'string']]),
            PaymentServiceType::ACCOUNT_CREDIT => array_merge($rules, ['amount_to_use' => ['required', 'numeric']]),
            default => $rules
        };
    }

    // private function storeTransactionInfo(): void
    // {
    //     $registeredDomains = $this->registerDomainProcess();
    //     MarketInvoiceService::instance()->createPaymentSummary($this->all(), $registeredDomains);
    //     $this->removeFromCart();
    // }

    // public function registerDomainProcess(): Collection
    // {
    //     $domainData = $this->createDomainPayload();
    //     $createdDomains = $this->getCreatedDomains([$domainData]);
    //     $domainService = new DomainRegistrationService((int) $this->user_id);
    //     $registeredDomains = $domainService->store($createdDomains, UserDomainStatus::RESERVED);

    //     return $registeredDomains;
    // }

    // private function createDomainPayload(): array
    // {
    //     $tlds = DomainTld::getAllTlds();
    //     $tldext = DomainTld::getTldsByExtension();
    //     $contacts = $this->getDefaultContacts($this->user_id);

    //     $exp = explode('.', $this->domains[0]['domain']);
    //     $tld = array_pop($exp);

    //     $tldId = $tldext[$tld]->id;
    //     $registryId = $tlds[$tldId]['registry_id'];

    //     $domain = [
    //         'name' => strtolower($this->domains[0]['domain']),
    //         'status' => DomainStatus::IN_PROCESS,
    //         'root' => $tldId,
    //         'registrant' => $contacts[$registryId]['name'][DomainContact::REGISTRANT],
    //         'year_length' => 1,
    //         'expiry' => Carbon::now()->addYears()->valueOf(),
    //         'contacts' => json_encode($contacts[$registryId]['name']),
    //         'created_at' => Carbon::now(),
    //         'updated_at' => Carbon::now(),
    //         'server_renew_at' => Carbon::now(),
    //         'client_renew_at' => Carbon::now(),
    //     ];

    //     return $domain;
    // }

    // private function getCreatedDomains(array $domainData): Collection
    // {
    //     $table = (new Domain)->getTable();
    //     $domainsCreated = BulkActions::instance()->bulkInsertGetData($table, $domainData, false);

    //     $createdDomainObj = collect();

    //     foreach ($domainsCreated as $item) {
    //         $createdDomain = $item;
    //         $createdDomain->privacy = true;
    //         $createdDomainObj->push($createdDomain);
    //     }

    //     $domainNames = $domainsCreated->pluck('name')->toArray();

    //     app(AuthLogger::class)->info($this->fromWho('MarketPaymentRequest: Created domains ' . implode(',', $domainNames)));

    //     return $createdDomainObj;
    // }

    // private function removeFromCart(): void
    // {
    //     MarketCart::where('name', strtolower($this->domains[0]['domain']))->where('user_id', $this->user_id)->delete();
    // }

    // private function checkRegistryBalance(float $price, int $tld_id)
    // {
    //     $tlds = DomainTld::getTldsById();
    //     $registryId = $tlds[$tld_id]->registry_id;
    //     $balance = RegistryAccountBalanceService::balance($registryId);

    //     if ($price > $balance->balance) {
    //         throw new InsufficientBalanceException(503, "We're currently undergoing maintenance. Marketplace transactions are temporarily unavailable. Please bear with us. Thank you for your patience.", 'System Maintenance in Progress');
    //     }

    //     RegistryAccountBalanceService::credit($balance, $price, RegistryTransactionType::SUB_FUND, RegistryTransactionType::MARKETPLACE_TRANSFER);
    // }
}
