<?php

namespace App\Modules\Auth\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Auth\Constants\InviteStatus;
use App\Modules\Auth\Services\RegisterUserService;
use App\Rules\ValidFormat;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;

class InviteAccountRequest extends FormRequest
{
    // protected $redirect = 'bad-request';

    public function prepareForValidation()
    {
        if ($this->route('data')) {
            $this->merge(['uuid' => $this->route('data')]);
        }

        $user = RegisterUserService::getUserInvited($this->all());
        if ($user) {
            $options = RegisterUserService::userInviteOptions($user->additional_options);
            $this->merge([
                'user_id' => $user->user_id,
                'email' => $user->email,
                'ip' => ClientIp::getClientIp($this),
                'disable_verification' => $options->disable_verification,
                'disable_deposit' => $options->disable_deposit,
                'balance' => $options->balance,
            ]);
        }
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {

        $user = RegisterUserService::authorize($this->all());

        if (!$user) {
            throw new FailedRequestException(410, 'Link has expired.', 'Gone');
        }

        if ($user->status === InviteStatus::ACCEPTED) {
            throw new FailedRequestException(410, 'This invitation link has already been used.', 'Gone');
        }

        if (now()->timestamp > Carbon::parse($user->expires_at)->timestamp) {
            throw new FailedRequestException(410, 'Link has expired.', 'Gone');
        }

        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email:rfc,dns', "max:255", "regex:/^[0-9a-zA-Z.@_-]*$/"],
            'ip' => ['required', 'string', 'min:1', "max:16"],
        ];
    }

    public function messages(): array
    {
        return [
            'email.unique' => 'Link has expired.',
        ];
    }
}
