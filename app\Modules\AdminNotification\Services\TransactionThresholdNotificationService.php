<?php

namespace App\Modules\AdminNotification\Services;

use App\Modules\AdminNotification\Services\AdminNotificationHandler;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Notification\Constants\NotificationType;
use App\Util\Constant\Transaction;

class TransactionThresholdNotificationService extends AdminNotificationService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $transactionThresholdNotificationService = new self;

        return $transactionThresholdNotificationService;
    }

    public function sendAdminUserLimitExceededNotif(array $adminIds, array $notifData): void
    {
        $notification = AdminNotificationHandler::Create();
        $transaction = Transaction::TYPES[$notifData['transactionType']];
        $user = strtoupper($notifData['userEmail']);
        $allowed = $notifData['isAllowed'];
        $message = "The user {$user} has exceeded the user {$transaction} limit." .
            ($allowed ? " The transaction was still allowed to proceed." : " The transaction has been rejected.");
        $log = $this->fromWho("was " . ($allowed ? "allowed for" : "rejected for") . " {$transaction} " .
            ($allowed ? "despite" : "due to") . " exceeding the user limit.");

        foreach ($adminIds as $adminId) {
            $notification->addPayload($adminId, "User Limit Exceeded", $message, $notifData['route'], NotificationType::IMPORTANT);
        }

        app(AuthLogger::class)->info($log);
        $notification->store();
    }

    public function sendAdminSystemLimitExceededNotif(array $adminIds, array $notifData): void
    {
        $notification = AdminNotificationHandler::Create();
        $transaction = Transaction::TYPES[$notifData['transactionType']];
        $user = strtoupper($notifData['userEmail']);
        $allowed = $notifData['isAllowed'];
        $message = "The user {$user} has exceeded the system {$transaction} limit." .
            ($allowed ? " The transaction was still allowed to proceed." : " The transaction has been rejected.");
        $log = $this->fromWho("was " . ($allowed ? "allowed for" : "rejected for") . " {$transaction} " .
            ($allowed ? "despite" : "due to") . " exceeding the system limit.");

        foreach ($adminIds as $adminId) {
            $notification->addPayload($adminId, "System Limit Exceeded", $message, $notifData['route'], NotificationType::IMPORTANT);
        }

        app(AuthLogger::class)->info($log);
        $notification->store();
    }
}
