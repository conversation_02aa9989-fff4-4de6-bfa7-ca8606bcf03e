<?php

namespace App\Modules\Notification\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Notification\Constants\NotificationType;
use App\Util\Constant\TableTypes;

class ContactNotificationService extends NotificationService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $contactNotificationService = new self;

        return $contactNotificationService;
    }

    public function sendContactInProcessNotif(string $userId, array $notifsData): void
    {
        $notification = NotificationHandler::For($userId);

        foreach ($notifsData as $notifData) {
            $message = 'Contact ID "'.strtoupper($notifData['contact']).'" for "'.strtoupper($notifData['registry']).'" is currently being processed. Please wait for further updates.';
            $notification->addPayload('Contact is In Process', $message, 'contact', NotificationType::IMPORTANT);
            app(AuthLogger::class)->info($this->fromWho('created a new contact for '.$notifData['registry'].' with registry contact '.$notifData['contact'].'.'));
        }

        $notification->store();
    }

    public function sendContactCreatedNotif(array $notifData): void
    {
        $message = 'Contact ID "'.strtoupper($notifData['contact']).'" for "'.strtoupper($notifData['registry']).'" was successfully created. Click to view your contacts.';
        $notification = NotificationHandler::For($notifData['user_id']);
        $notification->addPayload('Contact Registration Success', $message, 'contact', NotificationType::MEDIUM);
        $notification->store()->notifySelf()->updatePages(TableTypes::CONTACT);
        app(AuthLogger::class)->info($this->fromWho('successfully created a new contact for '.$notifData['registry'].' with registry contact '.$notifData['contact'].'.', $notifData['email']));
    }

    public function sendContactUpdatedNotif(array $notifData): void
    {
        $message = 'Contact ID "'.strtoupper($notifData['contact']).'" for "'.strtoupper($notifData['registry']).'" was successfully updated. Click to view contacts.';
        $notification = NotificationHandler::For($notifData['user_id']);
        $notification->addPayload('Contact Update Success', $message, 'contact', NotificationType::MEDIUM);
        $notification->store()->notifySelf()->updatePages(TableTypes::CONTACT);
        app(AuthLogger::class)->info($this->fromWho('successfully updated contact for '.$notifData['registry'].' with registry contact '.$notifData['contact'].'.', $notifData['email']));
    }
}
