//* PACKAGES
import React from "react";
import { Head, Link, router, usePage } from "@inertiajs/react";

//* ICONS
//..

//* COMPONENTS
import AppLogoWithTextComponent from "@/Components/App/AppLogoWithTextComponent";
import PrimaryButton from "@/Components/PrimaryButton";
import UserLayout from "@/Layouts/UserLayout";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PaymentMethodCreateSuccessPageWireTransfer({
    initialSetup = false,
    message,
    title,
}) {
    //! PACKAGE
    //...

    //! VARIABLES
    //...

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleGoBack() {
        router.get(
            route("account.balance.index"),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
                onSuccess: () => {
                    //...
                },
                onError: () => toast.error("Something went wrong!"),
            }
        );
    }

    function handleOnInitialSetupFinish() {
        router.get(
            route("domain"),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
                onSuccess: () => {
                    //...
                },
                onError: () => toast.error("Something went wrong!"),
            }
        );
    }

    return (
        <UserLayout
            postRouteName="identity.confirm"
            setup={false}
            additionalMainElementClassNames="bg-gray-100"
        >
            <Head title="Account Funds Success" />

            <div>
                <div className="mx-auto container max-w-[800px] text-center pt-14 flex justify-center items-center gap-4">
                    {/* Image */}
                    <div className="w-auto h-[10rem] flex-shrink-0">
                        <img
                            className="h-full w-auto"
                            src="/assets/images/order_confirmed.svg"
                            alt="background"
                        />
                    </div>

                    {/* Text */}
                    <div className="flex flex-col items-start pl-5 flex-1 text-left">
                        <span className="text-xl font-semibold">Funds Submitted for Verification</span>
                        <span className="text-l break-words">Your fund request has been received and is currently pending admin review. You'll be notified once it's verified and credited to your account.</span>
                    </div>
                </div>

                <div className="w-full text-center flex flex-col items-center pt-12 space-y-7">
                    {initialSetup ? (
                        <button
                            onClick={handleOnInitialSetupFinish}
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none"
                        >
                            Proceed to next setup
                        </button>
                    ) : (
                        <button
                            onClick={handleGoBack}
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none"
                        >
                            Go Back to Account Balance
                        </button>
                    )}
                </div>
            </div>
        </UserLayout>
    );
}
