<?php

namespace App\Modules\MarketPlace\Services;

use App\Modules\MarketPlace\Jobs\AfternicManualTransferJob;
use Illuminate\Support\Facades\DB;

class AfternicManualService
{
    public static function instance()
    {
        $afternicmanualservice = new self;

        return $afternicmanualservice;
    }

    public function getFailedDomains(): \Illuminate\Support\Collection
    {
        return DB::table('public.market_domains_manual')->get();
    }

    public function initiateManualTransfer($domain, $authcode): void
    {
        $query = DB::table('market_place_domains')
            ->select('users.email', 'market_place_domains.order_id')
            ->join('registered_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('users', 'market_place_domains.user_id', '=', 'users.id')
            ->where('domains.name', $domain)
            ->first();

        AfternicManualTransferJob::dispatch($domain, $authcode, 1, $query->email, $query->order_id);
    }
}
