<?php

namespace App\Modules\Guest\Requests;

use App\Modules\Guest\Services\GuestRequestService;
use App\Rules\ValidFormat;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RegistrationAccessForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email:rfc,dns',
                new ValidFormat,
                Rule::unique('users')->where(function ($query) {
                    $query->whereNull('deleted_at');
                }),
            ],
            'message' => ['required', 'string', new ValidFormat],
        ];
    }

    public function send()
    {
        GuestRequestService::instance()->storeRegistrationAccessRequest($this);
    }
}
