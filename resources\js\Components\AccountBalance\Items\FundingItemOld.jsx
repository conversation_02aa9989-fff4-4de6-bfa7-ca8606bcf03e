import React from "react";
import setDefaultDateFormat from "../../../Util/setDefaultDateFormat";

export const FundingItemOld = ({ item }) => {
    console.log(item);

    const getStatus = () => {
        if (item.deleted_at) {
            return "Rejected";
        } else if (item.verified_at) {
            return "Verified";
        } else if (item.reviewed_at && !item.deleted_at) {
            return "Unverified";
        } else if (!item.reviewed_at && !item.deleted_at) {
            return "Pending";
        }
    };

    return (
        <>
            <div className="mx-auto container max-w-[400px] flex flex-col space-y-2">
                <div className="flex items-center justify-between">
                    <span className="flex items-center space-x-2 text-xl">
                        ${parseFloat(item.amount).toFixed(2)}
                    </span>
                    <span className="flex items-center space-x-2 text-xl">
                        {getStatus()}
                    </span>
                </div>
                <div className="border-b border-gray-200">
                    <span className="flex items-center space-x-2">
                        <span className="text-gray-700">
                            {setDefaultDateFormat(item.created_at)}{" "}
                            {new Date(
                                item.created_at + "Z"
                            ).toLocaleTimeString()}
                        </span>
                    </span>
                </div>
            </div>
        </>
    );
};
