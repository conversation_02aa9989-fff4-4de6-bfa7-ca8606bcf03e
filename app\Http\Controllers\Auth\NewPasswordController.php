<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Modules\UserProfile\Requests\PasswordResetRequest;
use App\Modules\UserProfile\Requests\PasswordResetStoreRequest;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class NewPasswordController extends Controller
{
    /**
     * Display the password reset view.
     */
    public function create(PasswordResetRequest $request): Response
    {
        $status = $request->checkToken();

        if (strcmp($status, 'valid') === 0) {
            return Inertia::render('Auth/ResetPassword', [
                'email' => $request->email,
                'token' => $request->route('token'),
            ]);
        }

        $props = [];

        if (strcmp($status, 'limited') === 0) {
            $props['code'] = 429;
            $props['error'] = 'Too Many Requests';
            $props['help'] = 'Come back a little while.';
        } else {
            $props['code'] = 401;
            $props['error'] = 'Unauthorized';
            $props['help'] = 'Link has expired.';
        }

        return Inertia::render('Errors/CustomMessage', $props);
    }

    /**
     * Handle an incoming new password request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(PasswordResetStoreRequest $request): RedirectResponse
    {
        // Here we will attempt to reset the user's password. If it is successful we
        // will update the password on an actual user model and persist it to the
        // database. Otherwise we will parse the error and return the response.
        $status = $request->reset();

        // If the password was successfully reset, we will redirect the user back to
        // the application's home authenticated view. If there is an error we can
        // redirect them back to where they came from with their error message.
        if ($status == Password::PASSWORD_RESET) {
            return redirect()->route('login')->with('status', __($status));
        }

        if (strcmp($status, 'passwords.token') === 0) {
            return redirect()
                ->route('invalid-password-reset-token')
                ->withErrors(['message' => 'Password reset link has expired.']);
        }

        if (strcmp($status, 'passwords.user') === 0) {
            return redirect()
                ->route('bad-request')
                ->withErrors(['email' => 'Request is invalid.']);
        }

        // throw ValidationException::withMessages([
        //     'email' => [trans($status)],
        // ]);
    }
}
