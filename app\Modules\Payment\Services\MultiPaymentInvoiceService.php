<?php

namespace App\Modules\Payment\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\MarketPlace\Services\Payments\MarketInvoiceService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;

class MultiPaymentInvoiceService
{
    use CursorPaginate, UserLoggerTrait;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $multiPaymentInvoiceService = new self;

        return $multiPaymentInvoiceService;
    }

    public function getDataBySummaryId(int $summaryId, int $userId)
    {
        $domainInvoiceData = [];
        $marketInvoiceData = [];
        $domainSubTotal = 0;
        $marketSubTotal = 0;
        $allData = [];
        $allData['data'] = [];

        $summaryData = PaymentSummaryService::instance()->getById($summaryId, $userId);

        if (! $summaryData) {
            throw new FailedRequestException(404, 'Nothing to process.', 'Data empty');
        }
        // get registered domains
        if ($summaryData->payment_invoice_id) {
            $domainInvoiceData = PaymentInvoiceService::instance()->getInvoiceData($summaryData->payment_invoice_id, $userId);
            $domainSubTotal = $domainInvoiceData['data'][0]->invoice_paid_amount ?? 0;
        }
        // get marketplace domains
        if ($summaryData->payment_market_place_invoice_id) {
            $marketInvoiceData = MarketInvoiceService::instance()->getInvoiceData($summaryData->payment_market_place_invoice_id, $userId);
            $marketSubTotal = $marketInvoiceData[0]->invoice_paid_amount ?? 0;
        }

        if (! empty($domainInvoiceData)) {
            $allData['data'] = $domainInvoiceData['data'];
        }

        if (! empty($marketInvoiceData)) {
            $allData['data'] = array_merge($allData['data'], $marketInvoiceData);
        }

        // dd($domainInvoiceData, $marketInvoiceData);

        $allData['node_type'] = $summaryData->name;
        $allData['paid_amount'] = $summaryData->paid_amount;
        $allData['paymentIntent'] = $this->getStripePaymentIntent($allData['data'][0]);
        $allData['summary_type'] = PaymentSummaryType::MULTI_CHECKOUT_INVOICE;
        $allData['summaryData'] = $summaryData;
        $allData['registration_subtotal'] = $domainSubTotal;
        $allData['premium_subtotal'] = $marketSubTotal;

        $totalFees = $summaryData->paid_amount - ($domainSubTotal + $marketSubTotal);
        $allData['total_fees'] = ($totalFees >= $summaryData->paid_amount) ? 0 : $totalFees;

        return $allData;
    }

    private function getStripePaymentIntent(object $invoice)
    {
        if (! $invoice->stripe_id) {
            return null;
        }

        $stripeTransaction = DB::table('stripe_transactions')
            ->where('id', $invoice->stripe_id)
            ->get()->first();

        return $stripeTransaction->payment_intent ?? null;
    }
}
