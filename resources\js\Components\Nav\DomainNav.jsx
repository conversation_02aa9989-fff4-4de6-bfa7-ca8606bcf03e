import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/md";
import NavLink from "@/Components/NavLink";
import SideNavState from "@/State/SideNavState";
import { SlHandbag } from "react-icons/sl";
import { BiSolidOffer } from "react-icons/bi";
import { FaTrashRestoreAlt } from "react-icons/fa";

export default function DomainNav({ postRouteName }) {
    const currentRoute = route().current() || postRouteName;

    const routes = {
        domain_search: currentRoute && currentRoute == 'domain.search',
        my_domain    : currentRoute && currentRoute.includes('domain') && (currentRoute != 'domain.search') && (currentRoute != 'user-domain-export.index') && (currentRoute != 'domains.products') && (currentRoute != 'domain-redemption'),
        products     : currentRoute && currentRoute.includes('products') && (currentRoute != 'domain.search') && (currentRoute != 'domain'),
        offers       : currentRoute && currentRoute.includes('offers') && (currentRoute == 'offers') && (currentRoute == 'offers'),
        restore      : currentRoute && currentRoute.includes('domain-redemption'),
    };

    const { showPrefNav, setShowPrefNav } = SideNavState(Object.values(routes).includes(true));

    const visible = () => {
        return !showPrefNav ? " hidden" : "";
    };

    return (
        <>
            <NavLink
                href={route("domain.search")}
                active={routes.domain_search}
            >
                <span className='flex space-x-4'>
                    <MdOutlineSearch className="text-2xl" />
                    <span className="text-inherit">Get Domain</span>
                </span>

            </NavLink>

            <NavLink
                href={route("domain")}
                active={routes.my_domain}
            >
                <span className='flex space-x-4'>
                    <MdOutlineLanguage className="text-2xl" />
                    <span className="text-inherit"> My Domain</span>
                </span>
            </NavLink>
            <NavLink
                href={route("domains.products")}
                active={routes.products}
            >
                <span className='flex space-x-4'>
                    <SlHandbag className="text-xl" />
                    <span className="pl-1"> My Products</span>
                </span>
            </NavLink>
            {/* <NavLink
                href={route("domain")}
                active={routes.restore}
            >
                <span className='flex space-x-4'>
                    <FaTrashRestoreAlt className="text-xl" />
                    <span className="pl-1">Restore Domains</span>
                </span>
            </NavLink> */}
        </>
    );
}
