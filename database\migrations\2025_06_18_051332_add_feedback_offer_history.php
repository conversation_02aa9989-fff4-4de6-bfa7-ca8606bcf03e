<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // check new columns
        if (Schema::hasColumn('afternic_offer_histories', 'feedback')) {
            echo 'Column "feedback" of relation "afternic_offer_histories" already exists...'.PHP_EOL;

            return;
        }

        Schema::table('afternic_offer_histories', function (Blueprint $table) {
            $table->string('feedback')->default('')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
