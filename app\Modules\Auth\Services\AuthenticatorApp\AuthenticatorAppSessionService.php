<?php

namespace App\Modules\Auth\Services\AuthenticatorApp;

use App\Models\AuthenticatorAppUserSessions;
use App\Models\User;
use Illuminate\Support\Str;
use Jen<PERSON><PERSON>\Agent\Agent;
use PragmaRX\Google2FA\Google2FA;

class AuthenticatorAppSessionService
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Generate Entry
     */
    public function generateEntry(User $user, string $ipAddress): array
    {
        $agent = new Agent;

        // ! Transfered to Command
        // AuthenticatorAppUserSessions::query()
        //     ->where('user_id', '=', $user->id)
        //     ->where('session_valid_until', '=', null)
        //     ->delete();

        $userAgent = $agent->browser();
        $sessionToken = Str::random(32);

        AuthenticatorAppUserSessions::create(
            [
                'user_id' => $user->id,
                'ip' => $ipAddress,
                'session_token' => $sessionToken,
                'user_agent' => $agent->browser(),
            ]
        );

        return compact('userAgent', 'sessionToken');
    }

    /**
     * Verify Code
     */
    public function verifyCode(array $data, string $token): ?int
    {
        $google2FA = new Google2FA;
        $session = AuthenticatorAppUserSessions::where('session_token', '=', $token)->first();

        $isVerified = (new AuthenticatorAppService)->verifyCode($session->user->authenticator_app_secret_key, $data['code']);

        if ($isVerified == true) {
            $session->session_valid_until = now()->addDays(30);
            $session->save();

            return $session->user_id;
        }

        return null;
    }
}
