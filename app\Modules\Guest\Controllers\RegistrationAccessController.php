<?php

namespace App\Modules\Guest\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Guest\Requests\RegistrationAccessForm;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Http\Request;
use Inertia\Inertia;

class RegistrationAccessController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Guest/RegistrationAccess', ['ip' => ClientIp::getClientIp($request)]);
    }

    public function store(RegistrationAccessForm $request)
    {
        $request->send();

        return Inertia::render('Notice/GuestMessage', ['message' => "Request sent. We will send you a link once we've confirmed your request."]);
    }
}
