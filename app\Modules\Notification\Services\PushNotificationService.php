<?php

namespace App\Modules\Notification\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Notification\Constants\NotificationType;
use App\Util\Constant\TableTypes;

class PushNotificationService extends NotificationService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $pushNotificationService = new self;

        return $pushNotificationService;
    }

    public function sendPushRequestNotif(string $userReceiverId, string $receiverEmail, string $domainString): void
    {
        $wordDomain = $this->domainText($domainString);
        $domainStringTruncated = $this->truncateDomainString($domainString, 2);
        $message = 'You have a push request for the '.$wordDomain.$domainStringTruncated.' from '.auth()->user()->email.'. Please click to view the request and take appropriate action.';
        $notification = NotificationHandler::For($userReceiverId)->addPayload('Push Request Received', $message, 'incoming', NotificationType::IMPORTANT);
        $notification->store()->notifySelf()->updatePages(TableTypes::INCOMING_PUSH);
        app(AuthLogger::class)->info($this->fromWho('sent a push request for the '.$wordDomain.$domainString.' to '.$receiverEmail.'.'));
    }

    public function sendPushAcceptNotif(array $notifsData, string $email, string $userId): void
    {
        $notification = NotificationHandler::For($userId)->addPageUpdatePayload([TableTypes::INCOMING_PUSH, TableTypes::DOMAIN]);
        $userIds = array_column($notifsData, 'userId');

        $this->handlePushAcceptNotifPayloads($notifsData, $email, $notification);
        $notification->store()->notifyOthers($userIds)->dispatchPageUpdates();
    }

    public function sendPushDeclineNotif(array $notifsData, string $email): void
    {
        $notification = NotificationHandler::Create();
        $userIds = array_column($notifsData, 'userId');

        $this->handlePushDeclineNotifPayloads($notifsData, $email, $notification);
        $notification->store()->notifyOthers($userIds)->dispatchPageUpdates();
    }

    public function sendPushCancelNotif(array $notifsData, string $email): void
    {
        $notification = NotificationHandler::Create();
        $userIds = array_column($notifsData, 'userId');

        $this->handlePushCancelNotifPayloads($notifsData, $email, $notification);
        $notification->store()->notifyOthers($userIds)->dispatchPageUpdates();
    }

    public function sendPushExpiredNotif(array $notifsData, string $email): void
    {
        $notification = NotificationHandler::Create();
        $userIds = array_column($notifsData, 'userId');

        $this->handlePushExpiredNotifPayloads($notifsData, $email, $notification);
        $notification->store()->notifyOthers($userIds)->dispatchPageUpdates();
    }

    // PRIVATE Functions

    private function handlePushAcceptNotifPayloads(array $notifsData, string $email, NotificationHandler &$notification): void
    {
        foreach ($notifsData as $receiverUserId => $item) {
            $wordDomain = $this->domainText($item['domainString']);
            $domainStringTruncated = $this->truncateDomainString($item['domainString'], 2);
            $message = 'Your push request for the '.$wordDomain.$domainStringTruncated.' to '.$item['senderEmail'].' was approved. Click to check your current domains.';
            $notification->addPayload('Push Request Approved', $message, 'domain', NotificationType::MEDIUM, null, $receiverUserId);
            // $notification->addPayload('Push Request Approved', $message, 'domain', NotificationType::MEDIUM);
            $notification->addPageUpdatePayload([TableTypes::OUTGOING_PUSH, TableTypes::DOMAIN], $receiverUserId);
            app(AuthLogger::class)->info($this->fromWho('approved the push request from '.$item['receiverEmail'].' for the '.$wordDomain.$item['domainString'].'.', $email));
        }
    }

    private function handlePushDeclineNotifPayloads(array $notifsData, string $email, NotificationHandler &$notification): void
    {
        foreach ($notifsData as $userId => $item) {
            $wordDomain = $this->domainText($item['domainString']);
            $domainStringTruncated = $this->truncateDomainString($item['domainString'], 2);
            $message = 'Sorry, your push request for the '.$wordDomain.$domainStringTruncated.' to '.$item['senderEmail'].' was declined. Click to send another push request.';
            $notification->addPayload('Push Request Declined', $message, 'outgoing', NotificationType::IMPORTANT, null, $userId);
            // $notification->addPayload('Push Request Declined', $message, 'outgoing', NotificationType::IMPORTANT);
            $notification->addPageUpdatePayload([TableTypes::OUTGOING_PUSH, TableTypes::DOMAIN], $userId);
            app(AuthLogger::class)->info($this->fromWho('declined the push request from '.$item['receiverEmail'].' for the '.$wordDomain.$item['domainString'].'.', $email));
        }
    }

    private function handlePushCancelNotifPayloads(array $notifsData, string $email, NotificationHandler &$notification): void
    {
        foreach ($notifsData as $userId => $item) {
            $wordDomain = $this->domainText($item['domainString']);
            $domainStringTruncated = $this->truncateDomainString($item['domainString'], 2);
            $message = 'Sorry, the push request for the '.$wordDomain.$domainStringTruncated.' from '.$item['senderEmail'].' was canceled.';
            $notification->addPayload('Push Request Canceled', $message, 'incoming', NotificationType::IMPORTANT, null, $userId);
            // $notification->addPayload('Push Request Canceled', $message, 'incoming', NotificationType::IMPORTANT);
            $notification->addPageUpdatePayload(TableTypes::INCOMING_PUSH, $userId);
            app(AuthLogger::class)->info($this->fromWho('canceled the push request to '.$item['receiverEmail'].' for the '.$wordDomain.$item['domainString'].'.', $email));
        }
    }

    private function handlePushExpiredNotifPayloads(array $notifsData, string $email, NotificationHandler &$notification): void
    {
        foreach ($notifsData as $userId => $item) {
            $wordDomain = $this->domainText($item['domainString']);
            $domainStringTruncated = $this->truncateDomainString($item['domainString'], 2);
            $message = 'Sorry, the push request for the '.$wordDomain.$domainStringTruncated.' from '.$item['senderEmail'].' has already expired and you can no longer respond to this request. Click to send new push request.';
            $notification->addPayload('Push Request Expired', $message, 'incoming', NotificationType::IMPORTANT, null, $userId);
            // $notification->addPayload('Push Request Expired', $message, 'incoming', NotificationType::IMPORTANT);
            $notification->addPageUpdatePayload(TableTypes::INCOMING_PUSH, $userId);
            app(AuthLogger::class)->info($this->fromWho('push request to '.$item['receiverEmail'].' for the '.$wordDomain.$item['domainString'].' has been expired.', $email));
        }
    }

    private function truncateDomainString(string $domainString, int $limit): string
    {
        $domains = explode(', ', $domainString);
        $totalDomains = count($domains);

        if ($totalDomains > $limit) {
            $firstTwo = array_slice($domains, 0, $limit);
            $remainingCount = $totalDomains - $limit;
            $otherText = $remainingCount === 1 ? 'other' : 'others';

            return implode(', ', $firstTwo).", and ({$remainingCount}) {$otherText}";
        }

        return $domainString;
    }

    private function domainText(string $domainString): string
    {
        $domains = explode(', ', $domainString);

        return count($domains) > 1 ? 'domains ' : 'domain ';
    }
}
