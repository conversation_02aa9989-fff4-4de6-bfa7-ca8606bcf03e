<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Modules\Epp\Constants\EppDomainStatus;

class CancellationPayload
{
    public object $domain;

    public string $name;

    public string $registrant;

    public function __construct(object $domain)
    {
        $this->domain = $domain;
        $this->name = $domain->name;
        $this->registrant = $domain->registrant;
    }

    public function get(): array
    {
        return [
            'name' => $this->name,
            'registrant' => $this->registrant,
            'regenerateAuthCode' => false,
            'statusRemove' => [EppDomainStatus::CLIENT_DELETE_PROHIBITED],
        ];
    }
}
