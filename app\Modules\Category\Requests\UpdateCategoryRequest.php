<?php

namespace App\Modules\Category\Requests;

use App\Modules\Category\Services\CategoryService;
use App\Rules\CategoryNameExists;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'names.*' => [
                'required',
                'string',
                'min:3',
                'max:20',
                'distinct',
                'regex:/^[a-zA-Z0-9\s\-\'"]*$/',
                new CategoryNameExists($this->withId),
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'names.*' => 'name',
        ];
    }

    public function save()
    {
        CategoryService::instance()->save($this->all());
    }
}
