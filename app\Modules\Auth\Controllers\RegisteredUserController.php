<?php

namespace App\Modules\Auth\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Auth\Requests\CreateNewAccountRequest;
use App\Modules\Auth\Requests\InviteAccountRequest;
use App\Modules\Auth\Requests\StoreNewAccountRequest;
use App\Modules\Auth\Requests\UpdateUserAccountRequest;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    // public function create(CreateNewAccountRequest $request): Response
    // {
    //     return Inertia::render('Auth/Register', [
    //         'mode' => 'register',
    //         'initialData' => [
    //             'email' => $request->email,
    //             'ip' => $request->ip,
    //         ]
    //     ]);
    // }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    // public function store(StoreNewAccountRequest $request): RedirectResponse
    // {
    //     $request->store();

    //     return redirect()->route('identity.setup')->with('retry', false);
    // }

    /**
     * Display the registration view using the email from the invite url.
     */
    public function handleInvite(InviteAccountRequest $request): Response
    {
        return Inertia::render('UserRegistration/UserRegistrationFormCreate', [
            'mode' => 'invite',
            'initialData' => [
                'user_id' => $request->user_id,
                'email' => $request->email,
                'ip' => $request->ip,
                'disable_verification' => $request->disable_verification,
                'disable_deposit' => $request->disable_deposit,
                'balance' => $request->balance,
            ],
        ]);

    }

    public function storeUpdate(UpdateUserAccountRequest $request): RedirectResponse
    {
        $request->store();

        if (! $request->disable_verification) {
            return redirect()->route('identity.setup')->with('retry', false);
        }

        return redirect()->route('domain');
    }
}
