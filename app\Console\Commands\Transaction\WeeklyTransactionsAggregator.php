<?php

namespace App\Console\Commands\Transaction;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class WeeklyTransactionsAggregator extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transactions:weekly-transactions-aggregator';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles aggregation of user transactions per week.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'WeeklyTransactionsAggregator: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('WeeklyTransactionsAggregator: Aggregating weekly transactions...');

        [$startAt, $endAt] = $this->getDateRange();

        $userTransactionsPayload = $this->buildUserTransactionsPayload($startAt, $endAt);
        $systemTransactionsPayload = $this->buildSystemTransactionsPayload($startAt, $endAt);

        if (empty($userTransactionsPayload) || empty($systemTransactionsPayload)) {
            app(AuthLogger::class)->info('WeeklyTransactionsAggregator: Done. No transactions for this week.');

            return;
        }

        DB::table('user_weekly_transactions')->insert($userTransactionsPayload);
        DB::table('system_weekly_transactions')->insert($systemTransactionsPayload);

        app(AuthLogger::class)->info('WeeklyTransactionsAggregator: Done');
    }

    private function getDateRange(): array
    {
        $now = Carbon::now()->subDay();
        $startAt = $now->copy()->startOfWeek(Carbon::SUNDAY)->toDateString();
        $endAt = $now->copy()->endOfWeek(Carbon::SATURDAY)->toDateString();

        return [$startAt, $endAt];
    }

    private function buildUserTransactionsPayload(string $startAt, string $endAt): array
    {
        return DB::table('user_daily_transactions')
            ->select('user_transaction_id', DB::raw('SUM(counter) as total_counter'))
            ->whereBetween('date', [$startAt, $endAt])
            ->groupBy('user_transaction_id')
            ->get()->map(fn($summary) => [
                'user_transaction_id' => $summary->user_transaction_id,
                'counter' => $summary->total_counter,
                'start_at' => $startAt,
                'end_at' => $endAt,
            ])->toArray();
    }

    private function buildSystemTransactionsPayload(string $startAt, string $endAt): array
    {
        return DB::table('system_daily_transactions')
            ->select('transaction_id', DB::raw('SUM(counter) as total_counter'))
            ->whereBetween('date', [$startAt, $endAt])
            ->groupBy('transaction_id')
            ->get()->map(fn($summary) => [
                'transaction_id' => $summary->transaction_id,
                'counter' => $summary->total_counter,
                'start_at' => $startAt,
                'end_at' => $endAt,
            ])->toArray();
    }
}
