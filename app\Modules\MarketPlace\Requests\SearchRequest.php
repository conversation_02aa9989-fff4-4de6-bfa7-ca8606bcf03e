<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Constants\ModelNicknames;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Http;

class SearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'domains' => 'required|string',
            'model' => 'required|string',
        ];
    }

    public function doAISearch()
    {
        $model = ModelNicknames::nicknames[$this->model];
        return Http::ai()->get("$this->domains&provider=$model")->body();
    }
}
