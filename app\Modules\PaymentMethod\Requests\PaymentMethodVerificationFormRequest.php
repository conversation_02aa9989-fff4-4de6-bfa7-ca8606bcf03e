<?php

namespace App\Modules\PaymentMethod\Requests;

use App\Rules\PaymentMethodCheckIfNameIsCorrectRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class PaymentMethodVerificationFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'paymentMethodId' => ['required', 'string'],
            'cardName' => [
                'required',
                'string',
                new PaymentMethodCheckIfNameIsCorrectRule($this->input('paymentMethodId')),
            ],
        ];
    }
}
