<?php

namespace App\Modules\Domain\Services\CreateServices;

use App\Models\Domain;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Traits\UserContact;
use App\Util\Helper\Domain\DefaultDomains;
use App\Util\Helper\Domain\DomainTld;
use App\Util\Helper\Store\BulkActions;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class CreatedDomains
{
    use UserContact;

    public int $userId;

    public array $contacts;

    public array $tlds;

    public $nameservers;

    public Collection $carts;

    public array $rows;

    public Collection $domains;

    public const CONTACT_NAME = 'name';

    public const NAMESERVER_NAME = 'names';

    public function __construct(Collection $carts)
    {
        $this->carts = $carts ?? null;
        // $this->carts = $carts ?? DefaultDomains::throwEmptyError('carts');

        $this->userId = $this->getUserId();
        $this->contacts = $this->getDefaultContacts($this->userId);
        $this->tlds = DomainTld::getAllTlds();
        $this->nameservers = null;
    }

    public function format()
    {
        foreach ($this->carts as $item) {
            $registryId = $this->tlds[$item->tld_id]['registry_id'];
            $contactsArray = DefaultDomains::getDefaultContactsArray($this->contacts[$registryId], self::CONTACT_NAME);
            $statusArray = EppDomainStatus::CLIENT_LOCK_STATUS;
            $yearLength = $item->year_length ?? 1;

            $domain = [
                'name' => strtolower($item->name),
                'status' => DomainStatus::IN_PROCESS,
                'root' => $item->tld_id,
                'registrant' => $this->contacts[$registryId]['name'][DomainContact::REGISTRANT],
                'year_length' => $yearLength,
                'expiry' => now()->addYears($yearLength)->valueOf(),
                'transferredIn' => null,
                'transferredOut' => null,
                'auth_code_updated_at' => now(),
                'contacts' => json_encode($contactsArray),
                'client_status' => json_encode($statusArray),
                'hosts' => null,
                'nameservers' => null,
                'server_renew_at' => now(),
                'client_renew_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $this->rows[] = $domain;
        }

        return $this;
    }

    public function store()
    {
        $table = (new Domain)->getTable();
        $this->domains = BulkActions::instance()->bulkInsertGetData($table, $this->rows, false);

        return $this;
    }

    public function getDomains()
    {
        return $this->domains;
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? DefaultDomains::throwEmptyError('user id');
    }
}
