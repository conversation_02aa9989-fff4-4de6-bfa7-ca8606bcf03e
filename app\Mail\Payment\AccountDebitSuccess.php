<?php

namespace App\Mail\Payment;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Util\Helper\CryptHelper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class AccountDebitSuccess extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $greeting;

    private $date;

    private $paymentMethod;

    private $amount;

    private $balance;

    private $email;

    private $name;

    private $userId;

    private $subjectString;

    private $redirectUrl;

    private $supportUrl;

    private $phone;

    private $paymentServiceObj;

    private $transactionId;

    private $backOffMinutes = 5;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public $uniqueFor = 120; // 2 minutes

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->email = $data['email'];
        $this->userId = $data['user_id'];
        $this->name = $data['name'];
        $this->greeting = $data['greeting'];
        $this->date = $data['date'];
        $this->amount = round(floatval($data['amount']), 2);
        $this->balance = round(floatval($data['balance']), 2);
        $this->subjectString = $data['subject'];
        $this->supportUrl = $data['support_url'];
        $this->redirectUrl = $data['redirect_url'];
        $this->phone = $data['phone'];
        $this->paymentMethod = $data['payment_method'];
        $this->paymentServiceObj = $data['payment_service_obj'];
        $this->transactionId = CryptHelper::decrypt($this->paymentServiceObj->transaction_id ?? null) ?? null;

        if ($data['payment_service_type'] === PaymentServiceType::STRIPE) {
            $this->getStripePaymentIntent($data['payment_intent'] ?? '');
        }
    }

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: $this->subjectString,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {

        $view = 'Mails.Payment.AccountDebitSuccess';

        return new Content(
            markdown: $view,
            with: [
                'greeting' => $this->greeting,
                'senderName' => config('mail.from.sd_name'),
                'date' => $this->date,
                'paymentMethod' => $this->paymentMethod,
                'amount' => $this->amount,
                'balance' => $this->balance,
                'redirectUrl' => $this->redirectUrl,
                'supportUrl' => $this->supportUrl,
                'phoneNumber' => $this->phone,
                'paymentServiceObj' => $this->paymentServiceObj,
                'transactionId' => $this->transactionId,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    private function failed($e)
    {
        app(AuthLogger::class)->error('AccountDebitSuccess: '.$e->getMessage());
        $this->fail();
    }

    private function getStripePaymentIntent(string $paymentIntent)
    {
        if (! empty($paymentIntent)) {
            try {
                $paymentIntentDetails = PaymentIntentProvider::instance()->retrieveIntent($paymentIntent);

                $paymentMethodDetails = PaymentIntentProvider::instance()->retrievePaymentMethod($paymentIntentDetails->payment_method);

                $cardDetails = $paymentMethodDetails['card'];

                $this->paymentMethod = ucfirst($cardDetails['brand'])." ending in {$cardDetails['last4']}";
            } catch (Exception $e) {
                app(AuthLogger::class)->error('AccountDebitSuccess: '.$e->getMessage());
            }
        }
    }
}
