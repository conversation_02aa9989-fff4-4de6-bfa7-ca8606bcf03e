//* PACKAGES 
//...

//* ICONS
//... 

//* LAYOUTS 
//...

//* COMPONENTS
import InputLabel from "../InputLabel";
import InputError from "../InputError";

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function AppSelectComponent(
    { 
        //! PROPS
        inputId,
        inputName, 
        inputLabel,
        inputErrorMessage = null, 
        inputOptions = [], 
        placeholder = 'please select item',
        selectedValue = null,

        //! STATES 
        isDisabled = false, 
        
        //! EVENTS
        onChangeEvent = () => { alert('test')}
    }
)
{
    //! PACKAGE
    //... 
    
    //! VARIABLES
    ///...

    //! STATES
    //...
    
    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <div
            className="flex flex-col gap-2"
        >
            <InputLabel
                forInput={inputName}
                value={inputLabel}
                className={'capitalize'}
            />
            <select
                id={inputId}
                name={inputName}
                value={selectedValue}
                className={
                    `w-full border-gray-300 focus:border-gray-500 focus:ring-gray-500 rounded-md shadow-sm placeholder-gray-200
                    disabled:bg-gray-100 disabled:text-slate-500`
                }
                disabled={isDisabled}
                onChange={onChangeEvent}
            >
                <option
                    className="capitalize"
                    value=''
                    disabled
                >
                    {placeholder}
                </option>
                {
                    inputOptions.map(
                        (item, index) => 
                        {
                            return (
                                <option
                                    key={index}
                                    value={item.value}
                                >
                                    {item.label}
                                </option>
                            );
                        }
                    )
                }
            </select>
            <InputError
                message={inputErrorMessage}
                className=""
            />
        </div>
    );
}