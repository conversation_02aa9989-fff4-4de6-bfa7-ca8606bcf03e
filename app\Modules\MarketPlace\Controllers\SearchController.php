<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\DomainEppSearch\Requests\CheckMultipleDomainRequest;
use App\Modules\MarketPlace\Services\MarketCartService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;
use Inertia\Response;

class SearchController extends Controller
{
    public function index(): Response
    {
        return Inertia::render('Market/Search/Home', [
            'num_cart' => MarketCartService::instance()->getCount(),
        ]);
    }

    public function show($search): Response
    {
        return Inertia::render('Market/SearchResult/SearchResult', [
            'search' => $search,
            'domains' => [],
            'num_cart' => MarketCartService::instance()->getCount(),
        ]);
    }

    public function search($search, $filters)
    {
        return Auth::user() ? Http::marketlist()->get("/$search?$filters")->body() : json_encode([]);
    }

    public function check(CheckMultipleDomainRequest $request)
    {
        return Auth::user() ? $request->search() : json_encode([]);
    }
}
