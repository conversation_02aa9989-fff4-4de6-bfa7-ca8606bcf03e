<?php

namespace App\Modules\MarketPlace\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\MarketPlace\Constants\MarketPlaceVendors;
use App\Modules\Transfer\Services\EppTransferService;
use App\Util\Constant\QueueConnection;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class AfternicManualTransferJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * Create a new job instance.
     */
    public function __construct($domain, $authCode, $yearLength, $email, $order_id)
    {
        $this->params = [
            'domain' => $domain,
            'authCode' => $authCode,
            'yearLength' => $yearLength,
            'order_id' => $order_id,
            'email' => $email,
        ];

        $this->onConnection(QueueConnection::MARKET_PLACE_JOBS);
        $this->onQueue(MarketPlaceVendors::AFTERNIC);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            EppTransferService::instance()->callEppTransferRequest($this->params['domain'], $this->params['authCode'], $this->params['yearLength'], $this->params['email']);

            // update marketplace domain status
            DB::table('market_place_domains')->where('order_id', $this->params['order_id'])->update(['epp_error' => 'null']);

            // remove entry on manuals
            DB::table('market_domains_manual')->where('name', $this->params['domain'])->delete();

            app(AuthLogger::class)->info('AfternicManualTransfer: Completed transfer for domain: ', $this->params['domain']);
        } catch (Exception $e) {
            app(AuthLogger::class)->info('AfternicManualTransfer: Error: '.$e->getMessage());
            $this->fail();
        }
    }
}
