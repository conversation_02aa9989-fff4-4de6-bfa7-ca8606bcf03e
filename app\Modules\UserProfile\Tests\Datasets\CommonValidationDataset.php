<?php

namespace App\Modules\UserProfile\Tests\Datasets;

class CommonValidationDataset
{
    public static function validEmailData(): array
    {
        return [
            'standard_email' => ['email' => '<EMAIL>'],
            'email_with_subdomain' => ['email' => '<EMAIL>'],
            'email_with_numbers' => ['email' => '<EMAIL>'],
            'email_with_dots' => ['email' => '<EMAIL>'],
            'email_with_plus' => ['email' => '<EMAIL>'],
        ];
    }

    public static function invalidEmailData(): array
    {
        return [
            'invalid_format' => ['email' => 'invalid-email'],
            'missing_at' => ['email' => 'userexample.com'],
            'missing_domain' => ['email' => 'user@'],
            'missing_username' => ['email' => '@example.com'],
            'double_at' => ['email' => 'user@@example.com'],
        ];
    }

    public static function validPasswordData(): array
    {
        return [
            'strong_password_basic' => ['password' => 'StrongPass123', 'password_confirmation' => 'StrongPass123'],
            'strong_password_special' => ['password' => 'MyPass123!', 'password_confirmation' => 'MyPass123!'],
            'strong_password_long' => ['password' => 'VeryStrongPassword123', 'password_confirmation' => 'VeryStrongPassword123'],
        ];
    }

    public static function invalidPasswordData(): array
    {
        return [
            'password_too_short' => ['password' => 'Pass12', 'password_confirmation' => 'Pass12'],
            'password_no_numbers' => ['password' => 'WeakPassABC', 'password_confirmation' => 'WeakPassABC'],
            'password_mismatch' => ['password' => 'StrongPass123', 'password_confirmation' => 'DifferentPass123'],
            'password_too_long' => ['password' => str_repeat('a', 101), 'password_confirmation' => str_repeat('a', 101)],
        ];
    }

    public static function validNameData(): array
    {
        return [
            'simple_name' => ['first_name' => 'John', 'last_name' => 'Doe'],
            'compound_first_name' => ['first_name' => "Mary-Jane O'Connor", 'last_name' => 'Smith'],
            'compound_last_name' => ['first_name' => 'John', 'last_name' => "Smith-Jones O'Brien"],
            'name_with_spaces' => ['first_name' => 'Jean Pierre', 'last_name' => 'Van Der Berg'],
        ];
    }

    public static function invalidNameData(): array
    {
        return [
            'first_name_too_short' => ['first_name' => 'A'],
            'last_name_too_short' => ['last_name' => 'D'],
            'first_name_special_chars' => ['first_name' => 'John@123'],
            'last_name_special_chars' => ['last_name' => 'Doe#123'],
            'first_name_numbers' => ['first_name' => 'John123'],
            'last_name_numbers' => ['last_name' => 'Doe456'],
        ];
    }

    public static function validAddressData(): array
    {
        return [
            'valid_address_with_unit' => [
                'unit' => 'Apt 123',
                'street' => '123 Main Street',
                'city' => 'New York',
                'stateProvince' => 'NY',
                'postalCode' => '10001',
                'countryCode' => 'US'
            ],
            'valid_address_without_unit' => [
                'street' => '456 Oak Avenue',
                'city' => 'Los Angeles',
                'stateProvince' => 'CA',
                'postalCode' => '90210',
                'countryCode' => 'US'
            ],
            'valid_address_with_hyphen_postal' => [
                'street' => '789 Pine Road',
                'city' => 'Chicago',
                'stateProvince' => 'IL',
                'postalCode' => '123-456',
                'countryCode' => 'US'
            ],
            'valid_address_with_space_postal' => [
                'street' => '321 Elm Street',
                'city' => 'Miami',
                'stateProvince' => 'FL',
                'postalCode' => '123 456',
                'countryCode' => 'US'
            ],
        ];
    }

    public static function invalidAddressData(): array
    {
        return [
            'street_too_short' => ['street' => 'ab'],
            'street_too_long' => ['street' => str_repeat('a', 101)],
            'street_invalid_characters' => ['street' => '123 Main St @#$'],
            'city_too_short' => ['city' => 'ab'],
            'city_too_long' => ['city' => str_repeat('a', 51)],
            'city_invalid_characters' => ['city' => 'New York 123'],
            'stateProvince_too_short' => ['stateProvince' => 'a'],
            'stateProvince_too_long' => ['stateProvince' => str_repeat('a', 51)],
            'stateProvince_invalid_characters' => ['stateProvince' => 'NY123'],
            'postalCode_too_long' => ['postalCode' => '123456789'],
            'postalCode_invalid_characters' => ['postalCode' => 'ABC@123'],
            'countryCode_too_short' => ['countryCode' => 'U'],
            'countryCode_too_long' => ['countryCode' => 'USA'],
            'countryCode_invalid_characters' => ['countryCode' => '12'],
            'unit_too_short' => ['unit' => 'ab'],
            'unit_too_long' => ['unit' => str_repeat('a', 51)],
            'unit_invalid_characters' => ['unit' => 'Apt @#$'],
        ];
    }

    public static function validTokenData(): array
    {
        return [
            'standard_token' => ['token' => 'valid-token-123'],
            'long_token' => ['token' => 'very-long-token-with-many-characters-123456789'],
            'token_with_special_chars' => ['token' => 'token_with-special.chars'],
        ];
    }

    public static function invalidTokenData(): array
    {
        return [
            'empty_token' => ['token' => ''],
            'null_token' => ['token' => null],
        ];
    }

    public static function validProfileData(): array
    {
        return [
            'complete_profile' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>'
            ],
            'profile_with_compound_names' => [
                'first_name' => "Mary-Jane O'Connor",
                'last_name' => "Smith-Jones",
                'email' => '<EMAIL>'
            ],
        ];
    }

    public static function validPasswordResetData(): array
    {
        return [
            'complete_reset_data' => [
                'token' => 'valid-reset-token',
                'email' => '<EMAIL>',
                'password' => 'NewStrongPass1231',
                'password_confirmation' => 'NewStrongPass1231'
            ],
        ];
    }
}