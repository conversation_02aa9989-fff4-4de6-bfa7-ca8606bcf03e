<?php

namespace App\Modules\AccountCredit\Services;

use App\Exceptions\FailedRequestException;
use App\Models\AccountCredit;
use App\Models\User;
use App\Modules\AccountCredit\Constants\AccountCreditType;
use App\Modules\AccountCredit\Helpers\AccountCreditHash;
use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\Notification\Services\AccountCreditNotificationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class AccountCreditMaker
{
    public static function genesis(int $userId)
    {
        $block = [
            'user_id' => $userId,
            'block_index' => 0,
            'payment_service_id' => null,
            'type' => AccountCreditType::GENESIS,
            'running_balance' => 0,
            'amount' => 0,
            'previous_hash' => '0',
            'created_at' => Carbon::now()->timestamp,
        ];

        $block['hash'] = AccountCreditHash::generate($block);

        return AccountCredit::insertGetId($block);
    }

    public static function add(object $previousBlock, int $userId, array $data)
    {
        $runningBalance = self::getCurrentRunningBalance($previousBlock, $data);

        $block = [
            'user_id' => $userId,
            'block_index' => intval($previousBlock->block_index) + 1,
            'type' => $data['type'] ?? '',
            'running_balance' => $runningBalance,
            'amount' => $data['amount'] ?? 0,
            'previous_hash' => $previousBlock->hash,
            'created_at' => Carbon::now()->timestamp,
        ];

        $block['hash'] = AccountCreditHash::generate($block);

        return AccountCredit::insertGetId($block);
    }

    // PRIVATE FUNCTIONS

    public static function getCurrentRunningBalance($previousBlock, $currentBlock)
    {
        $previousBalance = $previousBlock->running_balance ?? 0;
        $amount = $currentBlock['amount'] ?? 0;
        $runningBalance = $currentBlock['type'] == AccountCreditType::DEBIT ?
            self::debit($previousBalance, $amount, $previousBlock->user_id) :
            self::credit($previousBalance, $amount, $previousBlock->user_id);

        return $runningBalance;
    }

    private static function credit($previousBalance, $amount, $userId)
    {
        self::validateBalance($previousBalance, $amount, $userId);

        $total = floatval($previousBalance) - floatval($amount);

        return round($total, 2);
    }

    private static function debit($previousBalance, $amount, $userId)
    {
        self::validateBalance($previousBalance, $amount, $userId);

        $total = floatval($previousBalance) + floatval($amount);

        return round($total, 2);
    }

    private static function validateBalance($previousBalance, $amount, $userId)
    {
        $validator = Validator::make(['balance' => $previousBalance, 'amount' => $amount], [
            'balance' => 'required|numeric',
            'amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            self::sendInvalidAccountNotifications($previousBalance, $amount, $userId);
            throw new FailedRequestException(400, 'There is invalid data on adding account credit.', 'Bad request');
        }
    }

    public static function sendInvalidAccountNotifications($previousBalance, $amount, $userId)
    {
        $user = User::findOrFail($userId);
        AdminNotificationService::instance()->sendInvalidAccountCreditBalanceNotif([
            'email' => $user->email,
            'amount' => $amount,
            'balance' => $previousBalance,
        ]);

        AccountCreditNotificationService::instance()->sendInvalidAccountCreditBalanceNotif([
            'email' => $user->email,
            'amount' => $amount,
            'balance' => $previousBalance,
            'user_id' => $userId,
        ]);
    }
}
