<?php

namespace App\Modules\Epp\Services;

use Illuminate\Support\Facades\Config;

class ProductionEppUrl implements EppUrlInterface
{
    protected $key;

    protected $secretKey;

    public function __construct(string $key, string $secretKey)
    {
        $this->key = $key;
        $this->secretKey = $secretKey;
    }

    public function domain(string $registry): string
    {
        return $this->key.Config::get('domain.v3_multiple_'.$registry);
    }

    public function contact(string $registry): string
    {
        return $this->key.Config::get('contact.v3_multiple_'.$registry);
    }

    public function transfer(string $registry): string
    {
        return $this->key.Config::get('transfer.v3_multiple_'.$registry);
    }

    public function poll(string $registry): string
    {
        return $this->key.Config::get('poll.v3_multiple_'.$registry);
    }

    public function host(string $registry): string
    {
        return $this->key.Config::get('host.v3_multiple_'.$registry);
    }

    public function secretKey(): string
    {
        return $this->secretKey;
    }
}
