<?php

namespace App\Modules\Epp\Constants;

final class EppRegistrySupportedStatus
{
    public const DOMAIN_CLIENTDELETEPROHIBITED = 'clientDeleteProhibited';

    public const DOMAIN_SERVERDELETEPROHIBITED = 'serverDeleteProhibited';

    public const DOMAIN_CLIENTHOLD = 'clientHold';

    public const DOMAIN_SERVERHOLD = 'serverHold';

    public const DOMAIN_CLIENTRENEWPROHIBITED = 'clientRenewProhibited';

    public const DOMAIN_SERVERRENEWPROHIBITED = 'serverRenewProhibited';

    public const DOMAIN_CLIENTTRANSFERPROHIBITED = 'clientTransferProhibited';

    public const DOMAIN_SERVERTRANSFERPROHIBITED = 'serverTransferProhibited';

    public const DOMAIN_CLIENTUPDATEPROHIBITED = 'clientUpdateProhibited';

    public const DOMAIN_SERVERUPDATEPROHIBITED = 'serverUpdateProhibited';

    public const DOMAIN_INACTIVE = 'inactive';

    public const DOMAIN_OK = 'ok';

    public const DOMAIN_PENDINGCREATE = 'pendingCreate';

    public const DOMAIN_PENDINGDELETE = 'pendingDelete';

    public const DOMAIN_PENDINGRENEW = 'pendingRenew';

    public const DOMAIN_PENDINGTRANSFER = 'pendingTransfer';

    public const DOMAIN_PENDINGUPDATE = 'pendingUpdate';

    public const HOST_CLIENTDELETEPROHIBITED = 'clientDeleteProhibited';

    public const HOST_SERVERDELETEPROHIBITED = 'serverDeleteProhibited';

    public const HOST_CLIENTUPDATEPROHIBITED = 'clientUpdateProhibited';

    public const HOST_SERVERUPDATEPROHIBITED = 'serverUpdateProhibited';

    public const HOST_LINKED = 'linked';

    public const HOST_OK = 'ok';

    public const HOST_PENDINGCREATE = 'pendingCreate';

    public const HOST_PENDINGDELETE = 'pendingDelete';

    public const HOST_PENDINGTRANSFER = 'pendingTransfer';

    public const HOST_PENDINGUPDATE = 'pendingUpdate';

    public const CONTACT_CLIENTDELETEPROHIBITED = 'clientDeleteProhibited';

    public const CONTACT_SERVERDELETEPROHIBITED = 'serverDeleteProhibited';

    public const CONTACT_CLIENTTRANSFERPROHIBITED = 'clientTransferProhibited';

    public const CONTACT_SERVERTRANSFERPROHIBITED = 'serverTransferProhibited';

    public const CONTACT_CLIENTUPDATEPROHIBITED = 'clientUpdateProhibited';

    public const CONTACT_SERVERUPDATEPROHIBITED = 'serverUpdateProhibited';

    public const CONTACT_LINKED = 'linked';

    public const CONTACT_OK = 'ok';

    public const CONTACT_PENDINGCREATE = 'pendingCreate';

    public const CONTACT_PENDINGDELETE = 'pendingDelete';

    public const CONTACT_PENDINGTRANSFER = 'pendingTransfer';

    public const CONTACT_PENDINGUPDATE = 'pendingUpdate';
}
