//* PACKAGES 
import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify';
import { useForm, router, Link } from "@inertiajs/react";
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';

//* ICONS
//...

//* COMPONENTS
import AppLoaderSpinner from '@/Components/App/AppLoaderSpinner';
import AppListEmptyComponent from '@/Components/App/AppListEmptyComponent';
import AppVerificationPromptGroup from "@/Components/App/AppVerificationPromptGroupComponent";
import PrimaryButton from "@/Components/PrimaryButton";
import Checkbox from "@/Components/Checkbox";
import PaymentMethodCardCheckoutComponent from '@/Components/PaymentMethod/PaymentMethodCardCheckoutComponent';

//* PARTIALS
//...

//* STATE
//... 

//* UTILS 
import { getEventValue } from "@/Util/TargetInputEvent";
import { evaluate } from "@/Util/AxiosResponseHandler";
import UtilCheckIfHasSecuredTransaction from '@/Util/UtilCheckIfHasSecuredTransaction';
import _PaymentSummary from '../../Constant/_PaymentSummary';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function StripeCheckoutFormComponent(
    {
        type,
        user_id,
        domains = [],
        market_domains = [],
        other_fees,
        intent,
        paymentMethod = 'stripe',
        isActive = false,
        selectedPaymentMethodId = null,
        isSelected,
        onPaymentMethodSelect = () => { },
        stripeFeeObj = [],
        onHandlePageProcessing = () => { },

    }
) {
    //! PACKAGE
    const stripe = useStripe();
    const elements = useElements();
    const { post, setError } = useForm(
        {
            user_id: user_id,
            domains: domains,
            market_domains: market_domains,
            other_fees: other_fees,
            intent: intent,
            payment_service_type: _PaymentSummary.SERVICE_TYPES.STRIPE,
            stripe_fees: stripeFeeObj,

        }
    );

    //! VARIABLES 
    const shouldVerifyUser = UtilCheckIfHasSecuredTransaction('purchases');

    //! STATES
    const [isProcessing, setProcessing] = useState(false);
    const [errorMessage, setErrorMessage] = useState(null);
    const [stateDefaultPaymentMethodId, setStateDefaultPaymentMethodId] = useState(null);

    //! FUNCTIONS
    const getGrossAmount = () => {
        return stripeFeeObj.gross_amount.toFixed(2) ?? other_fees.bill_total.toFixed(2);
    }

    async function checkError(e) {
        setProcessing(true);
        onHandlePageProcessing(true);

        let response = await axios
            .post(route("domain.payment.error"))
            .then(
                (response) => {
                    return response;
                }
            )
            .catch(
                (error) => {
                    router.get(route("domain.payment.fail"), { message: error.response.data.message });
                    return error.response;
                }
            );

        response = evaluate(response);

        if (!response.success) {
            setError(response.errors);

            return;
        }

        setProcessing(false);
        onHandlePageProcessing(false);
    };

    //! SUB COMPONENTS 
    function subComponentCheckoutForm() {
        const [stateShowVerificationPrompt, setStateShowVerificationPrompt] = useState(false);

        async function handleOnSubmit() {
            if (!stripe || !elements) {
                return;
            }

            setProcessing(true);
            onHandlePageProcessing(true);

            const paymentResult = await stripe.confirmPayment(
                {
                    elements,
                    redirect: 'if_required'
                }
            );

            await new Promise(resolve => setTimeout(resolve, 3000)); // for balance transactions to load re: stripe fees

            if (paymentResult.error) {
                checkError();
                setErrorMessage("");
                if (result.error.type != 'validation_error') {
                    setErrorMessage(result.error.message);
                }
                setProcessing(false);
                onHandlePageProcessing(false);
            }
            else {

                let postRoute = null;

                switch (type) {
                    case 'renew':
                        postRoute = route("domain.renew");
                        break;
                    case 'transfer':
                        postRoute = route("transfer.inbound.checkout");
                        break;
                    case 'multicheckout':
                        postRoute = route("domain.multicart.checkout");
                        break;
                    case 'redemption':
                        postRoute = route("domain-redemption.restore");
                        break;
                    case 'redeem':
                        postRoute = route("domain.redeem");
                        break;
                    case 'offercheckout':
                        postRoute = route("marketoffer.checkout.store");
                        break;
                }

                if (postRoute) {
                    post(postRoute, {
                        replace: true,
                        preserveState: false,
                    });
                }
            }

            // setProcessing(false);
        };

        return (
            <>
                <AppVerificationPromptGroup
                    isShow={stateShowVerificationPrompt}
                    onClose={() => setStateShowVerificationPrompt(false)}
                    onSubmitSuccess={handleOnSubmit}
                    onSubmitError={() => { }}
                />
                <form
                    className={`flex flex-col p-4 border rounded-lg transition-all duration-200 ${isActive ? 'shadow border-primary' : 'border-gray-200'} ${!isActive ? 'opacity-50' : ''}`}
                    onSubmit={
                        (e) => {
                            e.preventDefault();

                            if (shouldVerifyUser == true) {
                                setStateShowVerificationPrompt(true)
                            }
                            else {
                                handleOnSubmit();
                            }
                        }
                    }
                >
                    <div className="mb-2">
                        <p className="font-medium mb-2">New Card</p>
                    </div>
                    <div>
                        <PaymentElement
                            onFocus={() => {
                                const radioBtn = document.querySelector('input[type="radio"][value="stripe"]');
                                if (radioBtn) {
                                    radioBtn.click();
                                }
                                onPaymentMethodSelect(null);
                            }}
                        />
                    </div>
                    {errorMessage && <div><span className="text-danger">{errorMessage}</span></div>}
                </form>
            </>
        );
    }

    function subComponentSavedPaymentMethods() {
        const [stateShowVerificationPrompt, setStateShowVerificationPrompt] = useState(false);
        const [statePaymentMethods, setStatePaymentMethods] = useState([]);
        const [stateIsLoading, setStateIsLoading] = useState(false);

        useEffect(
            () => {
                const fetchPaymentMethods = async () => {
                    setStateIsLoading(true);

                    try {
                        const response = await axios.get(route('payment-method.fetch-all'));
                        const items = response?.data?.items || [];
                        setStatePaymentMethods(items);
                    }
                    catch (error) {
                        setStatePaymentMethods([]);
                    }
                    finally {
                        setStateIsLoading(false);
                    }
                };

                fetchPaymentMethods();
            },
            []
        );

        async function handleOnSubmit() {
            if (!stripe || !elements) {
                return;
            }

            setProcessing(true);
            onHandlePageProcessing(true);

            const response = await axios.post(
                route('payment-method.create-payment-intent'),
                {
                    amount: getGrossAmount(),
                    paymentMethodId: selectedPaymentMethodId
                },
                {
                    withCredentials: true,
                }
            );

            const { clientSecret, paymentIntentId } = response.data;

            if (clientSecret == null) {
                setErrorMessage('Something Went Wrong!');
                setProcessing(false);
                onHandlePageProcessing(false);
                return;
            }

            const paymentResult = await stripe.confirmPayment(
                {
                    clientSecret,
                    redirect: 'if_required'
                }
            );

            await new Promise(resolve => setTimeout(resolve, 3000)); // for balance transactions to load re: stripe fees

            if (paymentResult.error) {
                checkError();
                setErrorMessage(result.error.message);
                setProcessing(false);
                onHandlePageProcessing(false);
            }
            else {
                let postRoute = null;

                switch (type) {
                    case 'renew':
                        postRoute = route("domain.renew");
                        break;
                    case 'transfer':
                        postRoute = route("transfer.inbound.checkout");
                        break;
                    case 'multicheckout':
                        postRoute = route("domain.multicart.checkout");
                        break;
                    case 'redemption':
                        postRoute = route("domain-redemption.restore");
                        break;
                    case 'redeem':
                        postRoute = route("domain.redeem");
                        break;
                }

                if (postRoute) {
                    router.post(
                        postRoute,
                        {
                            user_id: user_id,
                            domains: domains,
                            market_domains: market_domains,
                            other_fees: other_fees,
                            intent: paymentIntentId,
                            payment_service_type: _PaymentSummary.SERVICE_TYPES.STRIPE,
                            stripe_fees: stripeFeeObj,
                        }, {
                        replace: true,
                        preserveState: false,
                    }
                    );
                }
            }

            // setProcessing(false);
        }

        return (
            <>
                <AppVerificationPromptGroup
                    isShow={stateShowVerificationPrompt}
                    onClose={() => setStateShowVerificationPrompt(false)}
                    onSubmitSuccess={handleOnSubmit}
                    onSubmitError={() => { }}
                />
                <form
                    className={`flex flex-col gap-5 ${!isActive ? 'opacity-50' : ''}`}
                    onSubmit={
                        (e) => {
                            e.preventDefault();

                            if (shouldVerifyUser == true) {
                                setStateShowVerificationPrompt(true)
                            }
                            else {
                                handleOnSubmit();
                            }
                        }
                    }
                >
                    {
                        stateIsLoading == true
                            ?
                            <AppLoaderSpinner
                                isLoading={stateIsLoading}
                                message={'Loading payment methods. Please wait...'}
                            />
                            :
                            statePaymentMethods.length == 0
                                ?
                                <AppListEmptyComponent
                                    customMessage='No Payment Methods Found'
                                />
                                :
                                <>
                                    <div
                                        className="flex flex-col gap-5"
                                    >
                                        {
                                            statePaymentMethods.map(
                                                (item, index) => {
                                                    return (
                                                        <div
                                                            key={index}
                                                            className="flex items-center justify-between cursor-pointer"
                                                            onClick={() => onPaymentMethodSelect(item.id)}
                                                        >
                                                            <div className="flex-grow">
                                                                <PaymentMethodCardCheckoutComponent
                                                                    item={item}
                                                                    isDefault={item.id == stateDefaultPaymentMethodId}
                                                                    isSelected={item.id == selectedPaymentMethodId}
                                                                />
                                                            </div>
                                                            <div className="ml-4">
                                                                <input
                                                                    type="radio"
                                                                    name="saved_payment_method"
                                                                    value={item.id}
                                                                    checked={selectedPaymentMethodId === item.id}
                                                                    onChange={(e) => onPaymentMethodSelect(e.target.value)}
                                                                    className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                                                />
                                                            </div>
                                                        </div>
                                                    )
                                                }
                                            )
                                        }
                                    </div>
                                    {errorMessage && <div><span className="text-danger">{errorMessage}</span></div>}
                                </>
                    }
                </form>
            </>
        );
    }

    return (
        <>
            {paymentMethod === 'stripe' && subComponentCheckoutForm()}
            {paymentMethod === 'saved_card' && subComponentSavedPaymentMethods()}
        </>
    )
}