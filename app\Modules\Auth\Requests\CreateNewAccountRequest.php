<?php

namespace App\Modules\Auth\Requests;

use App\Exceptions\FailedRequestException;
use App\Rules\ValidFormat;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Foundation\Http\FormRequest;

class CreateNewAccountRequest extends FormRequest
{
    protected $redirect = 'bad-request';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $clientIp = ClientIp::getClientIp($this);

        if ($this->hasValidSignature() && strcmp($clientIp, $this->ip) == 0) {
            return true;
        }

        if ($this->has('email') && $this->has('expires') && $this->has('ip') && $this->has('signature')) {
            throw new FailedRequestException(410, 'Link has expired.', 'Gone');
        }

        throw new FailedRequestException(403, 'This action is not authorized.', 'Unauthorized');
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email:rfc,dns', "max:255", "regex:/^[0-9a-zA-Z.@_-]*$/", 'unique:users'],
            'ip' => ['required', 'string', 'max:16'],
        ];
    }

    public function messages(): array
    {
        return [
            'email.unique' => 'Link has expired.',
        ];
    }
}
