<?php

namespace App\Util\Helper;

use RuntimeException;

class EnvironmentValidator
{
    public static function validateTestEnvironment(): void
    {
        if (self::isRunningTestsInProduction()) {
            throw new RuntimeException('Cannot run tests in production environment.');
        }
    }

    private static function isRunningTestsInProduction(): bool
    {
        return self::isCliEnvironment()
            && self::isTestCommand()
            && self::isProductionEnvironment();
    }

    private static function isCliEnvironment(): bool
    {
        return php_sapi_name() === 'cli';
    }

    private static function isTestCommand(): bool
    {
        return in_array('test', $_SERVER['argv'] ?? [], true);
    }

    private static function isProductionEnvironment(): bool
    {
        $envContent = @file_get_contents(__DIR__ . '/../../../.env');

        if ($envContent === false) {
            return false;
        }

        if (preg_match('/^APP_ENV=(.+)$/m', $envContent, $matches)) {
            return trim($matches[1]) === 'production';
        }

        return false;
    }
}