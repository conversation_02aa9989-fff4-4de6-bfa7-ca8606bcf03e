<?php

namespace App\Modules\Domain\Requests\Redemption;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\UpdateServices\RedemptionDomainService;
use App\Rules\Domain\DomainRedemptionArrayExists;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class RedemptionPayRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'domains' => ['required', 'array', 'min:1', new DomainRedemptionArrayExists],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, $validator->errors()->first(), 'Page not found');
    }

    public function getRedemptionPaymentData(): array
    {
        return RedemptionDomainService::instance()->getRedemptionPaymentData($this->all());
    }
}
