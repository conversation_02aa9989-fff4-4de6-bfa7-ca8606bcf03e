<?php

namespace App\Modules\Auth\Services\AuthenticatorApp;

use App\Models\AuthenticatorAppRecoveryCode;
use App\Models\AuthenticatorAppUserSessions;
use Illuminate\Support\Facades\Hash;

class AuthenticatorAppRecoveryCodeService
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Fetch User Recovery Codes
     */
    public function fetchUserRecoveryCodes(int $userId)
    {
        return AuthenticatorAppRecoveryCode::where('user_id', '=', $userId)->get();
    }

    /**
     * Verify Code
     *
     *
     * @return int
     */
    public function verifyToken(string $token): bool
    {
        $session = AuthenticatorAppUserSessions::where('session_token', '=', $token)->first();

        if ($session != null) {
            return true;
        }

        return false;
    }

    /**
     * Verify Code
     *
     *
     * @return int
     */
    public function verifyCode(array $data, string $token): array
    {
        $session = AuthenticatorAppUserSessions::where('session_token', '=', $token)->first();

        $recoveryCodes = AuthenticatorAppRecoveryCode::where('user_id', '=', $session->user_id)->get();

        $isVerified = false;
        $isCodeAlreadyUsed = false;
        $userId = null;

        foreach ($recoveryCodes as $recoveryCode) {
            if (Hash::check($data['code'], $recoveryCode->code)) {
                if ($recoveryCode->last_used != null) {
                    $isCodeAlreadyUsed = true;
                } else {
                    $recoveryCode->last_used = now();
                    $recoveryCode->save();

                    $isVerified = true;
                }

                break;
            }
        }

        if ($isVerified == true) {
            $session->session_valid_until = now()->addDays(30);
            $session->save();

            $userId = $session->user_id;
        }

        return [
            'isVerified' => $isVerified,
            'isCodeAlreadyUsed' => $isCodeAlreadyUsed,
            'userId' => $userId,
        ];
    }
}
