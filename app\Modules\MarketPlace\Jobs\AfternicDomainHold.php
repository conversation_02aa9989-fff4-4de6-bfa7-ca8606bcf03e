<?php

namespace App\Modules\MarketPlace\Jobs;

use App\Events\DomainHistoryEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\MarketPlace\Constants\MarketPlaceVendors;
use App\Modules\MarketPlace\Services\MarketPlaceDomainService;
use App\Util\Constant\QueueConnection;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class AfternicDomainHold implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    private $registeredDomainId;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(object $domain, int $userId, string $from = 'market', $id = null)
    {
        $this->params = [
            'domain' => $domain,
            'userId' => $userId,
            'from' => $from,
            'id' => $id
        ];

        $this->registeredDomainId = $domain->id;

        $this->onConnection(QueueConnection::MARKET_PLACE_JOBS);
        $this->onQueue(MarketPlaceVendors::AFTERNIC);
    }

    public $uniqueFor = 300; // 5 minutes

    public function uniqueId(): int
    {
        return intval($this->registeredDomainId.$this->params['userId']);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            MarketPlaceDomainService::instance()->placeAfternicPendingHold($this->params);
        } catch (Exception $e) {
            $this->callEventDomainHistory($e->getMessage());
            if ($this->attempts() < 3) {
                throw $e;
            } else {
                MarketPlaceDomainService::instance()->onErrorAfternicPendingHold($this->params);
                $this->fail();
            }
        }
    }

    public function backoff(): array
    {
        if (strcmp(config('app.env'), 'production') == 0) {
            return [1800, 1800]; // 30 minutes
        } else {
            return [305, 305]; // 5 minutes
        }
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error($exception->getMessage());
    }

    // PRIVATE FUNCTIONS
    private function callEventDomainHistory(string $errorMessage)
    {
        app(AuthLogger::class)->error('AfternicDomainHold: Error: '.$errorMessage);
        app(AuthLogger::class)->error('number of attempts: '.$this->attempts());

        event(new DomainHistoryEvent([
            'domain_id' => $this->params['domain']->domain_id ?? null,
            'type' => 'TRANSFER_PURCHASE_PENDING',
            'user_id' => $this->params['userId'],
            'status' => 'failed',
            'message' => 'Attempt: '.$this->attempts().' Error: '.$errorMessage,
            'payload' => $this->params,
        ]));
    }
}
