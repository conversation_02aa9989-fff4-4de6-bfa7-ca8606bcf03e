<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StripeTransaction extends Model
{
    /** @use HasFactory<\Database\Factories\StripeTransactionFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'gross_amount',
        'net_amount',
        'service_fee',
        'payment_intent',
        'refund_id',
        'charge_id',
        'adjustments',
        'bill_amount',
    ];
}
