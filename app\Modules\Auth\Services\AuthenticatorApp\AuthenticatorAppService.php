<?php

namespace App\Modules\Auth\Services\AuthenticatorApp;

use App\Models\User;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use PragmaRX\Google2FA\Google2FA;

class AuthenticatorAppService
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Verify Code
     *
     *
     * @return int
     */
    public function verifyCode(string $secretKey, string $code): bool
    {
        $google2FA = new Google2FA;

        return $google2FA->verifyKey($secretKey, $code);
    }

    /**
     * Generate Key
     */
    public function generateSecretKey(): string
    {
        $google2fa = new Google2FA;

        return $google2fa->generateSecretKey();
    }

    /**
     * Generate Qr
     */
    public function generateQrCodeImageURL(string $secretKey, int $userId): string
    {
        $google2fa = new Google2FA;
        $user = User::findOrFail($userId);

        $qrCodeUrl = $google2fa->getQRCodeUrl(
            'StrangeDomains',
            $user->email,
            $secretKey
        );

        $qrCode = new QrCode($qrCodeUrl);
        $writer = new PngWriter;
        $qrCodeImage = $writer->write($qrCode);

        return $qrCodeImage->getDataUri();
    }

    /**
     * Disable
     */
    public function disable(int $userId): bool
    {
        $user = User::findOrFail($userId);

        $user->authenticator_app_secret_key = null;
        $user->enable_authenticator_app = false;

        $isDisabled = $user->save();

        if ($isDisabled == true) {
            $user->authenticatorAppSessions()->delete();
            $user->authenticatorAppRecoveryCodes()->delete();
        }

        return $isDisabled;
    }
}
