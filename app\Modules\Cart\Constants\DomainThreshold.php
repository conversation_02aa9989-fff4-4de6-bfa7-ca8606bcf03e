<?php

namespace App\Modules\Cart\Constants;

final class DomainThreshold
{
    public const PERIOD_DAILY = 'daily';
    public const PERIOD_WEEKLY = 'weekly';
    public const PERIOD_MONTHLY = 'monthly';
    public const NOTIFY = 'notify';
    public const REJECT = 'reject';


    public const PERIOD_TYPES = [
        self::PERIOD_DAILY,
        self::PERIOD_WEEKLY,
        self::PERIOD_MONTHLY,
    ];

    public const ACTION_TYPES = [
        self::NOTIFY,
        self::REJECT,
    ];
    
}
