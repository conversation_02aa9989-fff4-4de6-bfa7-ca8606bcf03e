<?php

namespace App\Modules\Histories\Constants;

final class UserTransactionType
{
    public const SIGN_IN = 'SIGN_IN';

    public const SIGN_OUT = 'SIGN_OUT';

    public const CONTACT_UPDATE = 'CONTACT_UPDATE';

    public const CATEGORY_UPDATE = 'CATEGORY_UPDATE';

    public const REGISTER = 'REGISTER';

    public const SECURITY_UPDATE = 'SECURITY_UPDATE';

    public const PROFILE_UPDATE = 'PROFILE_UPDATE';

    public const DOMAIN_UPDATE = 'DOMAIN_UPDATE';

    public const IDENTITY_VERIFICATION = 'IDENTITY_VERIFICATION';

    public const PAYMENT_SUMMARY = 'PAYMENT_SUMMARY';

    /**
     * Get all transaction types.
     */
    public static function getAllTypes(): array
    {
        return [
            self::SIGN_IN,
            self::SIGN_OUT,
            self::REGISTER,
            self::CONTACT_UPDATE,
            self::PROFILE_UPDATE,
            self::SECURITY_UPDATE,
            self::DOMAIN_UPDATE,
            self::IDENTITY_VERIFICATION,
            self::PAYMENT_SUMMARY,
        ];
    }

    /**
     * Validate if a given type is a valid transaction type.
     */
    public static function isValidType(string $type): bool
    {
        return in_array($type, self::getAllTypes(), true);
    }
}
