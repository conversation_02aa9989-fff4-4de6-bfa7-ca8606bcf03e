<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Events\EmailSent;
use App\Mail\DomainAuthenticationCode;
use App\Models\User;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Traits\UserContact;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class JobAuthCodeService
{
    use UserContact, UserLoggerTrait;

    private $dispatchDelayInSeconds = 180;

    public static function instance(): self
    {
        $jobAuthCodeService = new self;

        return $jobAuthCodeService;
    }

    public function sendAuthCode($email, $registry_domains, $domainsToBeUpdated, $userId, $maxDomainsPerEmail)
    {
        // Mike Changes

        $user = User::find($userId);
        if (! $user) {
            Log::error("User not found with ID: $userId");

            return;
        }

        $domain_auth = $this->getEppAuthCode($registry_domains, $domainsToBeUpdated, $email);
        $chunked_domains = array_chunk($domain_auth, $maxDomainsPerEmail);

        foreach ($chunked_domains as $batch) {
            $csvFile = $this->generateAuthCodeInCsv($batch);

            //  Mail::to($email)->send(new DomainAuthenticationCode([
            //     'attachment' => $csvFilePath,
            //     'filename' => $filename,
            // ]));

            $mailable = new DomainAuthenticationCode([
                'attachment' => $csvFile['filePath'],
                'filename' => $csvFile['fileName'],
            ]);

            Mail::to($email)->send($mailable);

            $payloadString = json_encode($mailable);
            $name = $user->first_name.' '.$user->last_name;

            event(new EmailSent($user->id, $name, $email, 'Domain Authentication Request', 'Domain Authentication Request', $payloadString, null));

            Storage::disk('public')->delete($csvFile['fileName']);
        }

        // Mike Changes $userId to $user->id

        DomainNotificationService::instance()->sendAuthCodeRequestCompleteNotif($user->id);
    }

    // PRIVATE FUNCTIONS

    private function regenerateAuthCodeIfDue($domainsToBeUpdated, $email)
    {
        $domainsUpdated = [];
        $failedDomainUpdates = [];

        foreach ($domainsToBeUpdated as $domain) {
            $payload = ['name' => $domain['name'], 'regenerateAuthCode' => true];
            $response = EppDomainService::instance()->updateEppDomain($payload, $email);

            if (! array_key_exists('errors', $response)) {
                $domainsUpdated[] = $domain['name'];
            } else {
                $failedDomainUpdates[] = $domain['name'];
            }
        }

        if (! empty($domainsUpdated)) {
            DB::table('domains')->whereIn('name', $domainsUpdated)->whereNull('deleted_at')
                ->update(['auth_code_updated_at' => Carbon::now(), 'updated_at' => Carbon::now()]);
        }

        return $failedDomainUpdates;
    }

    private function getEppAuthCode($registry_domains, $domainsToBeUpdated, $email)
    {
        $domain_auth = [];
        $failedDomainUpdates = $this->regenerateAuthCodeIfDue($domainsToBeUpdated, $email);

        foreach ($registry_domains as $registry => $names) {
            foreach ($names as $chunk) {
                $domainEppData = EppDomainService::instance()->callEppDomainBatchAuthRequest($chunk, $registry, $email);

                if ($domainEppData['status'] === Config::get('domain.status.ok')) {
                    $data = array_filter($domainEppData['data']['results'], function ($item) use ($failedDomainUpdates) {
                        return ! in_array($item['name'], $failedDomainUpdates);
                    });
                    array_push($domain_auth, ...$data);
                }
            }
        }

        return $domain_auth;
    }

    private function generateAuthCodeInCsv($domain_auth)
    {
        $timestamp = Carbon::now()->format('Y-m-d-His');
        $fileName = "authcode_{$timestamp}.csv";
        $csvContent = "\"Name\",\"Authentication\"\n";

        foreach ($domain_auth as $domain) {
            $name = $this->escapeCsvValue($domain['name']);
            $authentication = $this->escapeCsvValue($domain['authentication']);
            $csvContent .= "{$name},{$authentication}\n";
        }

        Storage::disk('public')->put($fileName, $csvContent);
        $filePath = Storage::disk('public')->path($fileName);

        return ['fileName' => $fileName, 'filePath' => $filePath];
    }

    private function escapeCsvValue($value)
    {
        $escapedValue = str_replace('"', '""', $value);

        return "\"{$escapedValue}\"";
    }
}
