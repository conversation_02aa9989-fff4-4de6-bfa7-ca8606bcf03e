<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Events\ClientActivityEvent;
use App\Models\UserTransactionHistory;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Support\Facades\Log;

class ClientActivityListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ClientActivityEvent $event)
    {
        try {
            UserTransactionHistory::create([
                'user_id' => $event->userId,
                'type' => $event->type,
                'message' => $event->message,
                'link' => $event->link,
                'payload' => json_encode($event->payload) ?? null,
            ]);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }
}
