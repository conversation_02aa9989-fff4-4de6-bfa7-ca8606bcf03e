<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\Client\ClientIp;
use App\Util\Helper\RateLimit;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/ForgotPassword', [
            'status' => session('status'),
        ]);
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email:rfc,dns',
        ]);

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.

        $attempt = 5;
        $decay = 900; // 15 minutes
        $clientIp = ClientIp::getClientIp($request);

        if (RateLimit::attempt(RateLimiterKey::forgotPasswordAttempt($clientIp), $attempt, $decay)) {
            $status = Password::sendResetLink(
                $request->only('email')
            );

            // if ($status == Password::RESET_LINK_SENT) {
            //     return back()->with('status', __($status));
            // }

            return back()->with('status', __(Password::RESET_LINK_SENT));
        }

        throw ValidationException::withMessages([
            'email' => 'You have sent too many reset password request.',
        ]);
    }
}
