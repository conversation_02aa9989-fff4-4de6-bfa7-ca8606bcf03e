<?php

namespace App\Listeners\EmailExpiry;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Storage;

class UnlinkAttachments implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(MessageSent $event): void
    {

        try {
            // app(AuthLogger::class)->info('UnlinkAttachments:: '.json_encode($event->data['filename']));
            if (isset($event->data['filename'])) {
                Storage::disk('csv')->delete($event->data['filename']);
            }
        } catch (\Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }
}
