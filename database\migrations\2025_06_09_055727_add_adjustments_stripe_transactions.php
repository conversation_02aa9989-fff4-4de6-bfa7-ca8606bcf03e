<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->addAdjustmentsColumn();
        $this->addBillAmountColumn();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }

    private function addAdjustmentsColumn()
    {
        // check new columns
        if (Schema::hasColumn('stripe_transactions', 'adjustments')) {
            echo 'Column "adjustments" of relation "stripe_transactions" already exists...'.PHP_EOL;

            return;
        }

        // add price column
        Schema::table('stripe_transactions', function ($table) {
            $table->decimal('adjustments', 10, 2)->nullable();
        });
    }

    private function addBillAmountColumn()
    {
        // check new columns
        if (Schema::hasColumn('stripe_transactions', 'bill_amount')) {
            echo 'Column "bill_amount" of relation "stripe_transactions" already exists...'.PHP_EOL;

            return;
        }

        // add price column
        Schema::table('stripe_transactions', function ($table) {
            $table->decimal('bill_amount', 10, 2)->nullable();
        });
    }
};
