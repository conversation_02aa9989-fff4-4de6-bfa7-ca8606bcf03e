<?php

namespace App\Modules\DomainClassificationApi\Jobs;

use App\Models\User;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\DomainClassificationApi\Services\DomainClassificationApiService;
use App\Util\Constant\QueueConnection;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DomainClassificationApiInsertNewDomainJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use UserLoggerTrait;

    private object $domain;

    private int $id;

    private object $registeredDomain;

    private string $registry;

    private int $userId;

    private string $updateType;

    private string $email;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct($payload)
    {
        $this->domain = $payload[JobPayloadKeys::DOMAIN];
        $this->registeredDomain = $payload[JobPayloadKeys::REGISTERED_DOMAIN];
        $this->userId = $payload[JobPayloadKeys::USER_ID];
        $this->registry = $payload[JobPayloadKeys::REGISTRY];
        $this->email = $payload[JobPayloadKeys::EMAIL];
        $this->updateType = $payload[JobPayloadKeys::UPDATE_TYPE];

        $this->id = $this->domain->id;

        $this->onConnection(QueueConnection::DOMAIN_CLASSIFICATION_JOBS);
        $this->onQueue('insert');
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return intval(now()->timestamp.$this->id);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user = User::find($this->userId);

        (new DomainClassificationApiService)->insertClient($user->email, $user->api_key_bodis);
        (new DomainClassificationApiService)->insertDomain($this->domain->name, $this->email);
    }
}
