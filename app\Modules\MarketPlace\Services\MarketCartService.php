<?php

namespace App\Modules\MarketPlace\Services;

use App\Models\MarketCart;
use App\Traits\CursorPaginate;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MarketCartService
{
    use CursorPaginate;

    private $pageLimit = 10;

    public static function instance()
    {
        $marketCartService = new self;

        return $marketCartService;
    }

    public static function addToCart($user_id, $tld_id, $name, $price, $is_fast_transfer, $vendor): void
    {
        $exists = self::checkExist($name);

        if ($exists) {
            throw new \ErrorException;
        }

        DB::table('market_carts')->insert([
            'user_id' => $user_id,
            'tld_id' => $tld_id,
            'name' => strtolower($name),
            'price' => $price,
            'is_fast_transfer' => $is_fast_transfer,
            'vendor' => $vendor,
        ]);
    }

    public function getCollectionById(int|array $id)
    {
        $query = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->orderBy('created_at', 'desc')
            ->when(is_int($id), function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->when(is_array($id), function ($query) use ($id) {
                return $query->whereIn('id', $id);
            })
            ->get();

        return $query ?? null;
    }

    public function get()
    {
        $query = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return $query ?? null;
    }

    public function getAll(): array
    {
        $query = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return ['items' => $query];
    }

    public function getAllArray(): array
    {
        $query = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->orderBy('created_at', 'desc')
            ->get()->all();

        return $query ?? [];
    }

    public function getAllDomainsId(): array
    {
        $query = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->orderBy('created_at', 'desc')
            ->get()->pluck('id')->toArray();

        return $query ?? [];
    }

    public function getCount()
    {
        if (! Auth::user()) {
            return ['items' => 0];
        }

        $query = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->orderBy('created_at', 'desc')
            ->count();

        return ['items' => $query];
    }

    public function removeFromCart($id): void
    {
        MarketCart::where('id', $id)->delete();
    }

    // check domain availability
    public static function checkDomainAvailability($domain): void
    {
        try {
            $res = AfternicMiddleware::instance()::placeHold($domain);

            if ($res->status() == 200) {
                AfternicMiddleware::instance()::releaseHold($domain);
            } else {
                throw new \ErrorException;
            }
        } catch (RequestException $e) {
            throw new \ErrorException;
        }
    }

    public function getCartCount()
    {
        if (! Auth::user()) {
            return 0;
        }

        $query = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->count();

        return $query;
    }

    // private functions

    // check domain on cart
    private static function checkExist($domain)
    {
        return DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->where('name', $domain)
            ->first();
    }
}
