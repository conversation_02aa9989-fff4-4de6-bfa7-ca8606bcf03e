<?php

namespace App\Console\Commands\Epp;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\RegistryName;
use App\Modules\Epp\Services\PollHttpService;
use App\Modules\Epp\Services\PollService;
use App\Modules\MarketPlace\Services\DomainTransferService;
use App\Modules\MarketPlace\Services\MarketDomainService;
use App\Modules\MarketPlace\Services\MarketOfferService;
use App\Modules\Transfer\Services\EppTransferService;
use Exception;
use Illuminate\Console\Command;

class SessionPoll<PERSON>hecker extends Command
{
    private $registries = RegistryName::SUPPORTED;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'epp:session-poll-checker';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'check session polls';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'SessionPollChecker: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('SessionPollChecker: Running...');
        foreach ($this->registries as $registry) {
            $response = PollHttpService::check($registry);
            if (empty($response['data'])) {
                app(AuthLogger::class)->info('SessionPollChecker: Terminating, nothing to process..');

                continue;
            }
            app(AuthLogger::class)->info('SessionPollChecker: Updating poll list...');

            $data = PollService::instance()->getData($response['data']);

            PollService::instance()->store($data['storeData']);
            $this->evaluateTransferData($data['domainTransferData']);
        }

        app(AuthLogger::class)->info('SessionPollChecker: Done');
    }

    private function evaluateTransferData($pollTransferData)
    {
        if (empty($pollTransferData)) return;

        $transferData = $this->sortTransferData($pollTransferData);

        EppTransferService::instance()->updateTransferStatusFromPoll($transferData['transfer']);
        DomainTransferService::instance()->updateTransferStatusFromPoll($transferData['marketplace']);
    }

    private function sortTransferData($pollTransferData): array
    {
        $data['offer'] = [];
        $data['transfer'] = [];
        $data['marketplace'] = [];

        foreach ($pollTransferData as $item) {
            if (!array_key_exists('name', $item)) continue;

            if (MarketDomainService::instance()->isInMarketPlace(strtolower($item['name']))) {
                $data['marketplace'][] = $item;
            } else {
                $data['transfer'][] = $item;
            }
        }

        return $data;
    }
}
