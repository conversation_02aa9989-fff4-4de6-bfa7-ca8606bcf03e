<?php

namespace App\Modules\AccountCredit\Services;

use App\Models\Setting;
use App\Modules\PaymentMethod\Services\PaymentMethodService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Setting\Services\Settings;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\StripeKeyProvider;

use Illuminate\Support\Facades\Auth;

class AccountCreditFundSelectionService
{
    public static function instance(): self
    {
        $AccountCreditFundSelectionService = new self;

        return $AccountCreditFundSelectionService;
    }

    public function getFundSelectionData(bool $isAccountSetup = false)
    {
        //! GET DOMAIN ICANN FEE
        $icanFee = intval(value: Settings::instance()->getValueByKey(SettingKey::DOMAIN_ICANN_FEE));

        $paymentMethods = (new PaymentMethodService)->fetchPaymentMethods(Auth::user()->stripe_customer_id);
        $stripePublicKey = (new StripeKeyProvider)->getPromise();
        $extensionFee = ExtensionFees::instance()->getHighestPricebyType(FeeType::REGISTRATION);

        $minimumDepositAmount = $isAccountSetup ? ceil($extensionFee + $icanFee) : 1;
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($minimumDepositAmount);

        return compact('paymentMethods', 'minimumDepositAmount', 'stripePublicKey', 'stripeFeeObj');
    }
}
