<?php

namespace Database\Seeders;

use App\Util\Constant\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TransactionThresholdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->insertTransactions();
        $this->insertUserTransactions();
        $this->insertThresholdSubscribers();
        $this->insertTransactionTriggers();
        $this->insertTriggerSubscribers();
    }

    private function insertTransactions()
    {
        $payload = [];
        $systemLimit = Transaction::SYSTEM_LIMIT;
        $userLimit = Transaction::USER_LIMIT;
        $length = Transaction::LENGTH;
        $now = Carbon::now();

        foreach (array_keys(Transaction::TYPES) as $transaction) {
            $payload[] = [
                'name' => $transaction,
                'system_limit' => $systemLimit,
                'user_limit' => $userLimit,
                'length' => $length,
                'created_at' => $now,
                'updated_at' => $now
            ];
        }

        DB::table('transactions')->insert($payload);
    }

    private function insertThresholdSubscribers()
    {
        DB::table('threshold_subscribers')->insert(['admin_id' => 1]);
    }

    private function insertTransactionTriggers()
    {
        $transactionIds = DB::table('transactions')->pluck('id')->toArray();

        $payload = [];
        $now = Carbon::now();

        foreach ($transactionIds as $id) {
            $payload[] = [
                'transaction_id' => $id,
                'notify_subscriber' => true,
                'allow_action' => false,
                'updated_at' => $now
            ];
        }

        DB::table('transaction_triggers')->insert($payload);
    }

    private function insertTriggerSubscribers()
    {
        $payload = [];
        $transactionTriggerIds = DB::table('transaction_triggers')->pluck('id')->toArray();

        foreach ($transactionTriggerIds as $id) {
            $payload[] = [
                'threshold_subscriber_id' => 1,
                'transaction_trigger_id' => $id,
            ];
        }

        DB::table('trigger_subscribers')->insert($payload);
    }

    private function insertUserTransactions()
    {
        $payload = [];
        $userIds = DB::table('users')->pluck('id')->toArray();
        $transactionIds = DB::table('transactions')->pluck('id')->toArray();
        $now = Carbon::now();

        foreach ($userIds as $userId) {
            foreach ($transactionIds as $transactionId) {
                $payload[] = [
                    'user_id' => $userId,
                    'transaction_id' => $transactionId,
                    'custom_limit' => 0,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }
        }

        DB::table('user_transactions')->insert($payload);
    }
}
