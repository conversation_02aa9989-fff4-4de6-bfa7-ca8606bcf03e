<?php

namespace App\Modules\Payment\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\SendMailSummaryService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EmailPaymentInvoice implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $id;

    private $userId;

    private $type;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(int $id, int $userId, string $type = PaymentSummaryType::PAYMENT_INVOICE)
    {
        $this->id = $id;
        $this->userId = $userId;
        $this->type = $type;
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return intval(Carbon::now()->timestamp.$this->id);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            SendMailSummaryService::instance()->handle($this->id, $this->userId);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('EmailPaymentInvoice: '.$e->getMessage());
            $this->fail();
        }
    }
}
