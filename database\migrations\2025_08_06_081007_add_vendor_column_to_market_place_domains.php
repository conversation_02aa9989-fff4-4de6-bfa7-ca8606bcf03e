<?php

use App\Modules\MarketPlace\Constants\MarketPlaceVendors;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('market_place_domains', 'vendor')) 
        {
            echo 'Column "vendor" of relation "market_place_domains" already exists...' . PHP_EOL;

            return;
        }

        Schema::table('market_place_domains', function (Blueprint $table) 
            {
                $table->string('vendor')->default(MarketPlaceVendors::AFTERNIC);
            }
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('market_place_domains', 'vendor')) 
        {
            Schema::table('market_place_domains', function (Blueprint $table) 
            {
                $table->dropColumn('vendor');
            });
        }
    }
};
