<?php

namespace App\Modules\UserProfile\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddressUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'unit'          => ['nullable', 'string', 'min:3', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'street'        => ['required', 'string', 'min:3', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'city'          => ['required', 'string', 'min:3', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'stateProvince' => ['required', 'string', 'min:2', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'postalCode'    => ['required', 'string', 'max:8', 'regex:/^[0-9a-zA-Z\s\-]*$/'],
            'countryCode'   => ['required', 'string', 'min:2', 'max:2', 'regex:/^[a-zA-Z]*$/'],
        ];
    }

    public function messages()
    {
        return [
            'state_province.required' => 'The state/province field is required.',
            'country_code.required' => 'The country field is required.',
            'postal_code.regex' => 'The postal code may only contain letters, numbers, spaces and hyphens.',
            'country_code.size' => 'The country code must be 2 characters (ISO format).',
            'country_code.alpha' => 'The country code must contain only letters.',
        ];
    }
}
