<?php

namespace App\Modules\Domain\Services\CreateServices;

use App\Models\RegisteredDomain;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use App\Traits\UserContact;
use App\Util\Helper\Domain\DefaultDomains;
use App\Util\Helper\Domain\DomainParser;
use App\Util\Helper\Domain\DomainTld;
use App\Util\Helper\Store\BulkActions;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class CreatedRegisteredDomains
{
    use UserContact;

    public int $userId;

    public string $registeredStatus;

    public array $contacts;

    public array $tlds;

    public $nameservers;

    public int $categoryId;

    public int $lockinPeriod;

    public const CONTACT_ID = 'id';

    public const NAMESERVER_ID = 'ids';

    public array $rows;

    public Collection $unRegisteredDomains;

    public Collection $registeredDomains;

    public array $domains;

    public function __construct(Collection $unRegisteredDomains, string $status)
    {
        $this->unRegisteredDomains = $unRegisteredDomains ?? DefaultDomains::throwEmptyError('unregistered domains');
        $this->registeredStatus = $status ?? UserDomainStatus::OWNED;

        $this->userId = $this->getUserId();
        $this->contacts = $this->getDefaultContacts($this->userId);
        $this->tlds = DomainTld::getAllTlds();
        $this->nameservers = null;
        $this->categoryId = DefaultDomains::getDefaultCategoryId($this->userId);
        $this->lockinPeriod = intval(value: Settings::instance()->getValueByKey(SettingKey::DOMAIN_LOCKIN_PERIOD));
    }

    public function format()
    {
        foreach ($this->unRegisteredDomains as $item) {
            $registryId = $this->tlds[$item->root]['registry_id'];
            $extensionId = $this->tlds[$item->root]['extension_id'];
            $contactsIdArray = DefaultDomains::getDefaultContactsArray($this->contacts[$registryId], self::CONTACT_ID);
            $yearLength = $item->year_length ?? 1;

            $domain = [
                'user_contact_registrar_id' => $this->contacts[$registryId]['id'][DomainContact::REGISTRANT],
                'extension_id' => $extensionId,
                'domain_id' => $item->id,
                'status' => $this->registeredStatus,
                'locked_until' => now()->addDays($this->lockinPeriod)->timestamp,
                'user_category_id' => $this->categoryId,
                'contacts_id' => json_encode($contactsIdArray),
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $this->rows[] = $domain;
            $this->domains[$item->id]['domain'] = $item;
            $this->domains[$item->id]['year_length'] = $yearLength;
            $this->domains[$item->id]['registry'] = DomainParser::getRegistryName(strtolower($item->name));
        }

        return $this;
    }

    public function store()
    {
        $table = (new RegisteredDomain)->getTable();
        $this->registeredDomains = BulkActions::instance()->bulkInsertGetData($table, $this->rows, false);

        return $this;
    }

    /**
     * @return [$id => ['domain' => $domain, 'registered_domain' => $registeredDomain, 'registry' => $registry]]
     */
    public function mapByDomainId()
    {
        foreach ($this->registeredDomains as $item) {
            $item->year_length = $this->domains[$item->domain_id]['year_length'] ?? 1;
            $this->domains[$item->domain_id]['registered_domain'] = $item;
        }

        return $this;
    }

    public function getDomainsArray()
    {
        return [
            'domains' => $this->domains,
            'registered_domains' => $this->registeredDomains,
        ];
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? DefaultDomains::throwEmptyError('user id');
    }
}
