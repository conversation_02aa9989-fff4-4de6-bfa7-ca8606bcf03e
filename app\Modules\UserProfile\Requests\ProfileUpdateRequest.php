<?php

namespace App\Modules\UserProfile\Requests;

use App\Rules\Form\EmailValidation;
use App\Rules\Form\FirstNameValidation;
use App\Rules\Form\LastNameValidation;
use Illuminate\Foundation\Http\FormRequest;

class ProfileUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        $userId = app()->environment('testing') ? 1 : ($this->user() ? $this->user()->id : null);

        return [
            'first_name' => ['required', new FirstNameValidation()],
            'last_name' => ['required', new LastNameValidation()],
            'email' => ['required', new EmailValidation($userId)],
        ];
    }

    public function messages(): array
    {
        return [
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already in use.',
            'first_name.regex' => 'The first name field must only contain letters.',
            'last_name.regex' => 'The last name field must only contain letters.',
        ];
    }
}
