<?php

namespace App\Modules\UserProfile\Tests\Requests;

use App\Models\User;
use Illuminate\Support\Facades\Validator;
use App\Modules\UserProfile\Requests\UserProfileUpdateEmailFormRequest;
use App\Modules\UserProfile\Tests\Datasets\CommonValidationDataset;

it('fails validation when required fields are missing', function () {
    $validator = Validator::make([], (new UserProfileUpdateEmailFormRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue();
});

it('fails validation when email is already taken by another user', function () {
    User::factory()->create(['email' => '<EMAIL>']);

    $validator = Validator::make([
        'email' => '<EMAIL>',
    ], (new UserProfileUpdateEmailFormRequest)->rules());

    expect($validator->fails())->toBeTrue();
});

it('passes validation with unique email excluding current user', function () {
    $uniqueEmail = '<EMAIL>';

    $validator = Validator::make([
        'email' => $uniqueEmail,
    ], (new UserProfileUpdateEmailFormRequest)->rules());

    expect($validator->fails())->toBeFalse();
});

it('passes validation when current user updates to same email', function () {
    $validator = Validator::make([
        'email' => $this->user->email,
    ], (new UserProfileUpdateEmailFormRequest)->rules());

    expect($validator->fails())->toBeFalse();
});

it('fails validation with invalid email format', function () {
    $invalidDatasets = CommonValidationDataset::invalidEmailData();

    foreach ($invalidDatasets as $testName => $data) {
        $validator = Validator::make($data, (new UserProfileUpdateEmailFormRequest)->rules());
        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
        expect($validator->errors()->has('email'))->toBeTrue("Failed to validate email field for test case: {$testName}");
    }
});