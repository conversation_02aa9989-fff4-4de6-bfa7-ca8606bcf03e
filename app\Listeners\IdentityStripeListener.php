<?php

namespace App\Listeners;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Stripe\Jobs\StripeIdentityJob;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\Cashier\Events\WebhookReceived;

class IdentityStripeListener
{
    /**
     * Handle the event.
     */
    public function handle(WebhookReceived $event): void
    {

        app(AuthLogger::class)->info('Identity Webhook handler called.');
        // Log::info('Webhook Received cashier:: '.json_encode($event));
        StripeIdentityJob::dispatch($event->payload);
    }
}
