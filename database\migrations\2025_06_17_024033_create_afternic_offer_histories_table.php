<?php

use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('afternic_offer_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('afternic_offer_id')->constrained('afternic_offers');
            $table->integer('offer_price');
            $table->integer('counter_offer_price')->nullable()->default(0);
            $table->string('offer_status')->nullable()->default(AfternicOfferConstants::WAITING);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('afternic_offer_histories');
    }
};
