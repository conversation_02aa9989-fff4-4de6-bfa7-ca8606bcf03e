<?php

namespace App\Modules\Domain\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class EmailDomainRedemptionNotice implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $domains;

    private $userId;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(array $domains)
    {
        $this->domains = $domains;
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return intval(Carbon::now()->timestamp);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $data = DB::table('registered_domains')
                ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
                ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
                ->join('users', 'users.id', '=', 'user_contacts.user_id')
                ->whereIn('registered_domains.id', array_column($this->domains, 'registered_domains_id'))
                ->select('domains.name', 'users.email')->get()
                ->groupBy('email')->map(fn ($domains) => $domains->pluck('name')->toArray())
                ->toArray();

        } catch (Exception $e) {
            app(AuthLogger::class)->error('EmailDomainRedemptionNotice: '.$e->getMessage());
            $this->fail();
        }
    }
}
