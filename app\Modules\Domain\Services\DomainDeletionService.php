<?php

namespace App\Modules\Domain\Services;

use App\Models\Domain;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Jobs\RequestDomainAuthCode;
use App\Modules\Notification\Services\DomainNotificationService;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DomainDeletionService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $domainDeletionService = new self;

        return $domainDeletionService;
    }

    public function query(){
        return DB::table('domain_cancellation_requests')
            ->select(
                'domain_cancellation_requests.*',
            );
    }
}