<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\DomainCheckoutRequest;
use App\Modules\MarketPlace\Services\MarketCartService;
use App\Modules\Transfer\Requests\AuthenticationRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class MarketCheckoutController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(DomainCheckoutRequest $request): Response
    {
        return Inertia::render('Market/Checkout/Checkout', ['info' => $request->all()]);
    }

    public function check(Request $request)
    {
        return MarketCartService::instance()->checkDomainAvailability($request->domain);
    }

    public function authConfirm(AuthenticationRequest $request): JsonResponse
    {
        $request->authConfirm();

        return response()->json(['success' => true], 200);
    }
}
