<?php

namespace App\Modules\Auth\Services;

use App\Mail\LoginAttemptsExceededMail;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class AuthenticatedSessionService
{
    // * TRAITS */
    // ...

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Check if Entry exists in Database
     *
     *
     * @retuurn bool
     */
    public function getUserByEmailAndPassword(array $data): ?User
    {
        $user = User::query()
            ->where('email', '=', $data['email'])
            ->where('is_active', '=', true)
            ->first();

        if ($user && Hash::check($data['password'], $user->password)) {
            return $user;
        }

        return null;
    }

    public function authenticateUser(array $data) {}

    /**
     * Check if User Email Exist
     */
    public function checkIfUserEmailExist(string $email): bool
    {
        $user = User::where('email', '=', $email)->first();

        if ($user) {
            return true;
        }

        return false;
    }

    /**
     * Check if User Email Exist
     *
     *
     * @return bool
     */
    public function sendLoginAttemptsExceededMail(string $addressTo): void
    {
        Mail::to($addressTo)
            ->send(new LoginAttemptsExceededMail);
    }
}
