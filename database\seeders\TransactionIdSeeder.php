<?php

namespace Database\Seeders;

use App\Models\PaymentSummary;
use App\Util\Constant\CryptPrefixes;
use App\Util\Helper\CryptHelper;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class TransactionIdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * For old payment_summaries data
     */
    public function run(): void
    {
        $this->updateTransactionId();
    }

    private function updateTransactionId()
    {
        if (! Schema::hasColumn('payment_summaries', 'transaction_id')) {
            echo 'Column "transaction_id" of relation "payment_summaries" does not exist...'.PHP_EOL;

            return;
        }

        $this->runSeeder();
    }

    private function runSeeder()
    {
        PaymentSummary::whereNull('transaction_id')->chunkById(100, function ($paymentSummaries) {
            foreach ($paymentSummaries as $paymentSummary) {
                $paymentSummary->transaction_id = CryptHelper::encrypt(CryptHelper::generate(CryptPrefixes::PAYMENT_SUMMARY));
                $paymentSummary->save();
            }
        });
    }
}
