<?php

namespace App\Modules\Category\Services;

use App\Events\ClientActivityEvent;
use App\Models\UserCategory;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CategoryService
{
    use CursorPaginate;

    private $userId;

    private static $pageLimit = 20;

    public function __construct()
    {
        $this->userId = Auth::user()->id;
    }

    public static function instance()
    {
        $categoryService = new self;

        return $categoryService;
    }

    public function getToBeDeletedCategoryInfo($ids)
    {
        return DB::table('user_categories')
            ->where([['user_id', '=', $this->userId], ['is_default', '!=', true]])
            ->whereNull('deleted_at')
            ->whereIn('id', $ids)
            ->orderBy('name')
            ->select('id', 'name')
            ->get()
            ->all();
    }

    public function getAvailableCategories($ids)
    {
        return DB::table('user_categories')
            ->where('user_id', $this->userId)
            ->whereNull('deleted_at')
            ->whereNotIn('id', $ids)
            ->orderBy('name')
            ->select('id', 'name')
            ->get()
            ->all();
    }

    public function getAll($request)
    {
        $builder = DB::table('user_categories')
            ->leftJoin('registered_domains', 'registered_domains.user_category_id', '=', 'user_categories.id')
            ->leftJoin('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->where('user_categories.user_id', auth()->id())
            ->whereNull('user_categories.deleted_at')
            ->select(
                'user_categories.*',
                DB::raw("SUM(CASE WHEN registered_domains.status = '".UserDomainStatus::OWNED."' 
                        AND user_contacts.user_id = '".auth()->id()."' THEN 1 ELSE 0 END) AS domain_count"),
                // DB::raw('COUNT(registered_domains.user_category_id) as domain_count'))
            )
            ->groupBy('user_categories.id');

        self::whenHasCategory($builder, $request);
        self::whenHasOrderby($builder, $request);

        self::$pageLimit = $request->input('limit', 20);

        $builder = $builder->paginate(self::$pageLimit)->withQueryString();

        return CursorPaginate::cursor($builder, self::paramToURI($request));
    }

    private static function whenHasCategory(&$builder, $request)
    {
        $builder->when($request->has('category'), function (Builder $query) use ($request) {
            $category = $request->category;
            $query->whereRaw('LOWER(name) LIKE ?', [strtolower($category)]);
        });
    }

    private static function whenHasOrderby(&$builder, $request)
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], ['asc', 'desc'])) {
                switch ($orderby[0]) {
                    case 'created':
                        $query->orderBy('user_categories.created_at', $orderby[1]);
                        break;
                    case 'updated':
                        $query->orderBy('user_categories.updated_at', $orderby[1]);
                        break;
                    case 'name':
                        $query->orderBy('user_categories.name', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('user_categories.id', 'asc');
                }
            } else {
                $query->orderBy('user_categories.id', 'desc');
            }
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('user_categories.created_at', 'desc');
            });
    }

    private static function paramToURI($request)
    {
        $param = [];

        if ($request->has('orderby')) {
            $param[] = 'orderby='.$request->orderby;
        }

        if ($request->has('category')) {
            $param[] = 'category='.$request->category;
        }

        return $param;
    }

    public function getCategories(array $ids): array
    {
        $query = DB::table('user_categories')
            ->whereIn('id', $ids)
            ->select('id', 'name')
            ->where('user_categories.user_id', auth()->id())
            ->get();

        $names = $query->pluck('name')->toArray();
        $withId = $query->toArray();

        $categories = ['withId' => $withId, 'names' => $names];

        return $categories;
    }

    public function store(array $request)
    {
        foreach ($request as $name) {
            DB::table('user_categories')->insert([
                'name' => $name,
                'user_id' => auth()->id(),
                'is_default' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            app(AuthLogger::class)->info('Added "'.$name.'" category.');
            event(new ClientActivityEvent(auth()->id(), UserTransactionType::CATEGORY_UPDATE, "User created category: $name", '', $request));
        }
    }

    public function save(array $request)
    {
        foreach ($request['names'] as $index => $value) {
            DB::table('user_categories')
                ->where([['user_id', '=', $this->userId], ['id', '=', $request['withId'][$index]['id']]])
                ->update([
                    'name' => $value,
                    'updated_at' => Carbon::now(),
                ]);
            app(AuthLogger::class)->info('Updated category "'.$request['withId'][$index]['name'].'" to "'.$value.'.');
            event(new ClientActivityEvent(
                $this->userId,
                UserTransactionType::CATEGORY_UPDATE,
                "User updated category from \"{$request['withId'][$index]['name']}\" to \"$value\"",
                '',
                $request
            ));
        }
    }

    private function validateCategory($new)
    {
        $result = DB::table('user_categories')
            ->where([['user_id', '=', $this->userId], ['id', '=', $new]])
            ->select('id')
            ->first();

        return $result !== null;
    }

    private function getUserDefaultCategory()
    {
        return DB::table('user_categories')
            ->where([['user_id', '=', $this->userId], ['is_default', '=', true]])
            ->value('id');
    }

    public function softDelete($ids, $new)
    {
        if ($ids === null) {
            return;
        }

        $query = DB::table('registered_domains')
            ->leftJoin('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->where([['user_contacts.user_id', '=', $this->userId], ['status', '=', UserDomainStatus::OWNED]])
            ->whereIn('user_category_id', $ids);

        $domains = $query->select('registered_domains.domain_id')->get()->all();

        if (count($domains) > 0) {
            $newCategoryId = self::validateCategory($new) ? $new : self::getUserDefaultCategory();
            $query->update(['registered_domains.user_category_id' => $newCategoryId, 'registered_domains.updated_at' => Carbon::now()]);
        }

        UserCategory::where([['user_id', '=', $this->userId], ['is_default', '!=', true]])->whereIn('id', $ids)->delete();

        $names = DB::table('user_categories')
            ->whereIn('id', $ids)
            ->pluck('name')
            ->toArray();

        app(AuthLogger::class)->info('Deleted '.implode(',', $names).count($names) > 1 ? 'category.' : 'categories.');

        event(new ClientActivityEvent(
            $this->userId,
            UserTransactionType::CATEGORY_UPDATE,
            'User deleted category: '.implode(', ', $names),
            '',
            $names
        ));

        $hasDefault = DB::table('user_categories')
            ->whereNull('deleted_at')
            ->where('user_id', $this->userId)
            ->where('is_default', true)
            ->exists();

        if (! $hasDefault) {
            DB::table('user_categories')
                ->where('user_id', $this->userId)
                ->where('name', 'Default')
                ->update(['is_default' => true]);
        }
    }

    public function setDefault($id)
    {
        $userCategory = UserCategory::findOrFail($id);
        $userCategory->update(['is_default' => true]);

        DB::table('user_categories')->where('user_id', $this->userId)
            ->where('id', '!=', $id)
            ->update(['is_default' => false]);

        app(AuthLogger::class)->info('Updated category with name '.$userCategory->name.' set as default');
    }

    public function getCategoryList()
    {
        return DB::table('user_categories')
            ->where('user_id', auth()->id())
            ->whereNull('deleted_at')
            ->get()->all();
    }

    public function getCategoryNames()
    {
        return DB::table('user_categories')
            ->where('user_id', auth()->id())
            ->whereNull('deleted_at')
            ->get()->pluck('name')->toArray();
    }

    public function getById($id)
    {
        return DB::table('user_categories')
            ->where('id', $id)
            ->select('*')
            ->get()->first();
    }
}
