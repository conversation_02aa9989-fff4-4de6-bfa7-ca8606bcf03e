<?php

namespace App\Console\Commands\Notification;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Notification\Services\GeneralNotificationService;
use App\Modules\Notification\Constants\NotificationStatus;
use App\Modules\Notification\Constants\NotificationScheduleType;
use Exception;
use Illuminate\Console\Command;
use Carbon\Carbon;


class GeneralNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:general';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Schedule notifications';

    protected $notificationService;

    public function __construct(GeneralNotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            throw $e;
        }
    }

    // PRIVATE FUNCTIONS

    private function evaluate(): void
    {
        app(AuthLogger::class)->info('GeneralNotification: Running...');

        $notifications = $this->notificationService->getStatus();
        $this->processNotifications($notifications);

        app(AuthLogger::class)->info('GeneralNotification: done');
    }

    private function processNotifications($notifications): void
    {
        $notifications->each(fn($notification) => $this->processNotification($notification));
    }

    private function processNotification($notification): void
    {
        if ($this->isOneTimeActiveNotification($notification)) {
            return;
        }

        if (!$this->isValidStatusForProcessing($notification)) {
            return;
        }

        $this->processRegistrationPeriod($notification);
        if ($this->shouldUpdateSchedule($notification)) {
            $this->updateSchedule($notification);
        }
    }

    private function isValidStatusForProcessing($notification): bool
    {
        if ($notification->schedule_type === NotificationScheduleType::ONE_TIME) {
            return $notification->status === NotificationStatus::PENDING;
        }

        return in_array($notification->status, [
            NotificationStatus::PENDING,
            NotificationStatus::ACTIVE
        ]);
    }

    private function shouldUpdateSchedule($notification): bool
    {
        if ($this->notificationService->checkAndUpdateExpiration($notification)) {
            app(AuthLogger::class)->info(
                "GeneralNotification: Notification ID {$notification->id} marked as expired"
            );
            return false;
        }

        return $this->shouldBecomeActive(
            Carbon::parse($notification->start_date),
            Carbon::parse($notification->time)
        );
    }

    private function updateSchedule($notification): void
    {
        $this->notificationService->updateNotificationWithTimestamp(
            $notification->id,
            ['status' => NotificationStatus::ACTIVE]
        );

        if (in_array($notification->schedule_type, NotificationScheduleType::updateType())) {
            $this->updateNextScheduleDate($notification);
        }
    }

    private function processRegistrationPeriod($notification): void
    {
        if (!$this->notificationService->hasRegistrationPeriod($notification)) {
            return;
        }

        $newUsers = $this->notificationService->getNewUsers($notification);

        if ($newUsers->isNotEmpty()) {
            $this->createNotificationsForNewUsers($notification, $newUsers);
        }
    }

    private function createNotificationsForNewUsers($notification, $newUsers): void
    {
        $notificationData = $this->prepareNotificationData($notification);
        $createdCount = $this->notificationService->createNotificationsForUsers($notificationData, $newUsers);

        if ($createdCount > 0) {
            //     app(AuthLogger::class)->info(sprintf('Created %d notifications for notification ID: %d', $createdCount,
            //         $notification->id
            // ));
        }
    }

    private function prepareNotificationData($notification): array
    {
        return [
            'title' => $notification->title,
            'message' => $notification->message,
            'link_name' => $notification->link_name,
            'time' => $notification->time,
            'start_date' => $notification->start_date,
            'schedule_type' => $notification->schedule_type,
            'status' => $notification->status,
            'type' => $notification->type,
            'expiration' => $notification->expiration,
            'redirect_url' => $notification->redirect_url,
            'min_registration_period' => $notification->min_registration_period,
            'max_registration_period' => $notification->max_registration_period
        ];
    }

    private function shouldBecomeActive(Carbon $startDate, Carbon $time): bool
    {
        $isDatePastOrToday = $startDate->isPast() || $startDate->isToday();
        return $isDatePastOrToday && ($startDate->isPast() || $time->isPast());
    }

    private function updateNextScheduleDate($notification): void
    {
        if (!in_array($notification->schedule_type, NotificationScheduleType::updateType())) {
            return;
        }

        $startDate = Carbon::parse($notification->start_date);
        if (!($startDate->isPast() || $startDate->isToday())) {
            return;
        }

        $this->notificationService->updateStartDate($notification->id, $notification->schedule_type);
    }

    private function isOneTimeActiveNotification($notification): bool
    {
        return $notification->schedule_type === NotificationScheduleType::ONE_TIME &&
            $notification->status === NotificationStatus::ACTIVE;
    }
}
