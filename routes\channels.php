<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('ContactSetup.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('UpdateContactsTable.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('UpdateDomainsTable.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('UpdateIncomingPushTable.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('UpdateOutgoingPushTable.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('UpdateInboundTransferTable.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('UpdateOutboundTransferTable.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('Notifications.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('MyCart.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('MyTransferCart.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('IdentitySetupEvent.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel(
    'UserDomainExportFileGeneration.{id}',
    function ($user, $id) {
        return (int) $user->id === (int) $id;
    }
);
