<?php

namespace App\Modules\Cart\Requests;

use App\Events\MyCartCountEvent;
use App\Exceptions\FailedRequestException;
use App\Modules\Cart\Constants\CartDeleteType;
use App\Modules\Cart\Services\UpdateCartService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class DeleteCartItemRequest extends FormRequest
{
    use UserLoggerTrait;

    public function rules(): array
    {

        return [
            'id' => ['required', 'exists:carts,id'],
            'delete_type' => ['required', 'string', Rule::in(CartDeleteType::ALL)],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    public function delete(): void
    {
        UpdateCartService::instance()->softDelete($this->all());
        MyCartCountEvent::dispatch(Auth::user()->id);
    }
}
