<?php

namespace App\Modules\Domain\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Domain\Requests\DomainSearchRequest;
use App\Modules\Domain\Requests\ShowListRequest;
use App\Modules\Domain\Requests\Product\ShowListRequest as ProductShowListRequest;
use App\Modules\Domain\Services\ViewServices\ViewDomainService;
use App\Modules\MarketPlace\Services\MarketDomainService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ViewController extends Controller
{
    public function get(ShowListRequest $request): Response
    {
        $data = $request->getAll();

        return Inertia::render('Domain/Index', $data);
    }

    public function searchDomains(DomainSearchRequest $request): RedirectResponse
    {
        session()->put('searched_domains', $request->domains);

        return redirect()->route('domain', $request->filters);
    }

    public function clearSearchedDomains(Request $request): RedirectResponse
    {
        session()->forget('searched_domains');

        return redirect()->route('domain', $request->filters);
    }

    public function view(Request $request): Response
    {
        $data = ViewDomainService::instance()->getDomainView($request->id);

        return Inertia::render('Domain/DomainView', $data);
    }

    public function edit(Request $request): Response
    {
        $data = ViewDomainService::instance()->getDomainEdit($request->id);

        return Inertia::render('Domain/Edit', $data);
    }

    public function products(ProductShowListRequest $request)
    {
        $data = $request->getAll();
        return Inertia::render('Domain/Products', $data);
    }
}
