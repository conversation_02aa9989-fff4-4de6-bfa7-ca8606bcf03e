//* PACKAGES
import React, { useState, useEffect, useRef } from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import useOutsideClick from "@/Util/useOutsideClick";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";

//* ICONS
//...

//* COMPONENTS
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import CheckFilter from "@/Components/Util/Filter/CheckFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function FilterPushOutgoing(
    {
        orderbyItems,
        tabName
    }
) {
    //! PACKAGE
    const { orderby, email } = route().params;
    const refContainer = useRef();

    //! VARIABLES
    const [stateInputEmail, setStateInputEmail] = useState(email || "");

    const config =
    {
        container:
        {
            active: false,
        },
        field:
        {
            orderby:
            {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: [...orderbyItems],
                name: "Order By",
            },
            email:
            {
                active: false,
                value: email ? [email] : [],
                type: "text",
                name: "Email",
                tempValue: stateInputEmail,
            },
        },
    };

    //! STATES
    const [filter, setFilter] = useState(config);
    const { field } = filter;

    //! FUNCTIONS
    useOutsideClick(
        refContainer,
        () => {
            setFilter(
                (prevFilter) => {
                    const updatedFilter = offFilter(prevFilter);

                    return {
                        ...updatedFilter,
                        field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                            ...acc,
                            [key]: {
                                ...updatedFilter.field[key],
                                active: false
                            }
                        }), {})
                    };
                }
            );
        }
    );

    function handleSubmit(updatedFilter) {
        let { orderby, email } = updatedFilter.field;
        let payload = {};

        if (tabName) payload.tab = tabName;
        if (orderby.value.length > 0) payload.orderby = orderby.value[0];
        if (email.value.length > 0) payload.email = email.value[0];

        router.get(route("outgoing", payload));
    };

    function handleDisplayToggle(newObject) {
        const closedFilter = offFilter(filter);

        setFilter({
            ...closedFilter,
            ...newObject
        });
    };

    function handleFieldUpdateValue(key, value) {
        if (key == "email") {
            setStateInputEmail(value);

            if (!value || value === stateInputEmail) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter =
                {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue }
                    },
                };

                setFilter(offFilter(updatedFilter));
                handleSubmit(updatedFilter);

                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    email: {
                        ...prevFilter.field.email,
                        tempValue: value
                    }
                }
            }));

            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        handleSubmit(updatedFilter);
    };

    return (
        <div
            className="flex items-center relative"
            ref={refContainer}
        >
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div
                className="relative"
            >
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    placeholder='Search Email'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={
                        () => {
                            const currentValue = field.email.tempValue || field.email.value[0] || "";

                            handleFieldUpdateValue("email", currentValue);
                            setFilter(offFilter(filter));
                        }
                    }
                />
            </div>
        </div>
    );
}
