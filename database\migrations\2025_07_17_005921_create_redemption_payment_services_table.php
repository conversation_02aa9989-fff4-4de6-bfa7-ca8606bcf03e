<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('redemption_payment_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('redemption_order_id')->constrained('redemption_orders')->onDelete('cascade');
            $table->foreignId('payment_service_id')->constrained('payment_services')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('redemption_payment_services');
    }
};
