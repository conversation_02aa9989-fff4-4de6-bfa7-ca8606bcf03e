<?php

namespace App\Modules\Contact\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Contact\Requests\CreateContactRequest;
use App\Modules\Contact\Requests\EditContactRequest;
use App\Modules\Contact\Requests\SetDefaultContactRequest;
use App\Modules\Contact\Requests\ShowListRequest;
use App\Modules\Contact\Requests\UpdateContactRequest;
use App\Modules\Contact\Requests\ViewContactRequest;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class ContactController extends Controller
{
    public function index(ShowListRequest $request): Response
    {
        return Inertia::render('Contact/Index', $request->getData());
    }

    public function create()
    {
        return Inertia::render('Contact/New', ['setup' => true]);
    }

    public function store(CreateContactRequest $request): RedirectResponse
    {
        $request->store();

        return redirect()->route('contact');
    }

    public function show(ViewContactRequest $request): Response
    {
        return Inertia::render('Contact/Show', ['data' => $request->data]);
    }

    public function edit(EditContactRequest $request): Response
    {
        return Inertia::render('Contact/Edit', ['data' => $request->data]);
    }

    public function update(UpdateContactRequest $updateRequest): RedirectResponse
    {
        $updateRequest->update();

        return redirect()->route('contact');
    }

    public function setDefaultContact(SetDefaultContactRequest $request): void
    {
        $request->setDefault();
    }
}
