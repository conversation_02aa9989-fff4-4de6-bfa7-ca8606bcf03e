<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class CartCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required',
            'domain' => 'required|string',
            'price' => 'required',
            'is_fast_transfer' => 'required',
            'tld_id' => 'required',
            'vendor' => 'required',
        ];
    }

    public function getPaymentDetails()
    {
        $domain = (object) [
            'id' => $this->id,
            'domain' => $this->domain,
            'tld_id' => intval($this->tld_id),
            'price' => floatval($this->price),
            'year_length' => 1,
            'vendor' => $this->vendor,
        ];

        $data = [
            'domains' => [$domain],
        ];

        $transfer_fees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::TRANSFER);
        $other_fees = PaymentFeeService::getOtherTransferFees($data['domains'], $transfer_fees);
        $other_fees['bill_total'] += $domain->price;

        $data['other_fees'] = $other_fees;
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($other_fees['bill_total'] ?? 0);
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($stripeFeeObj['gross_amount'] ?? $other_fees['bill_total']);
        $setupIntents = PaymentIntentProvider::instance()->create($payment);
        $accountCredit = AccountCreditService::instance()->getLatestBlock(Auth::user()->id);

        $data['secret'] = $setupIntents->client_secret;
        $data['intent'] = $setupIntents->id;
        $data['promise'] = Config::get('stripe.publishable_key');
        $data['account_credit_balance'] = $accountCredit->running_balance ?? 0;
        $data['stripeFeeObj'] = $stripeFeeObj;

        return $data;
    }
}
