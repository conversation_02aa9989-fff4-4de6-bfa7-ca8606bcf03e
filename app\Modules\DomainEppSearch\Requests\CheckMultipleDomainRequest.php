<?php

namespace App\Modules\DomainEppSearch\Requests;

use App\Modules\DomainEppSearch\Services\SearchDomainService;
use App\Rules\ValidFormat;
use Illuminate\Foundation\Http\FormRequest;

class CheckMultipleDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {

        return [
            'domains' => ['required', 'string', new ValidFormat('domain_search')],
            'extensions' => ['required', 'array'],
        ];
    }

    public function search(): array
    {
        return SearchDomainService::instance()->searchDomains($this->all());
    }
}
