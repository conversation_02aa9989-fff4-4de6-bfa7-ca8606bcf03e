<?php

use App\Modules\MarketPlace\Controllers\MarketOfferController;
use Illuminate\Support\Facades\Route;

// Route::get('/', [SearchController::class, 'index'])->name('search');
// Route::get('/search/{search}', [SearchController::class, 'show']);
// Route::post('/search/{search}/{filters}', [SearchController::class, 'search']);
// Route::post('/eppsearch', [SearchController::class, 'check'])->name('epp.domain.check');

Route::middleware(['auth', 'auth.active', 'account.setup'])->group(function () {
    // Route::get('/cart', [MarketCartController::class, 'index'])->name('marketcart');

    // Route::post('/cartaddMarket', [MarketCartController::class, 'store'])->name('marketcart.add');
    // Route::post('/cartaddEpp', [MarketCartController::class, 'toEPPCart'])->name('marketcart.epp.add');

    // Route::post('/cartremove', [MarketCartController::class, 'destroy'])->name('marketcart.remove');

    // Route::post('/check', [MarketCheckoutController::class, 'check'])->name('marketdomain.check');
    // Route::post('/checkout', [MarketCheckoutController::class, 'index'])->name('marketdomain.checkout');
    // Route::post('/checkoutauth', [MarketCheckoutController::class, 'authConfirm'])->name('marketdomain.checkout.auth');

    // Route::post('/payment', [MarketPaymentController::class, 'showPayment'])->name('marketdomain.checkout.payment');
    // Route::post('/paymentstore', [MarketPaymentController::class, 'store'])->name('marketdomain.payment.store');

    // Route::get('/purchases', [MarketDomainController::class, 'index'])->name('marketdomain');
    
    Route::get('/offers', [MarketOfferController::class, 'index'])->name('offers');
    Route::post('/offers', [MarketOfferController::class, 'store'])->name('marketoffer.store');
    Route::post('/offers/history', [MarketOfferController::class, 'history'])->name('marketoffer.history');
    Route::post('/offers/update', [MarketOfferController::class, 'close'])->name('marketoffer.update');
    Route::post('/offers/counter', [MarketOfferController::class, 'counter'])->name('marketoffer.counter');
    Route::post('/offers/checkout', [MarketOfferController::class, 'checkout'])->name('marketoffer.checkout');
    Route::post('/offers/checkout/store', [MarketOfferController::class, 'checkoutStore'])->name('marketoffer.checkout.store');
});
