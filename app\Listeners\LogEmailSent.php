<?php

namespace App\Listeners;

use App\Events\EmailSent;
use App\Models\EmailHistory;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;


class LogEmailSent implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(EmailSent $event)
    {
        try {
            EmailHistory::create([
                'user_id' => $event->userId,
                'name' => $event->name,
                'recipient_email' => $event->recipient_email,
                'subject' => $event->subject,
                'email_body' => $event->email_body,
                'email_type' => $event->email_type,
                'attachment' => $event->attachment,
            ]);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('Error creating email history: ' . $e->getMessage());
        }     
    }
}