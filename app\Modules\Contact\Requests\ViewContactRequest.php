<?php

namespace App\Modules\Contact\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Contact\Services\ContactService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class ViewContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'data' => ['required', 'array'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $data = is_numeric($this->id)
            ? (array) ContactService::instance()->getUserContact($this->id) : [];

        $this->merge(['data' => $data]);
    }

    protected function failedValidation(Validator $validator)
    {
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }
}
