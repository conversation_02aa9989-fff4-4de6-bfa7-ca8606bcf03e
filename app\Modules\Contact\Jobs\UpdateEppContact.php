<?php

namespace App\Modules\Contact\Jobs;

use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\Contact\Services\JobContactService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class UpdateEppContact implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct($contactId, $localData, $eppData, $registry, $email, $userId)
    {
        $this->params = [
            'contactId' => $contactId,
            'localData' => $localData,
            'eppData' => $eppData,
            'registry' => $registry,
            'email' => $email,
            'userId' => $userId,
        ];

        $this->onConnection(QueueConnection::CONTACT_UPDATE);
        $this->onQueue(QueueTypes::CONTACT_UPDATE[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            JobContactService::instance()->update($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $this->params['email']));

            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();

                return;
            }

            $this->fail();
        }
    }

    public function failed(?Throwable $exception): void
    {
        // Send user notification of failure, etc...
    }

    public function retry(): void
    {
        $status = ContactStatus::IN_PROCESS;
        $type = QueueConnection::CONTACT_UPDATE;
        $jobId = $this->params['contactId'];

        JobContactService::instance()->addRetryLogs($type, $status, $this->params, $jobId);
    }
}
