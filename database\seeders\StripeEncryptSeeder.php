<?php

namespace Database\Seeders;

use App\Models\IdentityVerificationEvent;
use App\Models\StripeTransaction;
use App\Modules\Stripe\Constants\StripePrefixes;
use App\Util\Helper\CryptHelper;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class StripeEncryptSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->encryptStripeTransactions();
        $this->encryptIdentityVerificationEvents();
    }

    private function encryptStripeTransactions(): void
    {
        StripeTransaction::whereNotNull('payment_intent')->chunkById(100, function ($stripeTransactions) {
            foreach ($stripeTransactions as $stripeTransaction) {
                if ($stripeTransaction->payment_intent && Str::startsWith($stripeTransaction->payment_intent, StripePrefixes::PAYMENT_INTENT)) {
                    $stripeTransaction->payment_intent = CryptHelper::encrypt($stripeTransaction->payment_intent);
                }

                if ($stripeTransaction->refund_id && Str::startsWith($stripeTransaction->refund_id, StripePrefixes::REFUND)) {
                    $stripeTransaction->refund_id = CryptHelper::encrypt($stripeTransaction->refund_id);
                }

                if ($stripeTransaction->charge_id && Str::startsWith($stripeTransaction->charge_id, StripePrefixes::CHARGE)) {
                    $stripeTransaction->charge_id = CryptHelper::encrypt($stripeTransaction->charge_id);
                }

                $stripeTransaction->save();
            }
        });
    }

    private function encryptIdentityVerificationEvents(): void
    {
        IdentityVerificationEvent::whereNotNull('identity_id')->chunkById(100, function ($identityEvents) {
            foreach ($identityEvents as $identityEvent) {
                if ($identityEvent->identity_id && Str::startsWith($identityEvent->identity_id, StripePrefixes::IDENTITY)) {
                    $identityEvent->identity_id = CryptHelper::encrypt($identityEvent->identity_id);
                }

                $identityEvent->save();
            }
        });
    }
}
