<?php

namespace App\Modules\PaymentService\Services\Interfaces;

use App\Exceptions\FailedRequestException;
use App\Models\PaymentService;
use App\Modules\PaymentService\Contracts\PaymentServiceInterface;
use App\Modules\SystemCredit\Services\SystemCreditService;
use Illuminate\Support\Facades\Auth;

class SystemCreditPaymentService implements PaymentServiceInterface
{
    public function pay(array $data, int $userId): array
    {
        $createdCredit = SystemCreditService::instance()->getSystemCredit($data);

        $servicePayload = [
            'user_id' => $userId ?? $this->getUserId(),
            'system_credit_id' => $createdCredit->id,
        ];
        $paymentService = PaymentService::create($servicePayload);

        return [
            'payment_service' => $paymentService,
            'data' => null,
        ];
    }

    public function refund(array $data, int $userId)
    {
        throw new FailedRequestException(400, 'Refund not supported.', 'Error');
    }

    public function getPaymentService(object $paymentService)
    {
        return SystemCreditService::instance()->getPaymentServiceView($paymentService);
    }

    // PRIVATE FUNCTIONS
    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }
}
