<?php

namespace App\Modules\Client\Constants;

final class ScheduleType
{
    public const FIRST = 'FIRST';

    public const SECOND = 'SECOND';

    public const THIRD = 'THIRD';

    public const ALL = 'ALL';

    public const EXPIRY_RESTORE_COUNT = 30;

    public const FIRST_COUNT = 30;

    public const SECOND_COUNT = 7;

    public const THIRD_COUNT = 5;

    public const HOUR_THROTTLE = 2;

    public const MAX_LAPSE_IN_DAYS = 5; // 5 day group interval

    public const TIMEOUT_DEV = 300; // 5 minutes

    public const TIMEOUT_PROD = 3000; // 50 minutes

    public const GET_ALL = [
        self::FIRST,
        self::SECOND,
        self::THIRD,
    ];
}
