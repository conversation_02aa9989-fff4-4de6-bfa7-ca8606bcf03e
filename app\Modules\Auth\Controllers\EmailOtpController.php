<?php

namespace App\Modules\Auth\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Auth\Requests\TwoFactorAuthenticationVerifyCodeEmailFormRequest;
use App\Modules\Auth\Services\EmailOtpService;
use App\Modules\Auth\Services\UserIpAddressSessionService;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\Client\ClientIp;
use App\Util\Helper\RateLimit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class EmailOtpController extends Controller
{
    /* TRAITS */
    // ?...

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Render Page
     */
    public function renderPage(string $token): Response
    {
        return Inertia::render(
            'Auth/Login2FaVerification/Login2FaVerificationEmail',
            [
                'token' => $token,
            ]
        );
    }

    /**
     * Verify Code
     */
    public function verifyCode(TwoFactorAuthenticationVerifyCodeEmailFormRequest $request, string $token)
    {
        $rateLimitKey = RateLimiterKey::loginAttemptEmailOtp($token);

        $executed = RateLimit::attempt($rateLimitKey, 5, 30);

        if (! $executed) {
            throw ValidationException::withMessages(['code' => 'Too many attempts. Wait for 30 seconds before trying again.']);
        }

        $userId = (new EmailOtpService)->verifyCode($token, $request->only(['code']));

        if ($userId) {
            Auth::loginUsingId($userId, session('rememberMe', false));

            (new UserIpAddressSessionService)->recordEntry($userId, ClientIp::getClientIp($request));

            return Inertia::location(route('domain'));
        }
    }

    /**
     * Resend Code
     */
    public function resendCode(string $token)
    {
        $rateLimitKey = RateLimiterKey::loginAttemptEmailOtpResend($token);

        $executed = RateLimit::attempt($rateLimitKey, 1, 120);

        if (! $executed) {
            throw ValidationException::withMessages(['rateLimitMessage' => 'Wait for 2 minutes before resending another email.']);
        }

        $entry = (new EmailOtpService)->regenerateEntry($token);

        (new EmailOtpService)->sendEmail(
            $entry['userEmail'],
            $entry
        );
    }
}
