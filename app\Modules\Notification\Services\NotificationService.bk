<?php

namespace App\Modules\Notification\Services;

use App\Events\NotificationEvent;
use App\Events\UpdateContactsTableEvent;
use App\Events\UpdateDomainsTableEvent;
use App\Events\UpdateInboundTransferTableEvent;
use App\Events\UpdateIncomingPushTableEvent;
use App\Events\UpdateOutboundTransferTableEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Epp\Constants\EppErrorCodes;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Traits\CursorPaginate;
use App\Util\Constant\TableEvents;
use App\Util\Constant\TableTypes;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class NotificationService
{
    use CursorPaginate;
    use UserLoggerTrait;

    private $pageLimit = 20;

    private $now;

    private $userEmail;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        $notificationService = new self;

        return $notificationService;
    }

    public function getAll()
    {
        $query = DB::table('notifications')
            ->where('user_id', auth()->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate($this->pageLimit);

        return CursorPaginate::cursor($query);
    }

    public function get_dropdown(int $user_id)
    {
        return DB::table('notifications')
            ->where('user_id', $user_id)
            ->orderBy('created_at', 'desc')
            ->take(15)
            ->get();
    }

    public function setRead($id)
    {
        DB::table('notifications')
            ->where('id', $id)
            ->whereNull('read_at')
            ->update([
                'read_at' => $this->now,
                'updated_at' => $this->now,
            ]);

        $redirect_url = DB::table('notifications')
            ->where('id', $id)
            ->value('redirect_url');

        return $redirect_url;
    }

    public function getUnreadCount(int $user_id)
    {
        return DB::table('notifications')
            ->where('user_id', $user_id)
            ->whereNull('read_at')
            ->count();
    }

    private function sendNotification(array $data): void
    {
        DB::table('notifications')->insert($data);
    }

    private function dispatchNotifUpdateEvent(array $user_ids, array $channel_data = null)
    {
        foreach ($user_ids as $user_id) NotificationEvent::dispatch($user_id);
    }

    private function dispatchTableUpdateEvents(array $channel_data): void
    {
        foreach ($channel_data as $user_id => $user) {
            foreach ($user as $table_type) {
                $event = TableEvents::EVENTS[$table_type];
                $event::dispatch($user_id);
            }
        }
    }

    private function generateNotifPayload(string $id, string $title, string $route): array
    {
        return [
            'user_id' => $id,
            'title' => $title,
            'redirect_url' => route($route),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ];
    }

    // Custom Notifications

    public function sendPushRequestNotif(string $userReceiverId, string $receiverEmail, array $domains, int $domainCount): void
    {
        $notificationPayload = $this->generateNotifPayload($userReceiverId, 'Push Request Received', 'incoming');
        $wordDomain = $domainCount > 1 ? 'domains ' : 'domain ';
        $notificationPayload['message'] = 'You have a push request for the ' . $wordDomain . $domains['notif'] . ' from ' . auth()->user()->email . '. Please click to view the request and take appropriate action.';

        $this->sendNotification($notificationPayload);

        NotificationEvent::dispatch($userReceiverId);
        UpdateIncomingPushTableEvent::dispatch($userReceiverId);
        app(AuthLogger::class)->info($this->fromWho('sent a push request for the ' . $wordDomain . $domains['log'] . ' to ' . $receiverEmail . '.'));
    }

    public function sendPushAcceptNotif(array $notifsData, string $email, string $sender_userId): void
    {
        $data = [];
        $user_ids = array_column($notifsData, 'user_id');
        $channel_data[$sender_userId] = [TableTypes::INCOMING_PUSH, TableTypes::DOMAIN];

        foreach ($notifsData as $userId => $item) {
            $wordDomain = $item['domain_count'] > 1 ? 'domains ' : 'domain ';
            $data[] = [
                'user_id' => $userId,
                'title' => 'Push Request Approved',
                'message' => 'Your push request for the ' . $wordDomain . $item['domain_notif'] . ' to ' . $item['sender_email'] . ' was approved. Click to check your current domains.',
                'redirect_url' => route('domain'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];

            $channel_data[$userId] = [TableTypes::OUTGOING_PUSH, TableTypes::DOMAIN];
            app(AuthLogger::class)->info($this->fromWho('approved the push request from ' . $item['receiver_email'] . ' for the ' . $wordDomain . $item['domain_log'] . '.', $email));
        }

        $this->sendNotification($data);
        $this->dispatchNotifUpdateEvent($user_ids, $channel_data);
        $this->dispatchTableUpdateEvents($channel_data);
    }

    public function sendPushDeclineNotif(array $notifsData, string $email): void
    {
        $data = [];
        $channel_data = [];
        $user_ids = array_column($notifsData, 'user_id');

        foreach ($notifsData as $userId => $item) {
            $wordDomain = $item['domain_count'] > 1 ? 'domains ' : 'domain ';
            $data[] = [
                'user_id' => $userId,
                'title' => 'Push Request Declined',
                'message' => 'Sorry, your push request for the ' . $wordDomain . $item['domain_notif'] . ' to ' . $item['sender_email'] . ' was declined. Click to send another push request.',
                'redirect_url' => route('incoming'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];

            $channel_data[$userId] = [TableTypes::OUTGOING_PUSH, TableTypes::DOMAIN];
            app(AuthLogger::class)->info($this->fromWho('declined the push request from ' . $item['receiver_email'] . ' for the ' . $wordDomain . $item['domain_log'] . '.', $email));
        }

        $this->sendNotification($data);
        $this->dispatchNotifUpdateEvent($user_ids);
        $this->dispatchTableUpdateEvents($channel_data);
    }

    public function sendPushCancelNotif(array $notifsData, string $email): void
    {
        $data = [];
        $channel_data = [];
        $user_ids = array_column($notifsData, 'user_id');

        foreach ($notifsData as $userId => $item) {
            $wordDomain = $item['domain_count'] > 1 ? 'domains ' : 'domain ';
            $data[] = [
                'user_id' => $userId,
                'title' => 'Push Request Canceled',
                'message' => 'Sorry, the push request for the ' . $wordDomain . $item['domain_notif'] . ' from ' . $item['sender_email'] . ' was canceled. Click to send a push request.',
                'redirect_url' => route('outgoing'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];

            $channel_data[$userId] = [TableTypes::INCOMING_PUSH];
            app(AuthLogger::class)->info($this->fromWho('canceled the push request to ' . $item['receiver_email'] . ' for the ' . $wordDomain . $item['domain_log'] . '.', $email));
        }

        $this->sendNotification($data);
        $this->dispatchNotifUpdateEvent($user_ids);
        $this->dispatchTableUpdateEvents($channel_data);
    }

    public function sendPushExpiredNotif(array $notifsData, string $email): void
    {
        $data = [];
        $channel_data = [];
        $user_ids = array_column($notifsData, 'user_id');

        foreach ($notifsData as $userId => $item) {
            $wordDomain = $item['domain_count'] > 1 ? 'domains ' : 'domain ';
            $data[] = [
                'user_id' => $userId,
                'title' => 'Push Request Expired',
                'message' => 'Sorry, the push request for the ' . $wordDomain . $item['domain_notif'] . ' from ' . $item['sender_email'] . ' has already expired and you can no longer respond to this request. Click to send new push request.',
                'redirect_url' => route('incoming'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];

            $channel_data[$userId] = [TableTypes::INCOMING_PUSH];
            app(AuthLogger::class)->info($this->fromWho('push request to ' . $item['receiver_email'] . ' for the ' . $wordDomain . $item['domain_log'] . ' has been expired.', $email));
        }

        $this->sendNotification($data);
        $this->dispatchNotifUpdateEvent($user_ids);
        $this->dispatchTableUpdateEvents($channel_data);
    }

    public function sendContactInProcessNotif(array $notifsData): void
    {
        $data = [];

        foreach ($notifsData as $notifData) {
            $data[] = [
                'user_id' => $notifData['user_id'],
                'title' => 'Contact is In Process',
                'message' => 'Contact ID "' . strtoupper($notifData['contact']) . '" for "' . strtoupper($notifData['registry']) . '" is currently being processed. Please wait for further updates.',
                'redirect_url' => route('contact'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];

            app(AuthLogger::class)->info($this->fromWho('created a new contact for ' . $notifData['registry'] . ' with registry contact ' . $notifData['contact'] . '.'));
        }

        $this->sendNotification($data);
    }

    public function sendContactCreatedNotif(array $notifData): void
    {
        $user_id = $notifData['user_id'];

        $data = [
            'user_id' => $user_id,
            'title' => 'Contact Registration Success',
            'message' => 'Contact ID "' . strtoupper($notifData['contact']) . '" for "' . strtoupper($notifData['registry']) . '" was successfully created. Click to view your contacts.',
            'redirect_url' => route('contact'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ];

        $this->sendNotification($data);
        NotificationEvent::dispatch($user_id);
        UpdateContactsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('successfully created a new contact for ' . $notifData['registry'] . ' with registry contact ' . $notifData['contact'] . '.', $notifData['email']));
    }

    public function sendContactUpdatedNotif(array $notifData): void
    {
        $user_id = $notifData['user_id'];

        $data = [
            'user_id' => $user_id,
            'title' => 'Contact Update Success',
            'message' => 'Contact ID "' . strtoupper($notifData['contact']) . '" for "' . strtoupper($notifData['registry']) . '" was successfully updated. Click to view contacts.',
            'redirect_url' => route('contact'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ];

        $this->sendNotification($data);
        NotificationEvent::dispatch($user_id);
        UpdateContactsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('successfully updated contact for ' . $notifData['registry'] . ' with registry contact ' . $notifData['contact'] . '.', $notifData['email']));
    }

    public function sendBulkDomainInProcessStatusNotif(array $domains): void
    {
        $data = [];

        foreach ($domains as $domain) {
            $data[] = [
                'user_id' => auth()->id(),
                'title' => 'Domain is In Process',
                'message' => 'The payment invoice for the registration of the domain "' . strtoupper($domain) . '" is now being processed.',
                'redirect_url' => route('domain'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];
        }

        $this->sendNotification((array) $data);
        app(AuthLogger::class)->info($this->fromWho('Domains on process: ' . implode(',', $domains)));
    }

    public function sendDomainFailedStatusNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Registration Failed',
            'message' => 'Sorry, the registration for the domain "' . strtoupper($domain) . '" was unsuccessful. A refund is in process. Click to view your domains.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
    }

    public function sendDomainAlreadyRegistered(string $domain, string $userId): void
    {
        $this->sendNotification([
            'user_id' => $userId,
            'title' => 'Domain Already Exists',
            'message' => 'Sorry, the registration for the domain "' . strtoupper($domain) . '" was unsuccessful. A refund is in process. Click to view your domains.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($userId);
    }

    public function sendDomainRefund(string $domain, string $userId): void
    {
        $this->sendNotification([
            'user_id' => $userId,
            'title' => 'Refund Request Initiated',
            'message' => 'Refund for domain "' . strtoupper($domain) . '" was requested. Click to view payment summary.',
            'redirect_url' => route('payment.summary'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);
    }

    public function sendDomainRefundFailed(string $domain, string $userId): void
    {

        $this->sendNotification([
            'user_id' => $userId,
            'title' => 'Refund Request Failed',
            'message' => 'Refund request for domain "' . strtoupper($domain) . '" has failed. Please contact administrator.',
            'redirect_url' => route('payment.summary'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);
    }

    public function sendBulkDomainFailedStatusNotif(array $domains): void
    {
        $data = [];

        foreach ($domains as $domain) {
            $data[] = [
                'user_id' => auth()->user()->id,
                'title' => 'Domain Registration Failed',
                'message' => 'Sorry, the registration for the domain "' . strtoupper($domain) . '" was unsuccessful. Click to view your domains.',
                'redirect_url' => route('domain'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];
        }

        $this->sendNotification((array) $data);
        app(AuthLogger::class)->info($this->fromWho('Failed to register domains: ' . implode(',', $domains)));
    }

    public function sendDomainActiveStatusNotif(string $domain, string $userId): void
    {
        $this->sendNotification([
            'user_id' => $userId,
            'title' => 'Domain Registration Success',
            'message' => 'Congratulations! The registration for the domain "' . strtoupper($domain) . '" was successful and is now ACTIVE. Click to view your domains.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        $this->dispatchNotifUpdateEvent([$userId], [$userId => [TableTypes::DOMAIN]]);
        app(AuthLogger::class)->info($this->fromWho('Notification successfully created ' . $domain . ' domain and is now active.'));
    }

    public function sendAuthCodeRequestInProcessNotif(): void
    {
        $this->sendNotification([
            'user_id' => auth()->user()->id,
            'title' => 'Auth Code is In Process',
            'message' => 'Your request is currently being processed. Kindly await an email that will include the attached CSV file containing the authentication code/s.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);
    }

    public function sendAuthCodeRequestCompleteNotif(string $userId): void
    {
        $this->sendNotification([
            'user_id' => $userId,
            'title' => 'Auth Code was Sent',
            'message' => 'Your request has been successfully processed. Please check your inbox.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($userId);
    }

    public function sendBulkDomainTransferPendingRequestNotif(array $domains): void
    {
        $data = [];

        foreach ($domains as $domain) {
            $data[] = [
                'user_id' => auth()->id(),
                'title' => 'Domain Transfer Pending Request',
                'message' => 'Your domain transfer request for "' . strtoupper($domain) . '" is currently being processed. Please be patient as we work on your request.',
                'redirect_url' => route('transfer.inbound'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];
        }

        $this->sendNotification((array) $data);
        app(AuthLogger::class)->info($this->fromWho('Domains on process: ' . implode(',', $domains)));
    }

    public function sendTransferPendingApprovalNotif(string $domain, string $user_id, string $email): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Pending Approval',
            'message' => 'Your domain transfer request for "' . strtoupper($domain) . '" is currently awaiting approval. Please await further notification regarding your request.',
            'redirect_url' => route('transfer.inbound'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateInboundTransferTableEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('transfer request for ' . $domain . ' is now on pending approval.', $email));
    }

    public function sendPollTransferPendingNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Pending',
            'message' => 'You have a pending domain transfer for "' . strtoupper($domain) . '". Please click to view the request and take the appropriate action.',
            'redirect_url' => route('transfer.outbound'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        UpdateOutboundTransferTableEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('Notification pending domain transfer for ' . $domain . '.', 'Cron:'));
    }

    public function sendTransferCancelledNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Cancelled',
            'message' => 'The domain transfer for "' . strtoupper($domain) . '" has been cancelled.',
            'redirect_url' => route('transfer.inbound', ['transaction' => TransferRequest::CONFLICT]),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        UpdateOutboundTransferTableEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for ' . $domain . ' has been cancelled.', 'Cron:'));
    }

    public function sendTransferPollApprovedNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Approved',
            'message' => 'The domain transfer for "' . strtoupper($domain) . '" has been approved.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        UpdateInboundTransferTableEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for ' . $domain . ' has been approved.'));
    }

    public function sendTransferUserApprovedNotif(string $domain, string $user_id, string $email): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Approved',
            'message' => 'You have approved the transfer request for the domain "' . strtoupper($domain) . '".',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        UpdateOutboundTransferTableEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for ' . $domain . ' has been approved.', $email));
    }

    public function sendTransferServerApprovedNotif(string $domain, string $user_id, string $registered_domain_status): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Approved',
            'message' => 'The domain transfer for "' . strtoupper($domain) . '" has been automatically approved by the server.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        if ($registered_domain_status == UserDomainStatus::OWNED) UpdateOutboundTransferTableEvent::dispatch($user_id);
        else if ($registered_domain_status == UserDomainStatus::RESERVED) UpdateInboundTransferTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for ' . $domain . ' has been automatically approved by the server.', 'Cron:'));
    }

    public function sendTransferPollRejectedNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Rejected',
            'message' => 'The domain transfer for "' . strtoupper($domain) . '" has been rejected.',
            'redirect_url' => route('transfer.inbound', ['transaction' => TransferRequest::PREVIOUS]),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateInboundTransferTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('Poll notification domain transfer for ' . $domain . ' has been rejected.', 'Cron:'));
    }

    public function sendTransferUserRejectedNotif(string $domain, string $user_id, string $email): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Rejected',
            'message' => 'You have rejected the transfer request for the domain "' . strtoupper($domain) . '".',
            'redirect_url' => route('transfer.outbound', ['transaction' => TransferRequest::PREVIOUS]),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateOutboundTransferTableEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('Notification domain transfer for ' . $domain . ' has been rejected.', $email));
    }

    public function sendTransferConflictNotif(string $domain, string $user_id, string $email): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Conflict',
            'message' => 'Your transfer request for the domain "' . strtoupper($domain) . '" has failed. Click to view the error.',
            'redirect_url' => route('transfer.inbound', ['transaction' => TransferRequest::CONFLICT]),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateInboundTransferTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('unable to request a transfer for the domain ' . $domain . ' due to conflicts.', $email));
    }

    public function sendTransferAutoRefundNotif(string $domain, string $user_id, string $email, string $errorCode): void
    {
        $errorMessage = [
            EppErrorCodes::DOES_NOT_EXIST => 'does not exist',
            EppErrorCodes::PENDING_STATE_ERROR => 'is already in a pending state',
        ];

        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Transfer Auto-Refunded',
            'message' => 'The domain "' . strtoupper($domain) . '" ' . $errorMessage[$errorCode] . '. An auto-refund has been initiated.',
            'redirect_url' => route('transfer.inbound', ['transaction' => TransferRequest::PREVIOUS]),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateInboundTransferTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('auto-refund for ' . $domain . ' has been initiated.', $email));
    }

    public function sendBulkDomainActiveStatusNotif(array $domains): void
    {
        $data = [];

        foreach ($domains as $domain) {
            $data[] = [
                'user_id' => auth()->user()->id,
                'title' => 'Domain Registration Success',
                'message' => 'Congratulations! The registration for the domain "' . strtoupper($domain) . '" was successful and is now ACTIVE. Click to view your domains.',
                'redirect_url' => route('domain'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];
        }

        $this->sendNotification((array) $data);
        app(AuthLogger::class)->info($this->fromWho('Successfully created ' . implode(',', $domains) . ' domains and is now active.'));
    }

    public function sendDomainRenewalNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Renewal Success',
            'message' => 'The expiration of the domain "' . strtoupper($domain) . '" has been updated. Click to view your domains.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
        app(AuthLogger::class)->info($this->fromWho('updated the expiration of the domain ' . $domain . '.'));
    }

    public function sendBulkDomainRenewalNotif(array $domains): void
    {
        $data = [];

        foreach ($domains as $domain) {
            $data[] = [
                'user_id' => auth()->id(),
                'title' => 'Domain Renewal Success',
                'message' => 'The expiration of the domain "' . strtoupper($domain) . '" has been updated. Click to view your domains.',
                'redirect_url' => route('domain'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];
        }

        $this->sendNotification((array) $data);
        app(AuthLogger::class)->info($this->fromWho('updated the expiration of the domain ' . $domain . '.'));
    }

    public function sendDomainRenewalFailedNotif(string $domain, string $userId): void
    {
        $this->sendNotification([
            'user_id' => $userId,
            'title' => 'Domain Renewal Failed',
            'message' => 'Sorry, the renewal for the domain "' . strtoupper($domain) . '" was unsuccessful. A refund is in process. Click to view your domains.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($userId);
    }

    public function sendBulkDomainRenewalFailedNotif(array $domains): void
    {
        $data = [];

        foreach ($domains as $domain) {
            $data[] = [
                'user_id' => auth()->user()->id,
                'title' => 'Domain Renewal Failed',
                'message' => 'Sorry, the renewal for the domain "' . strtoupper($domain) . '" was unsuccessful. Click to view your domains.',
                'redirect_url' => route('domain'),
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];
        }

        $this->sendNotification((array) $data);
        app(AuthLogger::class)->info($this->fromWho('Failed to renew domains: ' . implode(',', $domains)));
    }

    public function sendDomainUpdateNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Update Success',
            'message' => 'The domain "' . strtoupper($domain) . '" has been updated. Click to view your domains.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
    }

    public function sendDomainUpdateFailedNotif(string $domain, string $user_id): void
    {
        $this->sendNotification([
            'user_id' => $user_id,
            'title' => 'Domain Update Failed',
            'message' => 'Sorry, the update for the domain "' . strtoupper($domain) . '" was unsuccessful. Click to view your domains.',
            'redirect_url' => route('domain'),
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ]);

        NotificationEvent::dispatch($user_id);
        UpdateDomainsTableEvent::dispatch($user_id);
    }
}
