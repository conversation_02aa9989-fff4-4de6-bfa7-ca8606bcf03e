<?php

namespace App\Modules\AccountCredit\Services;

use App\Modules\AccountCredit\Contracts\AccountCreditSourcePickerInterface;
use App\Modules\PaymentSummary\Contracts\PaymentSummaryPickerInterface;

class DepositAccountCreditService
{
    public static function instance(): self
    {
        $depositAccountCreditService = new self;

        return $depositAccountCreditService;
    }

    public function store(array $data, int $userId, string $paymentServiceType)
    {
        $accountMaker = app(AccountCreditSourcePickerInterface::class)->getType($paymentServiceType);
        $depositData = $accountMaker->store($data, $userId, $paymentServiceType);

        if (isset($depositData)) {
            $this->createPayment(
                $depositData['payload'],
                $userId,
                $depositData['summaryType'],
                $depositData['paymentServiceType'],
            );
        }
    }

    public function createPayment(array $payload, int $userId, string $summaryType, string $paymentServiceType)
    {
        $paymentProcessor = app(PaymentSummaryPickerInterface::class)->getType($summaryType);
        $createdPaymentService = $paymentProcessor->pay($payload, $userId, $paymentServiceType);
        $paymentObj = is_array($createdPaymentService) ? $createdPaymentService['payment_service'] : $createdPaymentService;
        $paymentProcessor->createPaymentSummary($payload, $paymentObj, $userId, $paymentServiceType);
        $paymentProcessor->sendInvoice($payload, $paymentObj, $userId, $paymentServiceType);
    }
}
