<?php

namespace App\Modules\Domain\Requests\Renewal;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\UpdateServices\RenewalDomainService;
use App\Rules\Domain\DomainOwnedArrayExists;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class RenewalPayRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'domains' => ['required', 'array', 'min:1', new DomainOwnedArrayExists],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, $validator->errors()->first(), 'Page not found');
    }

    public function getRenewalPaymentData(): array
    {
        return RenewalDomainService::instance()->getRenewalPaymentData($this->all());
    }
}
