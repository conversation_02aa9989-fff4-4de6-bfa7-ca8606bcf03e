<?php

namespace App\Modules\Domain\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DomainSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'domains' => ['required', 'array'],
        ];
    }

    public function messages(): array
    {
        return [
            'domains.required' => 'Please enter at least one domain name.',
        ];
    }

    public function prepareForValidation()
    {
        $rawDomains = $this->domains;

        if (empty($rawDomains)) {
            return;
        }

        $normalized = str_replace(["\r\n", "\n", "\r"], ' ', $rawDomains);
        $normalized = str_replace(',', ' ', $normalized);
        $domainArray = array_filter(array_map('trim', preg_split('/\s+/', $normalized)));
        $this->merge(['domains' => $domainArray]);
    }
}
