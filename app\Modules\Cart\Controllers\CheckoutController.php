<?php

namespace App\Modules\Cart\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Cart\Requests\CheckoutRequest;
use App\Modules\Cart\Services\CheckoutCartService;
use Inertia\Inertia;
use Inertia\Response;

class CheckoutController extends Controller
{
    public function index(): Response
    {
        $data = CheckoutCartService::instance()->getIndexData();
        return Inertia::render(
            'Domain/Checkout',
            $data
        );
    }

    public function store(CheckoutRequest $request): Response
    {
        $summaryId = $request->store();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment successful. Domain acquisition is in process.',
            'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
        ]);
    }

    public function storeAccountCredit(CheckoutRequest $request): Response
    {
        $summaryId = $request->store();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment successful. Domain acquisition is in process.',
            'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
        ]);
    }
}
