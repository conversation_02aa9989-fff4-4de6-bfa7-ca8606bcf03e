<?php

namespace App\Rules\TransferCart;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TransferCartArrayExists implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $exists = $this->transferCartIdExists($value);

        if (! $exists) {
            $fail($this->message());
        }
    }

    public function passes($attribute, $value)
    {
        return $this->transferCartIdExists($value);
    }

    public function message()
    {
        return 'Items are not valid.';
    }

    // PRIVATE FUNCTIONS

    private function transferCartIdExists(array $domains): bool
    {
        if (empty($domains)) {
            return false;
        }

        $transferCartObj = collect($domains);
        $ids = $transferCartObj->pluck('id')->toArray();

        $existingCount = DB::table('transfer_carts')
            ->where('user_id', Auth::user()->id)
            ->whereNull('deleted_at')
            ->whereIn('id', $ids)
            ->count();

        if ($existingCount !== count($ids)) {
            return false;
        }

        return true;
    }
}
