//* PACKAGES 
import React, { useState } from "react";

//* ICONS
//... 

//* COMPONENTS
import Modal from "@/Components/Modal";
import { IoMdClose } from "react-icons/io";
import Checkbox from "@/Components/Checkbox";

export default function DomainSelectedItemsModal({
    selectedItems,
    isModalOpen,
    closeModal
}) {

    const ShowSelectedItems = () => {
        return (
            <div className="flex flex-col gap-y-2 max-w-[60vw] max-h-[70vh] overflow-y-auto">
                {
                    selectedItems.map((item) => {
                        return <span key={item.id} className="text-sm font-medium p-3 border border-solid border-gray-300 rounded-md bg-gray-100">{item.name}</span>
                    })}
            </div>
        );
    };

    return (
        <>
            <Modal className={"flex flex-col justify-around gap-y-5 pt-5 pb-10 px-10"} show={isModalOpen} onClose={closeModal}>
                <div
                    className="flex justify-between w-full py-2 border-b border-solid border-gray-400"
                >
                    <span className="text-2xl font-normal">Selected Domains ({selectedItems.length})</span>
                    <button
                        type="button"
                        className="text-primary ease-in-out duration-100 hover:text-blue-900"
                        onClick={closeModal}
                    >
                        <IoMdClose
                            className="h-5 w-5 text-black"
                        />
                    </button>
                </div>

                <ShowSelectedItems />
            </Modal>
        </>

    );
}