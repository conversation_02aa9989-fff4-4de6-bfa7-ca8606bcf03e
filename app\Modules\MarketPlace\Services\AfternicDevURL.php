<?php

namespace App\Modules\MarketPlace\Services;

use App\Modules\MarketPlace\Constants\MarketConstants;

class AfternicDevURL implements AfternicInterface
{
    protected $key;

    protected $secretKey;

    public function __construct(string $key, string $secretKey)
    {
        $this->key = $key;
        $this->secretKey = $secretKey;
    }

    public function domain(): string
    {
        return MarketConstants::AFTERNIC_DEV;
    }

    public function getHeader(): array
    {
        return [
            'x-api-key' => $this->key,
            'x-api-secret' => $this->secretKey,
        ];
    }
}
