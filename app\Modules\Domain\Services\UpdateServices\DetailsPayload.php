<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainContact;
use App\Util\Helper\Domain\DomainContactsHelper;
use App\Util\Helper\Domain\DomainNameserversHelper;

class DetailsPayload
{
    use UserLoggerTrait;

    public object $newDomain;

    public object $oldDomain;

    public array $contacts;

    public array $contactRemove;

    public array $nameservers;

    public array $nameserverRemove;

    public string $name;

    public string $registrant;

    public bool $isUpdateContact;

    public bool $isUpdateNameserver;

    public function __construct(object $newDomain, array $oldDomain)
    {
        $this->newDomain = $newDomain;
        $this->oldDomain = json_decode(json_encode($oldDomain), false);

        $this->initialize();
    }

    public function initialize()
    {
        $this->name = $this->newDomain->name;
        $this->registrant = $this->newDomain->registrant;

        $this->contacts = [];
        $this->contactRemove = [];
        $this->nameservers = [];
        $this->nameserverRemove = [];
        $this->isUpdateContact = false;
        $this->isUpdateNameserver = false;

        return $this;
    }

    public function isContactUpdated()
    {
        $this->filterContacts();

        return $this->isUpdateContact;
    }

    public function isNameserverUpdated()
    {
        $this->filterNameservers();

        return $this->isUpdateNameserver;
    }

    public function check()
    {
        $this->filter();

        return $this->isUpdateContact || $this->isUpdateNameserver;
    }

    public function get()
    {
        $this->filter();
        $status = $this->getStatusPayload();

        $payload = [
            'name' => $this->name,
            'registrant' => $this->registrant,
            'regenerateAuthCode' => false,
            'contacts' => $this->contacts,
            'contactRemove' => $this->contactRemove,
            'nameservers' => $this->nameservers,
            'nameserverRemove' => $this->nameserverRemove,
            'status' => $status['status'],
            'statusRemove' => $status['statusRemove'],
        ];

        return $this->removeEmptyValues($payload);
    }

    public function getNameserverPayload()
    {
        return [
            'name' => $this->name,
            'registrant' => $this->registrant,
            'regenerateAuthCode' => false,
            'nameservers' => $this->nameservers,
            'nameserverRemove' => $this->nameserverRemove,
        ];
    }

    public function filterContacts()
    {
        $oldContacts = $this->oldDomain->contacts;
        $newContacts = $this->newDomain->contacts;
        $oldRegistrant = $this->oldDomain->registrant;

        $filteredData = DomainContactsHelper::filterContactUpdate((array) $oldContacts, $newContacts, $oldRegistrant);

        if (! $filteredData['is_update']) {
            return $this;
        }

        $this->registrant = $filteredData[DomainContact::REGISTRANT];
        $this->contactRemove = $this->decodeArray($filteredData['contactRemove']);
        $this->contacts = $this->decodeArray($filteredData['contacts']);
        $this->isUpdateContact = $filteredData['is_update'];

        return $this;
    }

    public function filterNameservers()
    {
        $oldNameservers = $this->oldDomain->nameservers ?? [];
        $newNameservers = $this->newDomain->nameservers ?? [];

        if (! $newNameservers) {
            return $this;
        }

        $filteredData = DomainNameserversHelper::filterNameserverUpdate($oldNameservers, $newNameservers);

        if (! $filteredData['is_update']) {
            return $this;
        }

        $this->nameservers = $this->decodeArray($filteredData['nameservers']);
        $this->nameserverRemove = $this->decodeArray($filteredData['nameserverRemove']);
        $this->isUpdateNameserver = $filteredData['is_update'];

        return $this;
    }

    public function getStatusPayload(): array
    {
        $domain = (array) $this->newDomain;

        if (! array_key_exists('status', $domain)) {
            return [
                'status' => [],
                'statusRemove' => [],
            ];
        }

        $payload = new RestrictionsPayload($this->oldDomain, $domain);

        return $payload->get();
    }

    private function removeEmptyValues(array $payload)
    {
        if (empty($this->contacts)) {
            unset($payload['contacts']);
        }

        if (empty($this->contactRemove)) {
            unset($payload['contactRemove']);
        }

        if (empty($this->nameservers)) {
            unset($payload['nameservers']);
        }

        if (empty($this->nameserverRemove)) {
            unset($payload['nameserverRemove']);
        }

        if (empty($payload['status'])) {
            unset($payload['status']);
        }

        if (empty($payload['statusRemove'])) {
            unset($payload['statusRemove']);
        }

        app(AuthLogger::class)->error(json_encode($payload));

        return $payload;
    }

    private function filter()
    {
        $this->filterContacts();
        $this->filterNameservers();
    }

    private function decodeArray(array $data)
    {
        if (empty($data)) {
            return json_decode(json_encode($data), true);
        }

        return json_decode(
            stripslashes(json_encode($data)),
            true,
            JSON_UNESCAPED_SLASHES
        );
    }
}
