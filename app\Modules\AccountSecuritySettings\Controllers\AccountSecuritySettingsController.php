<?php

namespace App\Modules\AccountSecuritySettings\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\AccountSecuritySettings\Services\AccountSecuritySettingsService;
use Inertia\Inertia;
use Inertia\Response;

class AccountSecuritySettingsController extends Controller
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Show Index Page
     */
    public function index(): Response
    {
        return Inertia::render(
            'AccountSecurity/AccountSecurityIndex',
            (new AccountSecuritySettingsService)->processIndexData()
        );
    }
}
