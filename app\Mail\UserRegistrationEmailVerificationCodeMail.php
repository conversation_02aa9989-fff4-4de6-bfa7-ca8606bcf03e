<?php

namespace App\Mail;

use App\Util\Constant\QueueConnection;
use App\Mail\Constants\OtpMailConstant;

use Illuminate\Mail\Mailables\Address;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class UserRegistrationEmailVerificationCodeMail extends Mailable  implements ShouldQueue, ShouldBeUnique
{
    use Queueable, SerializesModels;

    /**
     * Data Information for email
     * 
     * @var array 
     */
    private array $data;

    /**
     * The number of seconds after which the job's unique lock will be released.
     *
     * @var int
     */
    public $uniqueFor = 300;

    /**
     * Create a new message instance.
     */
    public function __construct(
        array $data = []
    ) {
        $this->onConnection(QueueConnection::OTP_MAIL_JOBS);
        $this->onQueue(OtpMailConstant::EMAIL);

        $this->data = $data;
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return $this->data['email'];
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: 'Registration Email Verification',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.RegistrationEmailVerificationMail',
            with: [
                'data'   => $this->data,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
