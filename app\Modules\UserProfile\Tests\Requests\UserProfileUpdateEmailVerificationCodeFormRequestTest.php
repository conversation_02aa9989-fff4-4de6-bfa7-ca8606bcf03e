<?php

namespace App\Modules\UserProfile\Tests\Requests;

use App\Modules\UserProfile\Requests\UserProfileUpdateEmailVerificationCodeFormRequest;
use App\Modules\UserProfile\Services\UserProfileService;
use App\Modules\UserProfile\Tests\Datasets\CommonValidationDataset;
use Illuminate\Support\Facades\Validator;
use Mockery;

// it('fails validation when required fields are missing', function () {
//     $validator = Validator::make([], (new UserProfileUpdateEmailVerificationCodeFormRequest)->rules());

//     expect($validator->fails())->toBeTrue();
//     expect($validator->errors()->has('code'))->toBeTrue();
// });

// it('fails validation with invalid code format', function () {
//     $validator = Validator::make([
//         'code' => 'not-a-code',
//     ], (new UserProfileUpdateEmailVerificationCodeFormRequest)->rules());

//     expect($validator->fails())->toBeTrue();
//     expect($validator->errors()->has('code'))->toBeTrue();
// });

// it('passes validation with valid code', function () {
//     $mock = Mockery::mock(UserProfileService::class);
//     $mock->shouldReceive('verifyEmailCode')
//         ->once()
//         ->with(['code' => '123456'], $this->user->id)
//         ->andReturn(['success' => true, 'message' => 'Verified.']);

//     app()->instance(UserProfileService::class, $mock);

//     $validator = Validator::make([
//         'code' => '123456',
//     ], (new UserProfileUpdateEmailVerificationCodeFormRequest)->rules());

//     expect($validator->fails())->toBeFalse();
// });

// it('fails validation with incorrect code', function () {
//     $mock = Mockery::mock(UserProfileService::class);
//     $mock->shouldReceive('verifyEmailCode')
//         ->once()
//         ->with(['code' => '123456'], $this->user->id)
//         ->andReturn(['success' => false, 'message' => 'The Code is incorrect.']);

//     app()->instance(UserProfileService::class, $mock);

//     $validator = Validator::make([
//         'code' => '123456',
//     ], (new UserProfileUpdateEmailVerificationCodeFormRequest)->rules());

//     expect($validator->fails())->toBeTrue();
//     expect($validator->errors()->has('code'))->toBeTrue();
// });