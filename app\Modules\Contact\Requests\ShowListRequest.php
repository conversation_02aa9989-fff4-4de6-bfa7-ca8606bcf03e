<?php

namespace App\Modules\Contact\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\Contact\Services\ContactIndexQueryService;
use App\Util\Constant\Registry;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Symfony\Component\CssSelector\Node\FunctionNode;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'contact' => strtolower($this->contact)
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => [
                'string',
                Rule::in([
                    'name:asc',
                    'name:desc',
                    'created:asc',
                    'created:desc',
                ])
            ],
            'status' => ['string', Rule::in(ContactStatus::ALL)],
            'registry' => ['string', Rule::in(Registry::ALL)],
            'contact' => ['string', 'min:1', 'max:100'],
            'email' => ['string', 'email:rfc,dns', 'max:100'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    public function getData()
    {
        return ContactIndexQueryService::instance()->getData($this);
    }
}
