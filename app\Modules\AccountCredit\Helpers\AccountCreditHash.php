<?php

namespace App\Modules\AccountCredit\Helpers;

use Illuminate\Support\Arr;

class AccountCreditHash
{
    public static function generate(array $data)
    {
        $flattenData = Arr::flatten($data);
        $stringData = Arr::join($flattenData, '');

        return hash('sha256', $stringData);
    }

    public static function validate(object $block)
    {
        $testBlock = [
            'user_id' => $block->user_id,
            'block_index' => $block->block_index,
            'type' => $block->type,
            'running_balance' => $block->running_balance,
            'amount' => $block->amount,
            'timestamp' => $block->timestamp,
            'previous_hash' => $block->previous_hash,
        ];

        $computedHash = self::generate($testBlock);

        return $block->hash === $computedHash;
    }

    // PRIVATE
}
