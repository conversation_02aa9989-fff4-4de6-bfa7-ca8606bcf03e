<?php

namespace App\Modules\JobRetry\Services;

use App\Models\JobRetryLog;
use App\Modules\JobRetry\Constants\RetryJobStatus;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class RetryJobService
{
    private $retryMin = 5;

    public static function instance(): self
    {
        $retryJobService = new self;

        return $retryJobService;
    }

    public function store(array $data): void
    {
        $item = $this->getItem($data);

        if ($item) {
            $this->checkMaxAttempt($item, $data);

            return;
        }

        if (! in_array($data['status'], RetryJobStatus::STOP_RETRY_STATUS)) {
            $this->addEntry($data);
        }
    }

    public function checkLogsByIdAndType(int $id, string $type): bool
    {
        $query = DB::table('job_retry_logs')
            ->where('job_id', $id)
            ->where('type', $type)
            ->get()->first();

        if ($query) {
            return true;
        }

        return false;
    }

    public function getRetryJobs(int $size = 5): object
    {
        // get domains
        $list = DB::table('job_retry_logs')
            ->whereIn('status', RetryJobStatus::GET_RETRY_STATUS)
            ->where('retry_at', '<', now()->timestamp)
            ->whereNull('deleted_at')
            ->orderBy('retry_at', 'asc')
            ->limit($size)
            ->get();

        if ($list->isEmpty()) {
            return $list;
        }

        // update to in process ?
        $list_ids = $list->pluck('id')->toArray();
        DB::table('job_retry_logs')
            ->whereIn('id', $list_ids)
            ->update(['status' => RetryJobStatus::IN_PROCESS]);

        return $list;
    }

    // PRIVATE FUNCTIONS

    private function getItem(array $data)
    {
        $job_id = $data['job_id'];
        $type = $data['type'];

        return JobRetryLog::where('job_id', $job_id)
            ->where('type', $type)
            ->whereNotIn('status', RetryJobStatus::STOP_RETRY_STATUS)
            ->get()->first();
    }

    private function addEntry(array $data): void
    {
        // if failed, add to db, add retry_at  by (5 min * attempt) + 5
        $attempt = 0;
        $minutes = ($this->retryMin * $attempt) + $this->retryMin;
        $retry_at = Carbon::now()->addMinutes($minutes)->timestamp;

        $payload = [
            'job_id' => $data['job_id'],
            'type' => $data['type'],
            'status' => $data['status'],
            'retry_at' => $retry_at,
            'attempts' => $attempt,
            'record' => json_encode($data['record']),
        ];

        JobRetryLog::create($payload);
    }

    private function updateEntry(object $item, array $data): void
    {
        // if failed, add to db, add retry_at  by (5 min * attempt) + 5
        $attempt = $item->attempts + 1;
        $minutes = ($this->retryMin * $attempt) + $this->retryMin;
        $retry_at = Carbon::now()->addMinutes($minutes)->timestamp;
        $status = $data['status'];

        $payload = [
            'status' => $status,
            'retry_at' => $retry_at,
            'attempts' => $attempt,
            'record' => json_encode($data['record']),
        ];

        JobRetryLog::where('id', $item->id)->update($payload);

        $this->deleteEntry($item, $status);
    }

    private function deleteEntry(object $item, string $status): void
    {
        if (in_array($status, RetryJobStatus::STOP_RETRY_STATUS)) {
            JobRetryLog::where('id', $item->id)->delete();
        }
    }

    private function checkMaxAttempt(object $item, array $data): void
    {
        $attempt = $item->attempts + 1;
        $type = $item->type;
        $maxAttempt = Config::get('job_retry.max_attempt.'.$type);
        if ($attempt < $maxAttempt) {
            $this->updateEntry($item, $data);

            return;
        }

        $this->setMaxAttempt($item, $maxAttempt);
    }

    private function setMaxAttempt(object $item, int $maxAttempt): void
    {
        $payload = [
            'status' => RetryJobStatus::MAX_ATTEMPT,
        ];

        JobRetryLog::where('id', $item->id)->update($payload);
        JobRetryLog::where('id', $item->id)->delete();

        throw new Exception(RetryJobStatus::MAX_ATTEMPT.': '.$maxAttempt);
    }
}
