import React, { useEffect, useState } from "react";
import UserLayout from "@/Layouts/UserLayout";
import RedemptionContainer from "../../Components/Domain/Redemption/RedemptionContainer";
import { useForm, usePage, router} from "@inertiajs/react";


export default function Redemption({ domains, settings }) {
    const user = usePage().props.auth.user;
    const { setError } = useForm({
        user_id: user.id,
    });

    useEffect(
        () => {
            window.history.pushState(null, '', window.location.href);
            window.onpopstate = () => {
                window.history.replaceState(null, '', router.get('domain'));
                window.location.reload();
            };
        },
        []
    );

    return (
        <UserLayout hideNav={true} postRouteName={'domain.redeem.confirm'}>
            <RedemptionContainer
                data={domains}
                settings={settings}
            />
        </UserLayout>
    );
}
