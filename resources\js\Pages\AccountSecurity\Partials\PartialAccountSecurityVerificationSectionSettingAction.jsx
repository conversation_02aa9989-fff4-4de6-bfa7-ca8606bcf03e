//* PACKAGES 
import React from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from 'react-toastify';
import { debounce } from 'lodash';

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import AppToggleSwitchComponent from '@/Components/App/AppToggleSwitchComponent';
import VerificationPromptSettingAuthenticatorAppComponent from '@/Components/VerificationPromptSetting/VerificationPromptSettingAuthenticatorAppComponent';
import VerificationPromptSettingEmailComponent from '@/Components/VerificationPromptSetting/VerificationPromptSettingEmailComponent';
import VerificationPromptSettingPasswordComponent from '@/Components/VerificationPromptSetting/VerificationPromptSettingPasswordComponent';
import VerificationPromptSettingPinCodeComponent from '@/Components/VerificationPromptSetting/VerificationPromptSettingPinCodeComponent';

//* STATE
//...

//* UTILS
import UtilCheckIfHasSecuredTransaction from '@/Util/UtilCheckIfHasSecuredTransaction';

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function PartialAccountSecurityVerificationSectionSettingAction(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES 
    //... 

    //! VARIABLES 
    const debounceTimer = 100;

    const enabledOptions =
    {
        domainSecure: UtilCheckIfHasSecuredTransaction('domainSecure'),
        domainAuthCode: UtilCheckIfHasSecuredTransaction('domainAuthCode'),
        domainNameserver: UtilCheckIfHasSecuredTransaction('domainNameserver'),
        domainRenewal: UtilCheckIfHasSecuredTransaction('domainRenewal'),
        domainTransfer: UtilCheckIfHasSecuredTransaction('domainTransfer'),
        domainPush: UtilCheckIfHasSecuredTransaction('domainPush'),
        domainDelete: UtilCheckIfHasSecuredTransaction('domainDelete'),
        purchases: UtilCheckIfHasSecuredTransaction('purchases')
    };

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    //...

    //! SUB COMPONENTS 
    function subComponentPreferredVerification() {
        const promptItems =
            [
                {
                    component: <VerificationPromptSettingAuthenticatorAppComponent />,
                    isAvailable: user.enable_authenticator_app == true
                },
                {
                    component: <VerificationPromptSettingEmailComponent />,
                    isAvailable: user.enable_email_otp == true
                },
                {
                    component: <VerificationPromptSettingPasswordComponent />,
                    isAvailable: true
                },
                {
                    component: <VerificationPromptSettingPinCodeComponent />,
                    isAvailable: user.hasPinCode == true
                },
            ];

        return (
            <div
                className='flex flex-col gap-5'
            >
                <div
                    className=''
                >
                    Preferred OTP
                </div>
                <div
                    className='flex flex-col gap-5 pl-5'
                >
                    {
                        promptItems.filter(item => item.isAvailable == true).map(
                            (item, index) => {
                                return (
                                    <div
                                        key={index}
                                    >
                                        {item.component}
                                    </div>
                                );
                            }
                        )
                    }
                </div>
            </div>
        );
    }

    function subComponentTransactions() {
        const toggleAll = debounce(
            (shouldEnable) => {
                router.post(
                    route('user-verification-secure-transactions.toggle-all'),
                    {
                        shouldEnable: shouldEnable
                    },
                    {
                        preserveState: true,
                        preserveScroll: true,
                        onSuccess: () => {
                            if (shouldEnable) {
                                toast.success("Options enabled.");
                            }
                            else {
                                toast.success("Options disabled.");
                            }
                        },
                        onError: () => toast.error("Something went wrong!")
                    }
                );
            },
            debounceTimer
        );

        const toggleOption = debounce(
            (value, shouldEnable) => {
                router.post(
                    route('user-verification-secure-transactions.toggle'),
                    {
                        value: value,
                        shouldEnable: shouldEnable
                    },
                    {
                        preserveState: true,
                        preserveScroll: true,
                        onSuccess: () => {
                            if (shouldEnable) {
                                toast.success("Option enabled.");
                            }
                            else {
                                toast.success("Option disabled.");
                            }
                        },
                        onError: () => toast.error("Something went wrong!")
                    }
                );
            },
            debounceTimer
        );

        const options =
            [
                {
                    label: 'Domain Locking & Unlocking',
                    value: 'domainSecure',
                    isEnabled: enabledOptions.domainSecure == true,
                },
                {
                    label: 'Domain Request Auth Code',
                    value: 'domainAuthCode',
                    isEnabled: enabledOptions.domainAuthCode == true,
                },
                {
                    label: 'Domain Renewal',
                    value: 'domainRenewal',
                    isEnabled: enabledOptions.domainRenewal == true,
                },
                {
                    label: 'Domain Nameserver Update',
                    value: 'domainNameserver',
                    isEnabled: enabledOptions.domainNameserver == true,
                },
                {
                    label: 'Domain Push',
                    value: 'domainPush',
                    isEnabled: enabledOptions.domainPush == true,
                },
                {
                    label: 'Domain Transfer',
                    value: 'domainTransfer',
                    isEnabled: enabledOptions.domainTransfer == true,
                },
                {
                    label: 'Domain Delete',
                    value: 'domainDelete',
                    isEnabled: enabledOptions.domainDelete == true,
                },
                {
                    label: 'Purchases',
                    value: 'purchases',
                    isEnabled: enabledOptions.purchases == true,
                },
            ];

        return (
            <div
                className='flex flex-col gap-5'
            >
                <div
                    className='flex justify-between items-center'
                >
                    <div
                        className=''
                    >
                        Secure transaction
                    </div>
                    <AppToggleSwitchComponent
                        isChecked={Object.values(enabledOptions).filter(value => value === true).length === Object.values(enabledOptions).length}
                        onChangeEvent={
                            () => {
                                let shouldEnable = true;

                                if (Object.values(enabledOptions).filter(value => value === true).length === Object.values(enabledOptions).length) {
                                    shouldEnable = false;
                                }

                                toggleAll(shouldEnable);
                            }
                        }
                    />
                </div>
                <div
                    className='flex flex-col gap-6 pl-5'
                >
                    {
                        options.map(
                            (item, index) => {
                                return (
                                    <div
                                        key={index}
                                        className="flex justify-between items-center"
                                    >
                                        <div
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </div>
                                        <AppToggleSwitchComponent
                                            isChecked={item.isEnabled == true}
                                            onChangeEvent={
                                                () => {
                                                    toggleOption(item.value, !item.isEnabled)
                                                }
                                            }
                                        />
                                    </div>
                                );
                            }
                        )
                    }
                </div>
            </div>
        );
    }

    return (
        <div
            className='flex flex-col gap-5 justify-between'
        >
            <div
                className='flex flex-col gap-y-4 '
            >
                <div
                    className='text-lg font-bold'
                >
                    Required OTP Transaction on Sensitive Actions
                </div>
            </div>

            <div
                className='flex flex-col gap-y-8 pl-5'
            >
                {subComponentPreferredVerification()}

                {/* <hr /> */}

                {subComponentTransactions()}
            </div>
        </div>
    );
}
