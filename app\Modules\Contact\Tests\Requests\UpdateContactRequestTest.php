<?php

require_once 'app\Modules\Contact\Tests\Helper.php';

use Illuminate\Support\Facades\Validator;
use App\Modules\Contact\Requests\UpdateContactRequest;
use App\Modules\Contact\Tests\Datasets\ContactValidationDataset;

it('passes validation with valid data', function () {
    $data = ContactValidationDataset::validUpdateContactData();
    $request = new UpdateContactRequest;

    $request->registry = $data['registry'];

    $validator = Validator::make($data, $request->rules());

    expect($validator->passes())->toBeTrue(json_encode($validator->errors()));
});


it('fails validation when contact name already exist', function () {
    $request = new UpdateContactRequest;
    $contact_name = "qwe";
    $registry = createTestRegistry('verisign');
    $contact = createTestContact(['name' => $contact_name]);
    $userContact = createTestUserContact($this->user->id, $contact->id, $registry->id, []);

    $data = [
        'name' => $contact_name,
        'registry' => $registry->name
    ];
    $request->registry = $data['registry'];

    $validator = Validator::make($data, $request->rules());

    expect($validator->errors()->has('name'))->toBeTrue("Failed to validate name field");
    expect($validator->fails())->toBeTrue();
});

it('fails validation when required fields are missing', function () {
    $request = new UpdateContactRequest;

    $request->registry = 'invalid';

    $required_fields = [
        'registry',
        'name',
        'organization_name',
        'registry_contact',
        'voice_number',
        'email',
        'street',
        'city',
        'state_province',
        'postal_code',
        'country_code',
        'ext_voice_number'
    ];

    $validator = Validator::make([], $request->rules());

    foreach ($required_fields as $field) {
        expect($validator->errors()->has($field))->toBeTrue("Failed to validate required " . $field . " field");
    }
    expect($validator->fails())->toBeTrue();
});


testInvalidContactData(new UpdateContactRequest(), ContactValidationDataset::invalidUpdateContactData());

