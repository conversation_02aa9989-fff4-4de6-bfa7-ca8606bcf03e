<?php

namespace App\Modules\TransactionThreshold\Constants;

use App\Util\Constant\Transaction;

final class TransactionThresholdRoutes
{
    public final const ROUTES = [
        Transaction::REGISTER => [
            'notif' => 'client',
            'exception' => 'domain.mycart',
            'label' => 'Go to My Cart',
        ],
        Transaction::TRANSFER => [
            'notif' => 'client',
            'exception' => 'transfer.inbound.mycart',
            'label' => 'Go to My Transfer Cart',
        ],
        Transaction::RENEW => [
            'notif' => 'client',
            'exception' => 'domain',
            'label' => 'Go to My Domain',
        ],
        Transaction::DELETE => [
            'notif' => 'client',
            'exception' => 'domain.mycart',
            'label' => 'Go to My Cart',
        ],
        Transaction::REDEEM => [
            'notif' => 'client',
            'exception' => 'domain.mycart',
            'label' => 'Go to My Cart',
        ],
        Transaction::AUTH_REQUEST => [
            'notif' => 'client',
            'exception' => 'domain.mycart',
            'label' => 'Go to My Cart',
        ],
        Transaction::DOMAIN_SEARCH => [
            'notif' => 'client',
            'exception' => 'domain.mycart',
            'label' => 'Go to My Cart',
        ],
        Transaction::PUSH => [
            'notif' => 'client',
            'exception' => 'domain.mycart',
            'label' => 'Go to My Cart',
        ]
    ];
}
