<?php

namespace App\Modules\Client\Jobs;

use App\Events\SystemLogEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Histories\Constants\SystemTransactionType;
use App\Modules\Push\Services\PushDomainService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ClientAccountMaintenance implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $userId;

    private $email;

    private $push_type;

    public $failOnTimeout = true;

    public $uniqueFor = 3600;

    public function __construct(string $userId, string $email)
    {
        $this->userId = $userId;
        $this->email = $email;

        $this->onConnection(QueueConnection::UPDATE_ON_LOGIN);
        $this->onQueue(QueueTypes::PUSH_REQUEST_EXPIRED);
    }

    public function uniqueId(): string
    {
        return Carbon::now()->timestamp.$this->userId;
    }

    public function handle(): void
    {
        try {
            PushDomainService::instance()->handleExpiredPushDomains($this->userId, $this->email);

            event(new SystemLogEvent(
                SystemTransactionType::DOMAIN_UPDATE,
                "Processed expired push domain requests for user {$this->email}",
                null
            ));
        } catch (Exception $e) {
            app(AuthLogger::class)->error('ClientAccountMaintenance: '.$e->getMessage());
            $this->fail();
        }
    }
}
