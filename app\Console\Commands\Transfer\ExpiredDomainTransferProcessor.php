<?php

namespace App\Console\Commands\Transfer;

use Exception;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Jobs\CancelDomainTransfer;
use App\Modules\Transfer\Services\TransferRefundService;
use Illuminate\Console\Command;

class ExpiredDomainTransferProcessor extends Command
{
    private $gracePeriod = 30; // 30 days

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transfer:expired-domain-transfer-processor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refund transfer requests that have been expired and invalid for 30 days.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'ExpiredDomainTransferProcessor: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate(): void
    {
        app(AuthLogger::class)->info('ExpiredDomainTransferProcessor: Running...');
        app(AuthLogger::class)->info('ExpiredDomainTransferProcessor: Checking for expired transfer requests...');
        $expiredRequests = TransferRefundService::instance()->getExpiredRequests($this->gracePeriod);

        if (empty($expiredRequests)) {
            app(AuthLogger::class)->info('ExpiredDomainTransferProcessor: Terminating, nothing to process...');
            return;
        }

        app(AuthLogger::class)->info('ExpiredDomainTransferProcessor: Processing refunds...');
        foreach ($expiredRequests as $request) {
            CancelDomainTransfer::dispatch(TransferRequest::SYSTEM_CANCELLED, $request->id, $request->user_id, $request->email);
        }

        app(AuthLogger::class)->info('ExpiredDomainTransferProcessor: Done');
    }
}
