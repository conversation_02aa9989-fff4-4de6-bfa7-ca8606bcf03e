<?php

namespace App\Rules\MarketPlace;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MarketCartArrayExists implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $exists = $this->cartIdExists($value);

        if (! $exists) {
            $fail($this->message());
        }
    }

    public function passes($attribute, $value)
    {
        return $this->cartIdExists($value);
    }

    public function message()
    {
        return 'Items are not valid.';
    }

    // PRIVATE FUNCTIONS

    private function cartIdExists(array $marketCarts): bool
    {
        if (empty($marketCarts)) {
            return false;
        }

        $cartObj = collect($marketCarts);
        $ids = $cartObj->pluck('id')->toArray();

        $existingCount = DB::table('market_carts')
            ->where('user_id', Auth::user()->id)
            ->whereIn('id', $ids)
            ->count();

        if ($existingCount !== count($ids)) {
            return false;
        }

        return true;
    }
}
