<?php

namespace App\Modules\Domain\Requests;

use App\Modules\Domain\Services\UpdateServices\UpdateDomainService;
use Illuminate\Foundation\Http\FormRequest;

class UpdateEppDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [

            'id' => ['required', 'integer'],
            'name' => ['required', 'string'],
            'registered_domain_id' => ['required', 'integer'],
            'registrant_id' => ['required', 'integer'],
            'registrant' => ['required', 'string'],
            'admin_contact_id' => ['required', 'integer'],
            'admin_contact' => ['required', 'string'],
            'technical_contact_id' => ['required', 'integer'],
            'technical_contact' => ['required', 'string'],
            'extension' => ['required', 'string'],
            'year_length' => ['required', 'integer'],
            'user_category_id' => ['required', 'integer'],
            'registry' => ['required', 'string'],
            'billing_contact_id' => ['required', 'integer'],
            'billing_contact' => ['required', 'string'],
            'locked_until' => ['required', 'int'],
            'nameservers' => ['array'],
            'is_opt_in' => ['required', 'boolean'],
        ];
    }

    public function update(): void
    {
        UpdateDomainService::instance()->updateDomain($this->all());
    }
}
