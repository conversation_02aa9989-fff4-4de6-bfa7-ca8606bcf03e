<?php

namespace App\Modules\PaymentService\Contracts;

use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentService\Services\Interfaces\AccountCreditPaymentService;
use App\Modules\PaymentService\Services\Interfaces\AccountDepositPaymentService;
use App\Modules\PaymentService\Services\Interfaces\BankTransferPaymentService;
use App\Modules\PaymentService\Services\Interfaces\StripePaymentService;
use App\Modules\PaymentService\Services\Interfaces\SystemCreditPaymentService;
use Exception;

class PaymentServicePicker implements PaymentServicePickerInterface
{
    public function getType(string $type)
    {
        switch ($type) {
            case PaymentServiceType::STRIPE:
                return new StripePaymentService;
            case PaymentServiceType::BANK_TRANSFER:
                return new BankTransferPaymentService;
            case PaymentServiceType::SYSTEM_CREDIT:
                return new SystemCreditPaymentService;
            case PaymentServiceType::ACCOUNT_CREDIT: // checkout - pay with account credit
                return new AccountCreditPaymentService;
            case PaymentServiceType::ACCOUNT_DEPOSIT: // add to account balance
                return new AccountDepositPaymentService;
            default:
                throw new Exception('Payment not supported.');
        }
    }
}
