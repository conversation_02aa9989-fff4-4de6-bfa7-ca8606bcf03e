<?php

namespace App\Modules\Notification\Services;

use App\Events\NotificationEvent;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class NotificationService
{
    use CursorPaginate;
    use UserLoggerTrait;

    private $pageLimit = 20;

    private $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance(): self
    {
        $notificationService = new self;

        return $notificationService;
    }

    public function getAll(): array
    {
        $limit = request()->all();
        $limit = empty($limit['limit']) ? $this->pageLimit : $limit['limit'];
        $userId = auth()->id();
        $type = request()->get('type', 'all');

        $notificationsQuery = DB::table('notifications')
            ->select([
                'id',
                'user_id',
                'title',
                'message',
                'read_at',
                'created_at',
                'updated_at',
                'redirect_url',
                'importance',
                DB::raw("'domain' as type"),
            ])
            ->where('user_id', $userId);

        $scheduleNotificationsQuery = DB::table('schedule_notifications')
            ->select([
                'id',
                'user_id',
                'title',
                'message',
                'read_at',
                'created_at',
                'updated_at',
                'redirect_url',
                DB::raw('NULL as importance'),
                DB::raw("'announcement' as type"),
            ])
            ->where('user_id', $userId)
            ->where('status', 'active');

        $query = match ($type) {
            'domain' => $notificationsQuery,
            'announcement' => $scheduleNotificationsQuery,
            default => $notificationsQuery->union($scheduleNotificationsQuery)
        };

        $result = $query->orderBy('created_at', 'desc')
            ->paginate($limit)
            ->onEachSide(1)
            ->withQueryString();

        return CursorPaginate::cursor($result);
    }

    public function get_dropdown(string $userId): Collection
    {
        $type = request()->get('type', 'domain');
        $page = request()->get('page', 1);
        $perPage = 20;

        if ($type === 'domain') {
            return DB::table('notifications')
                ->select([
                    'id',
                    'user_id',
                    'title',
                    'message',
                    'read_at',
                    'created_at',
                    'updated_at',
                    'redirect_url',
                    'importance',
                    DB::raw("'domain' as type"),
                ])
                ->where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();
        } else {
            return DB::table('schedule_notifications')
                ->where('user_id', $userId)
                ->where('status', 'active')
                ->orderBy('created_at', 'desc')
                ->take(20)
                ->get();
        }
    }

    public function showNotificationItem(string $notifId, string $type = 'domain'): string
    {
        $this->setNotifItemAsRead($notifId, $type);

        return $this->getNotifItemUrl($notifId, $type);
    }

    public function getUnreadCount(string $userId): int
    {

        $regularCount = DB::table('notifications')
            ->where('user_id', $userId)
            ->whereNull('read_at')
            ->count();

        $scheduleCount = DB::table('schedule_notifications')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->whereNull('read_at')
            ->count();

        return $regularCount + $scheduleCount;
    }

    public function markAllAsRead($userId): array
    {
        $now = Carbon::now();

        $unreadNotificationIds = DB::table('notifications')
            ->where('user_id', $userId)
            ->whereNull('read_at')
            ->pluck('id')
            ->toArray();

        DB::table('notifications')
            ->where('user_id', $userId)
            ->whereNull('read_at')
            ->update([
                'read_at' => $now,
            ]);

        $unreadCount = DB::table('notifications')
            ->where('user_id', $userId)
            ->whereNull('read_at')
            ->count();

        broadcast(new NotificationEvent(
            $userId,
            'read-all',
            json_encode($unreadNotificationIds),
            'domain'
        ));

        return [
            'success' => true,
            'unreadCount' => $unreadCount,
            'updatedIds' => $unreadNotificationIds,
        ];
    }

    public function markAllByType($userId, string $type = 'all'): array
    {
        if ($type === 'all') {
            $domainResult = $this->markAllAsRead($userId);
            $announcementResult = $this->markAllAnnouncementsAsRead($userId);
            
            return [
                'success' => true,
                'result' => [
                    'domain' => $domainResult,
                    'announcement' => $announcementResult
                ]
            ];
        } else if ($type === 'domain') {
            $result = $this->markAllAsRead($userId);
            
            return [
                'success' => true,
                'result' => $result
            ];
        } else if ($type === 'announcement') {
            $result = $this->markAllAnnouncementsAsRead($userId);
            
            return [
                'success' => true,
                'result' => $result
            ];
        }

        return [
            'success' => false,
            'message' => 'Invalid notification type'
        ];
    }

    public function markSelectedAsRead(array $ids): array
    {
        $now = Carbon::now();
        $userId = auth()->id();

        DB::table('notifications')
            ->whereIn('id', $ids)
            ->whereNull('read_at')
            ->update([
                'read_at' => $now,
            ]);

        $unreadCount = DB::table('notifications')
            ->where('user_id', $userId)
            ->whereNull('read_at')
            ->count();

        broadcast(new NotificationEvent(
            $userId,
            'read-all',
            json_encode($ids),
            'domain'
        ));

        return [
            'success' => true,
            'unreadCount' => $unreadCount,
            'updatedIds' => $ids,
        ];
    }

    public function markAllAnnouncementsAsRead(string $userId): array
    {
        $now = Carbon::now();

        $unreadAnnouncementIds = DB::table('schedule_notifications')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->whereNull('read_at')
            ->pluck('id')
            ->toArray();

        DB::table('schedule_notifications')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->whereNull('read_at')
            ->update([
                'read_at' => $now,
            ]);

        $unreadCount = DB::table('schedule_notifications')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->whereNull('read_at')
            ->count();

        broadcast(new NotificationEvent(
            $userId,
            'read-all',
            json_encode($unreadAnnouncementIds),
            'announcement'
        ));

        return [
            'success' => true,
            'unreadCount' => $unreadCount,
            'updatedIds' => $unreadAnnouncementIds,
        ];
    }

    public function getNotifById(string $id): Builder
    {
        return DB::table('notifications')->where('id', $id);
    }

    // PRIVATE Functions

    private function setNotifItemAsRead(string $notifId, string $type = 'domain'): void
    {
        $table = $type === 'domain' ? 'notifications' : 'schedule_notifications';

        DB::table($table)
            ->where('id', $notifId)
            ->whereNull('read_at')
            ->update([
                'read_at' => $this->now,
            ]);

        broadcast(new NotificationEvent(
            auth()->id(),
            'read',
            $notifId,
            $type
        ));
    }

    private function getNotifItemUrl(string $notifId, string $type = 'domain'): string
    {
        $table = $type === 'domain' ? 'notifications' : 'schedule_notifications';

        return DB::table($table)
            ->where('id', $notifId)
            ->value('redirect_url');
    }
}
