<?php

namespace App\Modules\AdminBackdoor\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\AdminBackdoor\Requests\FailedQueueForm;
use App\Modules\AdminBackdoor\Services\FailedQueue;
use App\Modules\CustomLogger\Services\AuthLogger;

class FailedQueueCommandController extends Controller
{
    public function retry(FailedQueueForm $request)
    {
        app(AuthLogger::class)->info('FailedQueueCommand: retry...');
        $request->retryQueue();

        return response()->json(['status' => 'ok'], 200);
    }

    public function forget(FailedQueueForm $request)
    {
        app(AuthLogger::class)->info('FailedQueueCommand: forget...');
        $request->forgetQueue();

        return response()->json(['status' => 'ok'], 200);
    }

    public function flush()
    {
        app(AuthLogger::class)->info('FailedQueueCommand: flush...');
        FailedQueue::flush();

        return response()->json(['status' => 'ok'], 200);
    }
}
