<?php

namespace App\Modules\Domain\Constants;

use App\Modules\Contact\Constants\ContactType;

final class DomainContact
{
    public const TECH = 'tech';

    public const ADMIN = 'admin';

    public const REGISTRANT = 'registrant';

    public const BILLING = 'billing';

    public const DB_TECH = 'technical_contact';

    public const DB_ADMIN = 'administrative_contact';

    public const DB_REGISTRANT = 'registrant';

    public const DB_BILLING = 'billing_contact';

    public const CONTACTS = [
        self::TECH,
        self::ADMIN,
        self::REGISTRANT,
        self::BILLING,
    ];

    public const DB_CONTACTS = [
        self::DB_TECH,
        self::DB_ADMIN,
        self::DB_REGISTRANT,
        self::DB_BILLING,
    ];

    public const DEFAULT_CONTACTS = [
        self::REGISTRANT => ContactType::DEFAULT_REGISTRAR,
        self::ADMIN => ContactType::DEFAULT_ADMINISTRATIVE,
        self::TECH => ContactType::DEFAULT_TECHNICAL,
        self::BILLING => ContactType::DEFAULT_BILLING,
    ];
}
