<?php

namespace App\Modules\MarketPlace\Jobs;

use App\Events\DomainHistoryEvent;
use App\Models\MarketPlaceDomains;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Services\AfternicMiddleware;
use App\Modules\Notification\Services\TransferNotificationService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Services\EppTransferService;
use App\Modules\Transfer\Services\JobTransferService;
use App\Modules\Transfer\Services\TransferDataQueryService;
use App\Modules\Transfer\Services\TransferDomainService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use stdClass;

class AfternicOfferUpdateTransferFromPoll implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    private $dispatchDelayInSeconds = 180; // three minutes

    /**
     * Create a new job instance.
     */
    public function __construct($pollId, $status, $name)
    {
        $this->params = ['pollId' => $pollId, 'status' => $status, 'name' => $name];
        $registry = DomainParser::getRegistryName($name);

        $this->onConnection(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE);
        $this->onQueue(QueueTypes::DOMAIN_TRANSFER_POLL_UPDATE[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return $this->params['pollId'];
    }

    public function handle(): void
    {
        app(AuthLogger::class)->info('AfternicOfferTransferUpdate for domain: '.$this->params['name']);

        try {
            switch ($this->params['status']) {
                case EppDomainStatus::TRANSFER_CLIENT_APPROVED:
                case EppDomainStatus::TRANSFER_SERVER_APPROVED:
                    $this->handleServerApproved();
                    break;
            }
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), 'Cron:'));

            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();

                return;
            }

            $this->fail();
        }
    }

    private function handleServerApproved(): void
    {
        $id = $this->getRecord();

        DB::table('afternic_offers')->where('domain_name', $this->params['name'])->update(['status' => AfternicOfferConstants::PAID_TRANSFERED_COMPLETED]);
        MarketPlaceDomains::where('order_id', $id[0]->order_id)->update(['status' => MarketConstants::DOMAIN_COMPLETED]);

        AfternicMiddleware::instance()::sendCompleted($id[0]->order_id);

        app(AuthLogger::class)->info('AfternicOfferTransferUpdate: Domain Transfer success for domain '.$this->params['name']);
    }

    private function getRecord()
    {
        $query = DB::table('afternic_offers')
        ->where('domain_name', $this->params['name'])
        ->get();

        return DB::table('domains')
            ->join('registered_domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('market_place_domains', 'registered_domains.domain_id', '=', 'market_place_domains.registered_domain_id')
            ->where('domains.name', $this->params['name'])
            ->select('registered_domains.id as registered_domain_id', 'market_place_domains.order_id')
            ->get();
    }

    private function inboundServerApproved(): void
    {
        $params = $this->params;

        $domainData = $this->getInboundDomainData($params['name']);
        $this->validateDomainData($params['name'], $domainData);
        $this->handleTransferredInDomain($domainData, $params['status']);

        TransferNotificationService::instance()->sendTransferServerApprovedNotif($domainData->domain_name, $domainData->user_id, $domainData->registered_domain_status);
        JobTransferService::instance()->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE, DomainStatus::ACTIVE, $params, $domainData->domain_id);
    }

    private function handleTransferredInDomain(stdClass $domainData, string $status): void
    {
        $response = EppTransferService::instance()->callDatastoreTransferApproved($domainData->domain_name, TransferRequest::INBOUND.'.'.$status);
        $this->validateDatastoreApprove($response);
        $transferredIn = $response['data']['property']['transferredIn'];

        EppTransferService::instance()->domainTransferUpdate($domainData, $transferredIn);
        TransferDomainService::instance()->update($domainData->domain_name, $domainData->registered_domain_id, ['status' => TransferRequest::INBOUND.'.'.$status], 'Cron:');
        ScheduleDomainExpiryNotice::dispatch($domainData->user_id)->delay($this->dispatchDelayInSeconds);

        $this->updatePaymentNodeStatus($domainData->registered_domain_id, PaymentNodeStatus::COMPLETED);
    }

    private function validateDatastoreApprove(array $response): void
    {
        if (JobTransferService::instance()->isSuccessful($response)) {
            return;
        }

        app(AuthLogger::class)->error($this->fromWho('Datastore approving domain transfer has failed. Job has been added to the retry logs.', 'Cron:'));
        throw new Exception(QueueErrorTypes::FAILED);
    }

    private function updatePaymentNodeStatus($regID, $status)
    {
        DB::table('market_place_payment_invoices')
        ->join('market_place_node_invoices', 'market_place_payment_invoices.id', 'market_place_node_invoices.marketplace_payment_invoice_id')
        ->join('market_place_domains', 'market_place_domains.id', 'market_place_node_invoices.marketplace_payment_node_id')
        ->where('market_place_domains.registered_domain_id', '=', $regID)
        ->update(['status' => $status]);
    }

    private function validateDomainData(string $name, string|stdClass|null $data): void
    {
        if (empty($data)) {
            app(AuthLogger::class)->info($this->fromWho('No data found for domain '.$name.'.', 'Cron:'));
            throw new Exception(QueueErrorTypes::FAILED);
        }
    }

    private function getInboundDomainData(string $name): ?stdClass
    {
        return DB::table('registered_domains')
            ->join('transfer_domains', 'transfer_domains.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->where('domains.name', $name)
            ->where('transfer_domains.status', TransferRequest::PENDING_APPROVAL)
            ->whereNull('registered_domains.deleted_at')
            ->select(
                'registered_domains.*',
                'registered_domains.id as registered_domain_id',
                'registered_domains.status as registered_domain_status',
                'domains.*',
                'domains.name as domain_name',
                'domains.id as domain_id',
                'user_contacts.user_id',
                'user_contacts.registry_id',
                'users.email'
            )
            ->latest('registered_domains.created_at')->first();
    }

    /**
     * Execute the job.
     */
    public function retry(): void
    {
        $status = DomainStatus::PENDING;
        $type = QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE;
        $jobId = TransferDataQueryService::instance()->getDomainIdByDomainName($this->params['name']);

        // add to afternic job retry
        // JobTransferService::instance()->addRetryLogs($type, $status, $this->params, $jobId);
    }
}
