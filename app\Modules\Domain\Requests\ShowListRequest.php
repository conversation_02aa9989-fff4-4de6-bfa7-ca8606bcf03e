<?php

namespace App\Modules\Domain\Requests;

use App\Modules\Domain\Services\ViewServices\ViewDomainService;
use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'sortby' => ['string'],
            'name' => ['array'],
        ];
    }

    public function prepareForValidation()
    {
        $domains = session('searched_domains', []);

        if (isset($domains) && ! empty($domains)) {
            $this->merge(['name' => $domains]);
        }
    }

    public function getAll(): array
    {
        return ViewDomainService::instance()->getAll($this);
    }
}
