<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\DomainHistoryEvent;
use App\Events\UpdateDomainsTableEvent;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Util\Helper\Domain\DomainParser;

class UpdateNameserverService
{
    use UserLoggerTrait;

    private $isJob = true;

    private $userId;

    private $userEmail;

    private $maxAttemptError = 3;

    private $dispatchTenSeconds = 10;

    public static function instance(): self
    {
        $updateNameserverService = new self;

        return $updateNameserverService;
    }

    public function updateNameservers(array $request)
    {
        $nameserverArray = $this->createNameserverArray($request);
        $this->setDomainsToInProcess($request['domains']);
        $this->updateNameserverJob($request['domains'], $nameserverArray);
    }

    // PRIVATE FUNCTIONS

    private function setDomainsToInProcess(array $updatedDomains)
    {
        $updatedObj = collect($updatedDomains);
        $domainUpdateIds = $updatedObj->pluck('id')->toArray();
        DomainService::instance()->updateDomainStatus($domainUpdateIds, DomainStatus::IN_PROCESS);
        UpdateDomainsTableEvent::dispatch($this->getUserId());
    }

    private function updateNameserverJob(array $domains, array $nameserverArray)
    {
        $count = 0;
        foreach ($domains as $domain) {
            $payload = $this->createPayload($domain, $nameserverArray);
            JobDispatchService::instance()->updateEppDispatch(
                $payload,
                $this->dispatchTenSeconds + $count,
            );

            event(new DomainHistoryEvent([
                'domain_id' => $domain['id'],
                'type' => 'NAMESERVER_UPDATED',
                'user_id' => $this->getUserId(),
                'status' => 'success',
                'message' => 'Updated nameservers for domain "'.$domain['name'].'" to: '.implode(', ', $nameserverArray['names']),
                'payload' => $payload,
            ]));

            $count += 1;
        }
    }

    private function createPayload(array $domain, array $nameserverArray): array
    {
        $domainPayload = $this->createDomain($domain, $nameserverArray['names']);
        $registeredDomain = $this->createRegisteredDomain($domain);
        $registry = DomainParser::getRegistryName($domain['name']);

        return [
            JobPayloadKeys::DOMAIN => $domainPayload,
            JobPayloadKeys::REGISTERED_DOMAIN => $registeredDomain,
            JobPayloadKeys::REGISTRY => $registry,
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_MULTIPLE_NAMESERVERS,
        ];
    }

    private function createNameserverArray(array $request): array
    {
        $names = [];

        foreach ($request['nameservers'] as $key => $ns) {
            if (! empty(trim($ns))) {
                $names[] = strtolower($ns);
            }
        }

        sort($names);

        return [
            'names' => $names,
        ];
    }

    private function createDomain(array $domain, array $names)
    {
        $domain =
            [
                'id' => $domain['id'],
                'name' => $domain['name'],
                'registrant' => $domain['registrant'],
                'nameservers' => $names,
            ];

        return $domain;
    }

    private function createRegisteredDomain(array $domain)
    {
        $registeredDomain = [
            'id' => $domain['registered_domain_id'],
            'name' => $domain['name'],
        ];

        return $registeredDomain;
    }

    private function getUserId(): int
    {
        return auth()->user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        return auth()->user()->email ?? 'Unauthorized';
    }
}
