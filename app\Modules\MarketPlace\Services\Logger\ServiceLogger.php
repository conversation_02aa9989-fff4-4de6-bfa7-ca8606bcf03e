<?php

namespace App\Modules\MarketPlace\Services\Logger;

use App\Events\DomainHistoryEvent;
use App\Modules\CustomLogger\Services\AuthLogger;

class ServiceLogger
{
    protected $service;

    public function __construct($service)
    {
        $this->service = $service;
    }

    public function __call($method, $arguments)
    {
        app(AuthLogger::class)->info('[Service Call]: ' . json_encode([
            'service' => get_class($this->service),
            'method'  => $method,
            'arguments' => $arguments,
        ]));

        $result = call_user_func_array([$this->service, $method], $arguments);

        app(AuthLogger::class)->info('[Service Result]: ' . json_encode([
            'service' => get_class($this->service),
            'method'  => $method,
            'result'  => $result,
        ]));

        return $result;
    }

    private function sendEvent()
    {
        event(new DomainHistoryEvent([
            'domain_id' => $this->params['domain']->domain_id ?? null,
            'type' => 'TRANSFER_PURCHASE_PENDING',
            'user_id' => '', // $this->params['userId'],
            'status' => 'failed',
            'message' => '', // 'Attempt: '.$this->attempts().' Error: '.$errorMessage,
            'payload' => '', // $this->params,
        ]));
    }
}
