<?php

namespace App\Modules\Transfer\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UpdateTransferCartService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $updateTransferCartService = new self;

        return $updateTransferCartService;
    }

    public function update(array $request): bool
    {
        return self::updateTransferCart($request, $this);
    }

    public function softDelete(array $request): bool
    {
        return self::deleteTransferCart($request, $this);
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function updateTransferCart(array $request, self $updateTransferCartService): bool
    {
        $isUpdated = false;

        $id = $request['id'];
        $column = $request['column'];
        $value = $request['value'];

        $updatedValues = [
            $column => $value,
            'updated_at' => now(),
        ];

        $isUpdated = self::updateDB($id, $updatedValues);

        app(AuthLogger::class)->info($updateTransferCartService->fromWho('Update transfer cart with id ' . json_encode($id) . ' column ' . json_encode($column) . ' with value ' . json_encode($value)));

        return $isUpdated;
    }

    private function deleteTransferCart(array $request, self $updateTransferCartService): bool
    {
        $id = $request['id'];
        // $delete_type = $request['delete_type'];

        $updatedValues = [
            // 'delete_type' => $delete_type,
            'deleted_at' => now(),
            'updated_at' => now(),
        ];

        $isDeleted = self::updateDB($id, $updatedValues);
        app(AuthLogger::class)->info($updateTransferCartService->fromWho('Deleted from transfer cart id: ' . json_encode($id)));

        return $isDeleted;
    }

    private function getQuery(): Builder
    {
        return DB::table('transfer_carts')
            ->where('transfer_carts.user_id', self::getUserId())
            ->whereNull('transfer_carts.deleted_at');
    }

    private function updateDB($id, array $updatedValues): bool
    {
        return self::getQuery()->when(is_int($id), function (Builder $query) use ($id) {
            return $query->where('id', $id);
        })->when(is_array($id), function (Builder $query) use ($id) {
            return $query->whereIn('id', $id);
        })->update($updatedValues);
    }
}
