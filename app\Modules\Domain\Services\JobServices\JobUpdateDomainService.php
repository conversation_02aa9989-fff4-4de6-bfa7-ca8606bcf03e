<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Events\UpdateDomainsTableEvent;
use App\Models\Domain;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Domain\Services\UpdateServices\CancellationPayload;
use App\Modules\Domain\Services\UpdateServices\DetailsPayload;
use App\Modules\Domain\Services\UpdateServices\PostAutoRenewalGracePeriodPayload;
use App\Modules\Domain\Services\UpdateServices\RestrictionsPayload;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Util\Constant\QueueErrorTypes;
use Exception;
use Illuminate\Support\Facades\Config;

class JobUpdateDomainService
{
    use UserLoggerTrait;

    private $dispatchDelayInSeconds = 180;

    private JobRecord $jobRecord;

    public static function instance(): self
    {
        $jobUpdateDomainService = new self;

        return $jobUpdateDomainService;
    }

    public function handle(JobRecord $record)
    {
        $this->jobRecord = $record;
        app(AuthLogger::class)->info($this->fromWho('Domain details update start...', $this->jobRecord->email));

        $eppData = $this->checkDomainExists();
        $payload = $this->getPayload($eppData['info']);
        $this->updateEppDomain($payload);
    }

    // PRIVATE FUNCTIONS

    // GET PAYLOAD
    private function getPayload(array $eppInfo)
    {
        switch ($this->jobRecord->updateType) {
            case DomainJobTypes::UPDATE_RESTRICTIONS:
                return $this->getRestrictionsPayload($eppInfo);
            case DomainJobTypes::UPDATE_AFTER_REGISTER:
                return $this->getAfterRegisterPayload($eppInfo);
            case DomainJobTypes::UPDATE_MULTIPLE_NAMESERVERS:
                return $this->getNameserversPayload($eppInfo);
            case DomainJobTypes::UPDATE_POST_AUTO_RENEWAL_GRACE_PERIOD:
                return $this->getPostAutoRenewalGracePeriodPayload($eppInfo);
            case DomainJobTypes::UPDATE_CANCELLATION:
                return $this->getCancellationPayload($eppInfo);
            case DomainJobTypes::UPDATE_PRIVACY:
                return $this->getPrivacyPayload($eppInfo);
            case DomainJobTypes::UPDATE_DETAILS:
            default:
                return $this->getDetailsPayload($eppInfo);
        }
    }

    private function getDetailsPayload(array $eppInfo): array
    {
        $payload = new DetailsPayload($this->jobRecord->domain, $eppInfo);

        return $payload->get();
    }

    private function getAfterRegisterPayload(array $eppInfo): array
    {
        $payload = new RestrictionsPayload($this->jobRecord->domain, $eppInfo);

        return $payload->getDefault();
    }

    private function getRestrictionsPayload(array $eppInfo): array
    {
        $payload = new RestrictionsPayload($this->jobRecord->domain, $eppInfo);

        return $payload->get();
    }

    private function getNameserversPayload(array $eppInfo): array
    {
        $payload = new DetailsPayload($this->jobRecord->domain, $eppInfo);

        if (! $payload->isNameserverUpdated()) {
            $this->throwActivateException('Nameservers not updated for: '.$this->jobRecord->name);
        }

        return $payload->getNameserverPayload();
    }

    private function getPostAutoRenewalGracePeriodPayload(array $eppInfo): array
    {
        $payload = new PostAutoRenewalGracePeriodPayload($this->jobRecord->domain, $eppInfo);

        return $payload->getDefault();
    }

    private function getCancellationPayload(array $eppInfo): array
    {
        $payload = new CancellationPayload($this->jobRecord->domain);

        return $payload->get();
    }

    private function getPrivacyPayload(array $eppInfo): array
    {
        return [
            'name' => $this->jobRecord->domain->name,
            'domainPrivacyProtection' => $this->jobRecord->domain->privacy_protection,
        ];
    }

    // EPP FUNCTIONS

    private function checkDomainExists(): array
    {
        app(AuthLogger::class)->info($this->fromWho('Checking if domain already exists...', $this->jobRecord->email));

        // try {
        //     $response = EppDomainService::instance()->callEppDomainInfo($this->jobRecord->name);
        // } catch (Exception $e) {
        //     throw new Exception(QueueErrorTypes::RETRY);
        // }
        $response = EppDomainService::instance()->callEppDomainInfo($this->jobRecord->name);

        return $this->evaluateDomainExistsResponse($response);
    }

    private function updateEppDomain(array $payload)
    {
        $response = EppDomainService::instance()->updateEppDomain($payload, $this->jobRecord->email);
        $this->evaluateResponse($response);
        app(AuthLogger::class)->info($this->fromWho('Domain update end...', $this->jobRecord->email));
    }

    // ERROR FUNCTIONS
    private function throwActivateException(string $error)
    {
        // For domain cancellation, keep status as IN_PROCESS even on errors
        if ($this->jobRecord->updateType === DomainJobTypes::UPDATE_CANCELLATION) {
            $this->jobRecord->stopJobRetry(DomainStatus::IN_PROCESS);
            DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::IN_PROCESS, false, $this->jobRecord->email);
        } else {
            $this->jobRecord->stopJobRetry(DomainStatus::ACTIVE);
            DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::ACTIVE, false, $this->jobRecord->email);
        }

        UpdateDomainsTableEvent::dispatch($this->jobRecord->userId);
        throw new Exception($error);
    }

    private function throwNotAvailableException(string $error)
    {
        $this->jobRecord->stopJobRetry(DomainStatus::NOT_AVAILABLE);
        DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::NOT_AVAILABLE, false, $this->jobRecord->email);
        UpdateDomainsTableEvent::dispatch($this->jobRecord->userId);
        throw new Exception($error);
    }

    // RESPONSE FUNCTIONS
    private function getResponseStatus(array $response)
    {
        return array_key_exists('status', $response) ? $response['status'] : Config::get('domain.status.error');
    }

    private function evaluateResponse(array $response)
    {
        $status = $this->getResponseStatus($response);
        $expectedStatus = Config::get('domain.status.ok');
        if ($status === $expectedStatus) {
            return $this->evaluateUpdateSuccess();
        }

        return $this->evaluateUpdateFailed($response);
    }

    private function evaluateDomainExistsResponse(array $response)
    {
        $status = $this->getResponseStatus($response);

        if ($status === Config::get('domain.status.ok')) {
            return $this->getEppInfo($response);
        }

        return $this->evaluateUpdateFailed($response);
    }

    private function getEppInfo(array $response)
    {
        if (array_key_exists('data', $response)) {
            return [
                'exists' => true,
                'info' => $response['data'],
            ];
        }

        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function evaluateUpdateSuccess()
    {
        if ($this->jobRecord->updateType === DomainJobTypes::UPDATE_PRIVACY) {
            $this->updatePrivacyProtection();
        }

        // For domain cancellation, keep status as IN_PROCESS instead of ACTIVE
        if ($this->jobRecord->updateType === DomainJobTypes::UPDATE_CANCELLATION) {
            $this->jobRecord->stopJobRetry(DomainStatus::IN_PROCESS);
            DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::IN_PROCESS, false, $this->jobRecord->email);
        } else {
            JobDispatchService::instance()->refreshEppDispatch($this->jobRecord->getRecord());
            $this->jobRecord->stopJobRetry(DomainStatus::ACTIVE);
        }

        if ($this->jobRecord->updateType === DomainJobTypes::UPDATE_POST_AUTO_RENEWAL_GRACE_PERIOD) {
            DomainNotificationService::instance()->sendDomainDeletionNotif($this->jobRecord->name, $this->jobRecord->userId);
        }

        return true;
    }

    private function updatePrivacyProtection(): void
    {
        Domain::where('id', $this->jobRecord->domainId)->update([
            'privacy_protection' => $this->jobRecord->domain->privacy_protection,
        ]);
    }

    private function evaluateUpdateFailed(array $response)
    {
        $errorCode = array_key_exists('eppCode', $response) ? $response['eppCode'] : $response['statusCode'] ?? Config::get('domain.code.error');

        switch ($errorCode) {
            case Config::get('domain.code.parameter_policy_error'):
                $this->throwActivateException('Parameter policy error: '.$this->jobRecord->name);
            case Config::get('domain.code.object_does_not_exist'):
            case Config::get('domain.code.not_found'):
                $this->throwNotAvailableException('Object does not exist: '.$this->jobRecord->name);
            default:
                $this->evaluateFailedResponse($response);
        }
    }

    private function evaluateFailedResponse(array $response)
    {
        if (array_key_exists('result', $response)) {
            $error = 'Domain update error: '.json_encode($response['result']);
            app(AuthLogger::class)->error($this->fromWho($error, $this->jobRecord->email));
        }

        throw new Exception(QueueErrorTypes::RETRY);
    }
}
