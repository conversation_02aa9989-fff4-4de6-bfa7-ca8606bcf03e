<?php

namespace App\Rules\Cart;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CartArrayExists implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! $this->cartIdExists($value)) {
            $fail($this->message());
        }
    }

    public function passes($attribute, $value)
    {
        return $this->cartIdExists($value);
    }

    public function message()
    {
        return 'Items are not valid.';
    }

    // PRIVATE FUNCTIONS

    private function cartIdExists(array $domains): bool
    {
        if (empty($domains)) {
            return false;
        }

        $cartObj = collect($domains);
        $ids = $cartObj->pluck('id')->toArray();

        $existingCount = DB::table('carts')
            ->where('user_id', Auth::user()->id)
            ->whereNull('deleted_at')
            ->whereIn('id', $ids)
            ->count();

        if ($existingCount !== count($ids)) {
            return false;
        }

        return true;
    }
}
