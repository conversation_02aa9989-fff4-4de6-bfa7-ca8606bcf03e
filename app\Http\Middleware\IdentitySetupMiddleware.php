<?php

namespace App\Http\Middleware;

use App\Modules\Stripe\Constants\StripeIdentityEvent;
use App\Modules\Stripe\Services\IdentityService;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class IdentitySetupMiddleware extends Authenticate
{
    public function handle($request, Closure $next, ...$guards)
    {
        if (
            (strcmp(config('app.env'), 'production') != 0)
            && ! config('app.identity_verification_enabled')
        ) {
            return $next($request);
        }

        $status = IdentityService::instance()->getIdentityStatus();

        // Log::info('Identity Status: '.$status);

        if ($status == StripeIdentityEvent::VERIFIED && Auth::user()->is_identity_verified) {
            return $next($request);
        }

        if ($status == null) {
            return redirect()->route('identity.setup', ['is_retry' => Auth::user()->contact_setup ?? false]);
        }

        switch ($status) {
            case StripeIdentityEvent::CREATED:
                Auth::logout();

                return redirect()->route('login')->with(
                    'flash',
                    [
                        'message' => 'User Identity Verification is currently in progress. Please check your email for updates.',
                    ]
                );
                break;

            case StripeIdentityEvent::PROCESSING:
                Auth::logout();

                return redirect()->route('login')->with(
                    'flash',
                    [
                        'message' => 'User Identity Verification is currently in progress. Please check your email for updates.',
                    ]
                );
                break;

            case StripeIdentityEvent::REDACTED:
                return redirect()->route('identity.setup', ['is_retry' => Auth::user()->contact_setup ?? false]);
                break;

            case StripeIdentityEvent::REQUIRES_INPUT:
                return redirect()->route('identity.setup', ['is_retry' => Auth::user()->contact_setup ?? false]);
                break;

            case StripeIdentityEvent::CANCELLED:
                return redirect()->route('identity.setup', ['is_retry' => Auth::user()->contact_setup ?? false]);
                break;
        }

        return redirect()->route('identity.status');
    }
}
