<?php

namespace App\Listeners\Payment;

use App\Events\Payment\DepositBankTransferEvent;
use App\Modules\AccountCredit\Services\DepositAccountCreditService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class DepositBankTransferListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(DepositBankTransferEvent $event)
    {
        try {
            DepositAccountCreditService::instance()->createPayment(
                [
                    'source_id' => $event->sourceId,
                    'payment_service_id' => $event->paymentServiceId,
                ],
                $event->userId,
                PaymentSummaryType::ACCOUNT_BALANCE,
                $event->sourceType,
            );
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }
}
