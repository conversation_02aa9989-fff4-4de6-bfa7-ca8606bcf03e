<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class RedemptionOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'domain_id',
        'total_amount',
        'uuid',
        'paid_at',
        'valid_until',
        'note',
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'valid_until' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    public function redemptionPaymentServices(): HasMany
    {
        return $this->hasMany(RedemptionPaymentService::class);
    }

    public function getStatusAttribute(): string
    {
        if ($this->deleted_at) {
            return 'Completed';
        } elseif ($this->paid_at) {
            return 'In Process';
        } else {
            return 'Not Paid';
        }
    }
}
