<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\EmailSent;
use App\Mail\Constants\MailConstant;
use App\Mail\DomainRedemptionNotice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Util\Constant\QueueConnection;
use App\Util\Helper\Domain\DomainParser;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use stdClass;

class PostAutoRenewalGracePeriodService
{
    use UserLoggerTrait;

    private $userId;

    private $userEmail;

    private $dispatchTenSeconds = 10;

    public static function instance(): self
    {
        $postAutoRenewalGracePeriodService = new self;

        return $postAutoRenewalGracePeriodService;
    }

    public function setForDeletion(Collection $domains): void
    {
        $this->setDomainsServerRenewAt($domains);
        $this->storeForPendingDeletion($domains);
        $this->dispatchJobs($domains->toArray());

        $data = $domains->groupBy('user_email')->toArray();

        foreach ($data as $email => $domains) {
            $this->sendEmail($email, array_column($domains, 'name'));
        }
    }

    public function dispatchJobs(array $domains): void
    {
        $count = 0;
        foreach ($domains as $domain) {
            $payload = $this->createPayload($domain);
            JobDispatchService::instance()->updatePostAutoRenewalGracePeriodDispatch(
                $payload,
                $this->dispatchTenSeconds + $count,
            );
            $count += 1;
        }
    }

    // PRIVATE FUNCTIONS

    private function setDomainsServerRenewAt(Collection $domainsList)
    {
        $domainUpdateIds = $domainsList->pluck('id')->toArray();
        DomainService::instance()->updateDomainServerRenewAt($domainUpdateIds);
    }

    private function storeForPendingDeletion(Collection $domains): void
    {
        $now = Carbon::now();
        $insertPayload = array_map(function ($domain) use ($now) {
            return [
                'registered_domain_id' => $domain->registered_domain_id,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }, $domains->all());

        DB::table('pending_domain_deletions')->insert($insertPayload);
    }

    private function createDomain(stdClass $domain): array
    {
        $status = json_decode($domain->client_status, true);

        $payload = [
            'id' => $domain->id,
            'name' => $domain->name,
            'registrant' => $domain->registrant,
            'client_status' => $status,
        ];

        return $payload;
    }

    private function createRegisteredDomain(stdClass $domain): array
    {
        $payload = [
            'id' => $domain->registered_domain_id,
            'name' => $domain->name,
        ];

        return $payload;
    }

    private function createPayload(stdClass $domain): array
    {
        $domainPayload = $this->createDomain($domain);
        $registeredDomain = $this->createRegisteredDomain($domain);
        $registry = DomainParser::getRegistryName($domain->name);

        return [
            JobPayloadKeys::DOMAIN => $domainPayload,
            JobPayloadKeys::REGISTERED_DOMAIN => $registeredDomain,
            JobPayloadKeys::REGISTRY => $registry,
            JobPayloadKeys::USER_ID => $domain->user_id,
            JobPayloadKeys::EMAIL => $domain->user_email,
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_POST_AUTO_RENEWAL_GRACE_PERIOD,
        ];
    }

    private function sendEmail(string $email, array $domains): void
    {
        try {
            $queueMessage = (new DomainRedemptionNotice($domains))
                ->onConnection(QueueConnection::MAIL_JOB)
                ->onQueue(MailConstant::DOMAIN_REDEMPTION_NOTICE);

            $payloadData = json_encode($domains);

            $this->emailHistory($domains, $payloadData);

            Mail::to($email)->send($queueMessage);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
        }
    }

    private function emailHistory(array $domains, string $payloadData): void
    {
        event(new EmailSent(
            $domains['user_id'],
            $domains['name'],
            $domains['email'],
            'Domain Redemption Notice',
            'Domain Redemption Notice',
            $payloadData,
            null
        ));
    }
}
