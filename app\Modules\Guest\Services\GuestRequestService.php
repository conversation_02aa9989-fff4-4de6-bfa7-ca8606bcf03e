<?php

namespace App\Modules\Guest\Services;

use App\Models\GuestRequest;
use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\Guest\Constants\RequestStatus;
use App\Modules\Guest\Constants\RequestType;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Support\Facades\URL;

class GuestRequestService
{
    public static function instance()
    {
        $guestRequestService = new self;

        return $guestRequestService;
    }

    public function storeRegistrationAccessRequest($request)
    {
        $url = URL::temporarySignedRoute(
            'register',
            now()->addDay(),
            ['email' => $request->email, 'ip' => ClientIp::getClientIp($request)]
        );

        $this->store($request, RequestType::REGISTRATION_ACCESS, $url);

        // send notif
        AdminNotificationService::instance()->sendClientRequestNotif([
            'email' => $request->email,
            'request_type' => RequestType::REGISTRATION_ACCESS,
        ]);
    }

    public function storeNewIpRequest($request)
    {
        $this->store($request, RequestType::NEW_IP, route('login'));

        // send notif
        AdminNotificationService::instance()->sendClientRequestNotif([
            'email' => $request->email,
            'request_type' => RequestType::NEW_IP,
        ]);
    }

    private function store($request, $type, $url = '')
    {
        $guest = new GuestRequest;
        $guest->email = $request->email;
        $guest->message = $request->message;
        $guest->status = RequestStatus::PENDING;
        $guest->url = $url;
        $guest->type = $type;
        $guest->ip = ClientIp::getClientIp($request);
        $guest->save();
    }
}
