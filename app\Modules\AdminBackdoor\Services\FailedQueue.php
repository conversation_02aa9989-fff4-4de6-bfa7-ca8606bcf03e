<?php

namespace App\Modules\AdminBackdoor\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Support\Facades\Artisan;

class FailedQueue
{
    public static function retry($option)
    {
        try {
            Artisan::call('queue:retry '.$option);

            $output = Artisan::output();
            app(AuthLogger::class)->info('Artisan: '.$output);
        } catch (Exception $e) {
            $errorMsg = 'Artisan: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }

    }

    public static function flush()
    {
        try {
            Artisan::call('queue:flush');

            $output = Artisan::output();
            app(AuthLogger::class)->info('Artisan: '.$output);
        } catch (Exception $e) {
            $errorMsg = 'Artisan: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    public static function forget($option)
    {
        try {
            Artisan::call('queue:forget '.$option);

            $output = Artisan::output();
            app(AuthLogger::class)->info('Artisan: '.$output);
        } catch (Exception $e) {
            $errorMsg = 'Artisan: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }
}
