<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\MakeOfferRequest;
use App\Modules\MarketPlace\Requests\OfferCheckoutRequest;
use App\Modules\MarketPlace\Requests\OfferHistoryRequest;
use App\Modules\MarketPlace\Requests\OfferHistoryStatusUpdateRequest;
use App\Modules\MarketPlace\Requests\UserCounterRequest;
use App\Modules\MarketPlace\Services\MarketOfferService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class MarketOfferController extends Controller
{
    public function index() : Response
    {
        return Inertia::render('Market/MarketOffers/ShowOffers', ['myoffers' => MarketOfferService::instance()->getOffers()]);
    }

    public function store(MakeOfferRequest $request) : void
    {
        $request->sendOffer();
    }

    public function history(OfferHistoryRequest $request) : Collection
    {
        return $request->getHistory();
    }

    public function close(OfferHistoryStatusUpdateRequest $request) : void
    {
        $request->closeDeal();
    }

    public function counter(UserCounterRequest $request) : void
    {
        $request->counter();
    }

    public function checkout(OfferCheckoutRequest $request) : Response
    {
        $data = $request->checkout();

        return Inertia::render('Market/OfferCheckout/OfferCheckout', $data);
    }

    public function checkoutStore(Request $request)
    {
        $summaryId = MarketOfferService::instance()->store($request->all());

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment successful. Domain acquisition is in process.',
            'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
        ]);
    }
}
