<?php

namespace App\Modules\Domain\Requests\Lock;

use App\Modules\Domain\Services\UpdateServices\LockDomainService;
use Illuminate\Foundation\Http\FormRequest;

class LockConfirmDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'domains' => ['required', 'array', 'min:1'],
            'isDisable' => ['required', 'boolean'],
        ];
    }

    public function getData(): array
    {
        return LockDomainService::instance()->getLockConfirmData($this->all());
    }
}
