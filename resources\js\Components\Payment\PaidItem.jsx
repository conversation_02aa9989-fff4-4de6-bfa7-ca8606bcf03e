export default function PaidItem({ label, amount }) {
    const formatAmount = (amount) => {
        return amount ? parseFloat(amount).toFixed(2) : "0.00";
    }

    return (
        <>
            <div className="flex flex-col text-lg font-semibold">
                <div className="flex items-center justify-between">
                    <span className=" text-inherit">{label}</span>
                    <span className=" text-inherit">${formatAmount(amount)}</span>
                </div>
            </div>
        </>
    );
}
