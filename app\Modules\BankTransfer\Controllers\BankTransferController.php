<?php

namespace App\Modules\BankTransfer\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\BankTransfer\Requests\ShowListRequest;
use App\Modules\BankTransfer\Requests\StoreWireTransferRequest;
use Inertia\Inertia;

class BankTransferController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('WireTransfer/Index', $request->show());
    }

    public function store(StoreWireTransferRequest $request)
    {
        $request->store();

        return redirect()->route('account.balance.index');

        // return true;
    }
}
