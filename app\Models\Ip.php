<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ip extends Model
{
    use HasFactory;

    /**
     * Manually Define Table Name.  
     * 
     * @var string 
     */
    protected $table = 'ips';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable =
    [
        'ip',
        'is_active',
    ];

    /**
     * Define attributes that should be hidden for arrays. 
     * 
     * @var array 
     */
    protected $hidden =
    [
        //...
    ];

    /**
     * Cast Type 
     * 
     * @var array 
     */
    protected $casts =
    [
        //
    ];
}
