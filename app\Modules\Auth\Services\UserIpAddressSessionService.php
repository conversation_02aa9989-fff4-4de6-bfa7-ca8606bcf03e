<?php

namespace App\Modules\Auth\Services;

use App\Models\Ip;
use App\Models\UserIpAddressSessions;
use Jen<PERSON>gers\Agent\Agent;

class UserIpAddressSessionService
{
    /**
     * Record Entry
     *
     * @poram int    $userId
     *
     * @param  string  $ipAddress
     * @return void
     */
    public function recordEntry($userId, $ipAddress)
    {
        $agent = new Agent;

        $session = UserIpAddressSessions::where('user_id', '=', $userId)->first();
        $ip = Ip::where('ip', '=', $ipAddress)->first();

        if ($ip == null) {
            $ip = Ip::create(
                [
                    'ip' => $ipAddress,
                    'is_active' => true,
                ]
            );
        }

        if ($session != null) {
            $session->ip_id = $ip->id;
            $session->user_agent = $agent->browser();

            $session->save();
        } else {
            UserIpAddressSessions::create(
                [
                    'user_id' => $userId,
                    'ip_id' => $ip->id,
                    'user_agent' => $agent->browser(),
                ]
            );
        }
    }
}
