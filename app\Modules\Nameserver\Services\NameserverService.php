<?php

namespace App\Modules\Nameserver\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\Auth;

class NameserverService
{
    use CursorPaginate;

    private $userId;

    private static $pageLimit = 20;

    public function __construct()
    {
        $this->userId = Auth::user()->id;
    }

    public static function instance(): self
    {
        $nameserverService = new self;

        return $nameserverService;
    }

    public function check($name)
    {
        $success = false;
        $message = 'Cannot validate.';

        $response = NameserverEppService::instance()->check(strtolower($name));

        if (array_key_exists('data', $response)) {
            $success = ! $response['data']['available'];
            $message = $success ? '' : 'Nameserver does not exist.';
        }

        app(AuthLogger::class)->info('Nameserver check: '.json_encode($response));

        return ['success' => $success, 'message' => $message];
    }
}
