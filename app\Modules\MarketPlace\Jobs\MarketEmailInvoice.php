<?php

namespace App\Modules\MarketPlace\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Services\MarketDomainService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MarketEmailInvoice implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $id;

    private $userId;

    private $order_id;

    private $domain;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(int $id, int $userId, string $order_id, string $domain)
    {
        $this->id = $id;
        $this->userId = $userId;
        $this->order_id = $order_id;
        $this->domain = $domain;
    }

    public $uniqueFor = 10;

    public function uniqueId(): int
    {
        return intval(Carbon::now()->timestamp.$this->id);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            MarketDomainService::instance()->sendInvoice($this->id, $this->userId, $this->order_id, $this->domain);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('MarketEmailInvoice: '.$e->getMessage());
            $this->fail();
        }
    }
}
