<?php

namespace App\Modules\AccountCredit\Services\CreditSources;

use App\Events\ClientActivityEvent;
use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Exceptions\FailedRequestException;
use App\Modules\AccountCredit\Constants\AccountCreditSourceType;
use App\Modules\AccountCredit\Constants\AccountCreditType;
use App\Modules\AccountCredit\Contracts\AccountCreditInterface;
use App\Modules\BankTransfer\Services\BankTransferMailService;
use App\Modules\BankTransfer\Services\BankTransferService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;

class BankTransferAccountCredit implements AccountCreditInterface
{
    public function store(array $data, int $userId, string $paymentServiceType = PaymentServiceType::BANK_TRANSFER)
    {
        $createdPaymentService = PaymentServiceHelper::instance()->pay($data, $userId, $paymentServiceType);

        if (! $createdPaymentService['bank_transfer']) {
            throw new FailedRequestException(400, 'Bank transfer not created.', 'Error');
        }
        BankTransferMailService::instance()->sendPendingMail($createdPaymentService['bank_transfer'], $userId);
        $this->createClientActivityEvent($data, $userId);

        return null;
    }

    public function deposit(int $sourceId, int $userId)
    {
        $bankTransfer = BankTransferService::instance()->getVerifiedTransferById($sourceId, $userId);

        if (! $bankTransfer) {
            throw new FailedRequestException(404, 'Bank transfer transaction not found.', 'Not Found');
        }

        $createdPaymentService = $this->createPaymentService($userId, $sourceId, $bankTransfer);
        BankTransferService::instance()->updateCreditedTransfer($sourceId);

        return $createdPaymentService;
    }

    public function credit(int $sourceId, int $userId)
    {
        throw new FailedRequestException(400, 'Credit not supported.', 'Error');
    }

    public function createPaymentSummary(object $paymentService, int $userId)
    {
        $sourceId = $paymentService->bank_transfer_id;
        $bankTransfer = BankTransferService::instance()->getBankTransferById($sourceId, $userId, $paymentService->id);
        $paymentServiceType = PaymentServiceType::BANK_TRANSFER;

        app(AuthLogger::class)->info('createPaymentSummary bank transfer: '.json_encode($bankTransfer));
        app(AuthLogger::class)->info('createPaymentSummary payment service: '.json_encode($paymentService));
        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::ACCOUNT_BALANCE],
            'paid_amount' => $bankTransfer->gross_amount,
            'total_amount' => $bankTransfer->net_amount,
            'payment_service_id' => $paymentService->id,
            'source' => $paymentServiceType,
        ];

        event(new CreatePaymentSummaryEvent($data, $userId, PaymentSummaryType::ACCOUNT_BALANCE));
    }

    // PRIVATE FUNCTIONS

    private function createPaymentService(int $userId, int $sourceId, object $bankTransfer)
    {
        $data = [
            'user_id' => $userId,
            'type' => AccountCreditType::DEBIT,
            'amount' => $bankTransfer->net_amount,
            'sourceId' => $sourceId,
            'sourceType' => AccountCreditSourceType::BANK_TRANSFER,
        ];

        $createdPaymentService = PaymentServiceHelper::instance()
            ->pay($data, $userId, PaymentServiceType::ACCOUNT_DEPOSIT);

        return $createdPaymentService;
    }

    private function createClientActivityEvent(array $data, string $userId)
    {
        $message = 'Requested '.PaymentServiceType::TEXT[PaymentServiceType::BANK_TRANSFER].' approval of $'.$data['amount'].'.';

        event(new ClientActivityEvent(
            $userId,
            UserTransactionType::PAYMENT_SUMMARY,
            $message,
            '',
            $data,
        ));
    }
}
