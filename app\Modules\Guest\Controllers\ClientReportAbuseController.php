<?php

namespace App\Modules\Guest\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Guest\Requests\ReportAbuseForm;
use Inertia\Inertia;

class ClientReportAbuseController extends Controller
{
    public function store(ReportAbuseForm $request)
    {
        $request->sendData();

        return Inertia::render('Notice/GuestMessage', [
            'message' => "Report sent. We will get back to you once we've finish reviewing the domain.",
        ]);
    }
}
