<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('domain_cancellation_requests', 'support_agent_id')) {
            Schema::table('domain_cancellation_requests', function (Blueprint $table) {
                $table->unsignedBigInteger('support_agent_id')->nullable();
            });
        }

        if (!Schema::hasColumn('domain_cancellation_requests', 'support_agent_name')) {
            Schema::table('domain_cancellation_requests', function (Blueprint $table) {
                $table->text('support_agent_name')->nullable();
            });
        }

        if (!Schema::hasColumn('domain_cancellation_requests', 'support_note')) {
            Schema::table('domain_cancellation_requests', function (Blueprint $table) {
                $table->text('support_note')->nullable();
            });
        }

        if (!Schema::hasColumn('domain_cancellation_requests', 'feedback_date')) {
            Schema::table('domain_cancellation_requests', function (Blueprint $table) {
                $table->timestamp('feedback_date')->nullable();
            });
        }
    }

    public function down()
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->dropColumn(['support_agent_id', 'support_agent_name', 'support_note', 'feedback_date']);
        });
    }
};
