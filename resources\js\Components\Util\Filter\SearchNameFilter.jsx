import { IoMdSearch } from "react-icons/io";
import { MdCancel } from "react-icons/md";
import { useRef } from "react";
import useOutsideClick from "@/Util/useOutsideClick";

export default function SearchNameFilter({
    value = "",
    onChange,
    onClear,
    onSubmit,
    onOutsideClick,
    placeholder = "Search by contact name...",
    hasSearch = false,
    className = ""
}) {
    const searchRef = useRef(null);

    useOutsideClick(searchRef, () => {
        if (onOutsideClick) {
            onOutsideClick();
        }
    });

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            onSubmit();
        }
    };

    const handleInputChange = (e) => {
        const newValue = e.target.value;
        onChange(e);
        
        if (!newValue.trim() && hasSearch) {
            onClear();
        }
    };

    return (
        <div className="relative flex items-center" ref={searchRef}>
            <form onSubmit={(e) => {
                e.preventDefault();
                onSubmit();
            }}>
                <div className="relative">
                    <input
                        type="text"
                        placeholder={placeholder}
                        value={value}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        className={`pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${className}`}
                    />
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <IoMdSearch className="h-5 w-5 text-gray-400" />
                    </div>
                    {hasSearch && value && (
                        <button
                            type="button"
                            onClick={onClear}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-danger transition-colors duration-200"
                            title="Clear search"
                        >
                            <MdCancel className="h-5 w-5" />
                        </button>
                    )}
                </div>
            </form>
        </div>
    );
}
