import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import SearchNameFilter from "@/Components/Util/Filter/SearchNameFilter";

export default function DomainPushFilterSearchComponent({
    hasDomainSearch = false,
    setHasDomainSearch,
    setDomainSearchValue,
    searchedDomain = "",
    transaction,
    currentTab = null,
}) {
    const handleRemoveSearch = () => {
        setHasDomainSearch(false);
        setDomainSearchValue('');

        let payload = {};

        if (transaction) payload.transaction = transaction;
        if (currentTab) payload.tab = currentTab;

        router.get(route(route().current(), payload));
    };

    const handleSearch = (e) => {
        const value = e.target.value;
        setDomainSearchValue(value);
    };

    const handleSubmitSearch = () => {
        const searchTerm = searchedDomain.trim();

        if (!searchTerm) {
            toast.error("Please enter a domain to search.");
            return;
        }

        let payload = {
            domain: searchTerm
        };

        if (transaction) payload.transaction = transaction;
        if (currentTab) payload.tab = currentTab;

        router.get(route(route().current(), payload));

        setHasDomainSearch(true);

        toast.info("Searching domain, please wait...");
    };

    const handleOutsideClick = () => {
        if (searchedDomain.trim() && !hasDomainSearch) {
            handleSubmitSearch();
        }
    };

    return (
        <SearchNameFilter
            value={searchedDomain}
            onChange={handleSearch}
            onClear={handleRemoveSearch}
            onSubmit={handleSubmitSearch}
            onOutsideClick={handleOutsideClick}
            hasSearch={hasDomainSearch}
            placeholder="Search domain..."
        />
    );
} 