<?php

namespace App\Listeners\Payment;

use App\Events\Payment\CreditAccountBalanceEvent;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreditAccountBalanceListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(CreditAccountBalanceEvent $event)
    {
        try {
            PaymentSummaryService::instance()->createRefund(
                [
                    'source_id' => $event->sourceId,
                    'payment_service_id' => $event->paymentServiceId,
                    'payment_summary_type' => PaymentSummaryType::ACCOUNT_BALANCE,
                    'payment_service_type' => $event->sourceType,
                ],
                0,
                $event->userId,
            );
            // AccountCreditService::instance()->creditToAccount($event->sourceId, $event->paymentServiceId, $event->userId, $event->sourceType);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }
}
