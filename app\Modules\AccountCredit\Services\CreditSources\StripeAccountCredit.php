<?php

namespace App\Modules\AccountCredit\Services\CreditSources;

use App\Events\ClientActivityEvent;
use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Exceptions\FailedRequestException;
use App\Modules\AccountCredit\Constants\AccountCreditSourceType;
use App\Modules\AccountCredit\Constants\AccountCreditType;
use App\Modules\AccountCredit\Contracts\AccountCreditInterface;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\StripeTransaction\Services\StripeTransactionService;

class StripeAccountCredit implements AccountCreditInterface
{
    public function store(array $data, int $userId, string $paymentServiceType = PaymentServiceType::STRIPE)
    {
        $createdPaymentService = PaymentServiceHelper::instance()->pay($data, $userId, PaymentServiceType::STRIPE);
        $paymentService = $createdPaymentService['payment_service'];

        $this->createClientActivityEvent($userId, $createdPaymentService, $data, $paymentServiceType);

        return [
            'payload' => [
                'source_id' => $paymentService->stripe_id,
                'payment_service_id' => $paymentService->id,
            ],
            'userId' => $userId,
            'summaryType' => PaymentSummaryType::ACCOUNT_BALANCE,
            'paymentServiceType' => PaymentServiceType::STRIPE,
        ];
    }

    public function deposit(int $sourceId, int $userId)
    {
        $stripeTransaction = StripeTransactionService::instance()->getById($sourceId, $userId);

        if (! $stripeTransaction) {
            throw new FailedRequestException(404, 'Stripe transaction not found.', 'Not Found');
        }

        $createdPaymentService = $this->createPaymentService($userId, $sourceId, $stripeTransaction);

        return $createdPaymentService;
    }

    public function credit(int $sourceId, int $userId)
    {
        throw new FailedRequestException(400, 'Credit not supported.', 'Error');
    }

    public function createPaymentSummary(object $paymentService, int $userId)
    {
        $sourceId = $paymentService->stripe_id;
        $stripeTransaction = StripeTransactionService::instance()->getById($sourceId, $userId);
        $paymentServiceType = PaymentServiceType::STRIPE;

        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::ACCOUNT_BALANCE],
            'paid_amount' => $stripeTransaction->gross_amount,
            'total_amount' => $stripeTransaction->net_amount,
            'payment_service_id' => $paymentService->id,
            'source' => $paymentServiceType,
        ];

        event(new CreatePaymentSummaryEvent($data, $userId, PaymentSummaryType::ACCOUNT_BALANCE));
    }

    // PRIVATE FUNCTIONS

    private function createPaymentService(int $userId, int $sourceId, object $stripeTransaction)
    {
        $data = [
            'user_id' => $userId,
            'type' => AccountCreditType::DEBIT,
            'amount' => $stripeTransaction->net_amount,
            'sourceId' => $sourceId,
            'sourceType' => AccountCreditSourceType::STRIPE,
        ];

        $createdPaymentService = PaymentServiceHelper::instance()
            ->pay($data, $userId, PaymentServiceType::ACCOUNT_DEPOSIT);

        return $createdPaymentService;
    }

    private function createClientActivityEvent(int $userId, array $createdPaymentService, array $paymentPayload, string $paymentServiceType)
    {
        $stripeObj = $createdPaymentService['data'];
        $netAmount = $stripeObj['net_amount'] ?? $paymentPayload['net_amount'] ?? 0;
        $grossAmount = $stripeObj['gross_amount'] ?? $paymentPayload['gross_amount'] ?? 0;
        $paymentService = $createdPaymentService['payment_service'];

        $clientData = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::ACCOUNT_BALANCE],
            'paid_amount' => $grossAmount ?? 0,
            'total_amount' => $netAmount ?? 0,
            'payment_service_id' => $paymentService->id ?? 0,
            'source' => $paymentServiceType,
        ];

        $message = 'Added $'.$clientData['total_amount'].' to account balance via '.PaymentServiceType::TEXT[$paymentServiceType].'.';

        event(new ClientActivityEvent(
            $userId,
            UserTransactionType::PAYMENT_SUMMARY,
            $message,
            '',
            $clientData,
        ));
    }
}
