<?php

namespace App\Modules\AccountCredit\Contracts;

use App\Modules\AccountCredit\Constants\AccountCreditSourceType;
use App\Modules\AccountCredit\Services\CreditSources\BankTransferAccountCredit;
use App\Modules\AccountCredit\Services\CreditSources\StripeAccountCredit;
use App\Modules\AccountCredit\Services\CreditSources\SystemToAccountCredit;
use Exception;

class AccountCreditSourcePicker implements AccountCreditSourcePickerInterface
{
    public function getType(string $type)
    {
        switch ($type) {
            case AccountCreditSourceType::STRIPE:
                return new StripeAccountCredit;
            case AccountCreditSourceType::BANK_TRANSFER:
                return new BankTransferAccountCredit;
            case AccountCreditSourceType::SYSTEM_CREDIT:
                return new SystemToAccountCredit;
            default:
                throw new Exception('Payment not supported.');
        }
    }
}
