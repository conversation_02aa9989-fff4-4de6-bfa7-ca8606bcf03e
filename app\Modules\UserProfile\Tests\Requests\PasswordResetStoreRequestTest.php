<?php
namespace App\Modules\UserProfile\Tests\Requests;

use Illuminate\Support\Facades\Validator;
use App\Modules\UserProfile\Requests\PasswordResetStoreRequest;
use App\Modules\UserProfile\Tests\Datasets\CommonValidationDataset;

it('passes validation with valid data', function () {
    $request = new PasswordResetStoreRequest;
    $validDatasets = CommonValidationDataset::validPasswordResetData();

    foreach ($validDatasets as $testName => $data) {
        $request->merge($data);
        $validator = Validator::make($data, $request->rules());
        expect($validator->passes())->toBeTrue("Failed validation for {$testName}: " . json_encode($validator->errors()));
    }
});

it('fails validation when required fields are missing', function () {
    $request = new PasswordResetStoreRequest;
    $request->merge([]);

    $validator = Validator::make([], $request->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('token'))->toBeTrue("Failed to validate required field for token");
    expect($validator->errors()->has('email'))->toBeTrue("Failed to validate required field for email");
    expect($validator->errors()->has('password'))->toBeTrue("Failed to validate required field for password");
    expect($validator->errors()->has('password_confirmation'))->toBeTrue("Failed to validate required field for password_confirmation");
});

it('fails validation when email field has invalid data', function () {
    $request = new PasswordResetStoreRequest;
    $invalidDatasets = CommonValidationDataset::invalidEmailData();

    foreach ($invalidDatasets as $testName => $data) {
        $completeData = array_merge([
            'token' => 'valid-token',
            'password' => 'StrongPass123',
            'password_confirmation' => 'StrongPass123'
        ], $data);
        $request->merge($completeData);

        $validator = Validator::make($completeData, $request->rules());

        foreach (array_keys($data) as $key) {
            expect($validator->errors()->has($key))->toBeTrue("Failed to validate {$key} field for test case: {$testName}");
        }
        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
    }
});

it('fails validation when password field has invalid data', function () {
    $request = new PasswordResetStoreRequest;
    $invalidDatasets = CommonValidationDataset::invalidPasswordData();

    foreach ($invalidDatasets as $testName => $data) {
        $completeData = array_merge([
            'token' => 'valid-token',
            'email' => '<EMAIL>'
        ], $data);
        $request->merge($completeData);

        $validator = Validator::make($completeData, $request->rules());

        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
        expect(
            $validator->errors()->has('password') || $validator->errors()->has('password_confirmation')
        )->toBeTrue("Failed to validate password fields for test case: {$testName}");
    }
});

it('fails validation when token field has invalid data', function () {
    $request = new PasswordResetStoreRequest;
    $invalidDatasets = CommonValidationDataset::invalidTokenData();

    foreach ($invalidDatasets as $testName => $data) {
        $completeData = array_merge([
            'email' => '<EMAIL>',
            'password' => 'StrongPass123',
            'password_confirmation' => 'StrongPass123'
        ], $data);
        $request->merge($completeData);

        $validator = Validator::make($completeData, $request->rules());

        foreach (array_keys($data) as $key) {
            expect($validator->errors()->has($key))->toBeTrue("Failed to validate {$key} field for test case: {$testName}");
        }
        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
    }
});