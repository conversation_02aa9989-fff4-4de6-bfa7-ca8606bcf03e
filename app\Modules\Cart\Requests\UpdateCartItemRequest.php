<?php

namespace App\Modules\Cart\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Cart\Services\UpdateCartService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\Rule;

class UpdateCartItemRequest extends FormRequest
{
    use UserLoggerTrait;

    public function rules(): array
    {

        return [
            'id' => ['required', 'exists:carts,id'],
            'column' => ['required', 'string', Rule::in($this->getCartsTable())],
            'value' => ['required', 'min:1'],

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    public function update(): void
    {
        UpdateCartService::instance()->update($this->all());
    }

    private function getCartsTable()
    {
        return Schema::getColumnListing('carts');

    }
}
