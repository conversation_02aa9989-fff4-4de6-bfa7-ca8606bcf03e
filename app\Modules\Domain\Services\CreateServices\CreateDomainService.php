<?php

namespace App\Modules\Domain\Services\CreateServices;

use App\Exceptions\FailedRequestException;
use App\Modules\Cart\Constants\CartDeleteType;
use App\Modules\Cart\Services\UpdateCartService;
use App\Modules\Cart\Services\UserCart;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Notification\Services\DomainNotificationService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class CreateDomainService
{
    use UserLoggerTrait;

    private $isJob = true;

    public static function instance(): self
    {
        $createDomainService = new self;

        return $createDomainService;
    }

    public function storeDomains()
    {
        $cartContent = $this->getCartContent();
        if (! $cartContent) {
            return false;
        }

        $createdDomains = $this->getCreatedDomains($cartContent);

        $this->notifyStoreSuccess($createdDomains, 'Domains', true);
        $this->deleteCart($cartContent);

        return $createdDomains;
    }

    public function storeRegisteredDomains(Collection $unregisteredDomains, string $status)
    {
        $registeredDomains = $this->getCreatedRegisteredDomains($unregisteredDomains, $status);

        $this->notifyStoreSuccess($unregisteredDomains, 'Registered Domains', false);

        return $registeredDomains;
    }

    public function registerToEpp(array $domains, array $refundData): void
    {
        foreach ($domains as $key => $domain) {
            $registerPayload = $this->getRegisterEppDomainPayload($domain, $refundData);
            $updatePayload = $this->getUpdateEppDomainPayload($domain, DomainJobTypes::UPDATE_AFTER_REGISTER);

            JobDispatchService::instance()->registerEppDispatch($registerPayload, $updatePayload);
        }
    }

    // PRIVATE FUNCTIONS
    private function getCartContent()
    {
        $cart = new UserCart;
        $cartContent = $cart->filterDuplicateDomains();

        if ($cartContent->isEmpty()) {
            return false;
            // throw new FailedRequestException(404, 'Nothing to process.', 'Cart is empty or duplicate domains');
        }

        return $cartContent;
    }

    private function getCreatedDomains(Collection $cartContent)
    {
        $domains = new CreatedDomains($cartContent);

        return $domains->format()->store()->getDomains();
    }

    private function getCreatedRegisteredDomains(Collection $unregisteredDomains, string $status)
    {
        $registeredDomains = new CreatedRegisteredDomains($unregisteredDomains, $status);

        return $registeredDomains->format()->store()->mapByDomainId()->getDomainsArray();
    }

    private function notifyStoreSuccess(Collection $createdDomains, string $key, bool $isNotify = false): void
    {
        $domainNames = $createdDomains->pluck('name')->toArray();
        app(AuthLogger::class)->info($this->fromWho('Created '.$key.': '.implode(',', $domainNames)));

        if ($isNotify) {
            DomainNotificationService::instance()->sendBulkDomainInProcessStatusNotif($domainNames);
        }
    }

    private function deleteCart(Collection $cartContent)
    {
        UpdateCartService::instance()->softDelete([
            'id' => $cartContent->pluck('id')->toArray(),
            'delete_type' => CartDeleteType::CHECKED_OUT,
        ]);
    }

    private function getUpdateEppDomainPayload(array $domain, string $jobType)
    {
        return [
            JobPayloadKeys::DOMAIN => $domain['domain'],
            JobPayloadKeys::REGISTERED_DOMAIN => $domain['registered_domain'],
            JobPayloadKeys::REGISTRY => $domain['registry'],
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
            JobPayloadKeys::UPDATE_TYPE => $jobType,
        ];
    }

    private function getRegisterEppDomainPayload(array $domain, array $refundData)
    {
        return [
            JobPayloadKeys::DOMAIN => $domain['domain'],
            JobPayloadKeys::REGISTERED_DOMAIN => $domain['registered_domain'],
            JobPayloadKeys::REGISTRY => $domain['registry'],
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
            JobPayloadKeys::REFUND_DATA => $refundData,
        ];
    }

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        return Auth::user()->email ?? 'Unauthorized';
    }
}
