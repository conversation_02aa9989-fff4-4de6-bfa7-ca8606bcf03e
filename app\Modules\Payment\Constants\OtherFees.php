<?php

namespace App\Modules\Payment\Constants;

final class OtherFees
{
    public const DEFAULT_FEES = [
        'domain_count',
        'bill_total',
        'icann_fee',
        'year_sum',
    ];

    public const REGISTRATION_FEES = [
        'register_total',
        'register_fee',
    ];

    public const RENEWAL_FEES = [
        'renewal_total',
        'penalty_total',
        'renewal_fee',
    ];

    public const TRANSFER_FEES = [
        'transfer_total',
        'transfer_fee',
    ];

    public const REDEMPTION_FEES = [
        'redemption_total',
        'redemption_fee',
    ];

    public const ACCOUNT_BALANCE = ['bill_total'];

    public const MULTI_CHECKOUT_FEES = [
        'register_total',
        'register_fee',
        'transfer_total',
        'transfer_fee',
    ];

    public const STRIPE_FEES = [
        'net_amount',
        'gross_amount',
        'service_fee',
    ];

    public const CHECKOUT_BY_TYPE = [
        CheckoutType::RENEW => self::RENEWAL_FEES,
        CheckoutType::MULTI_CHECKOUT => self::MULTI_CHECKOUT_FEES,
        CheckoutType::TRANSFER => self::TRANSFER_FEES,
        CheckoutType::REDEMPTION => self::REDEMPTION_FEES,
        CheckoutType::MARKET_TRANSFER => self::TRANSFER_FEES,
        CheckoutType::ACCOUNT_BALANCE => self::ACCOUNT_BALANCE,
    ];

    public const CONDITIONAL_FEES = [
        'penalty_total',
    ];
}
