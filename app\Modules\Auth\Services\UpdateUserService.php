<?php

namespace App\Modules\Auth\Services;

use App\Events\ClientActivityEvent;
use App\Models\User;
use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\Histories\Constants\UserTransactionType;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class UpdateUserService
{
    public static function store($request)
    {
        $user = new User;
        $user->first_name = $request->first_name;
        $user->last_name = $request->last_name;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->street = $request->street;
        $user->city = $request->city;
        $user->state_province = $request->state_province;
        $user->country_code = $request->country_code;
        $user->postal_code = $request->postal_code;
        $user->save();

        // send notif
        AdminNotificationService::instance()->sendClientRegistrationNotif(['email' => $request->email]);

        event(new Registered($user));

        $payload = collect($request->all())->except(['password'])->toArray();

        $message = "New registration: {$user->email} ({$user->first_name} {$user->last_name}) from {$request->country_code}";
        event(new ClientActivityEvent($user->id, UserTransactionType::REGISTER, $message, '', $payload));

        return $user;
    }

    /**
     * Create data array for registration activity event
     */
    private static function createRegistrationActivityData($user, $request)
    {
        return [
            'user_id' => $user->id,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'country_code' => $request->country_code,
        ];
    }

    public static function whitelistIp($clientIp)
    {

        $whitelistedIp = DB::table('ips')->where('ip', $clientIp)->first();

        if ($whitelistedIp == null) {
            $whitelistedIp = DB::table('ips')->insertGetId(['ip' => $clientIp, 'is_active' => true, 'updated_at' => now(), 'created_at' => now()]);
        } elseif (strcmp($whitelistedIp->is_active, true) == 0) {
            $whitelistedIp = $whitelistedIp->id;
        } else {
            throw ValidationException::withMessages(['message' => $clientIp.' is blocked']);
        }

        return $whitelistedIp;
    }

    public static function setUserIp($clientEmail, $clientIp)
    {
        $user = User::where('email', $clientEmail)->first();

        if ($user == null) {
            throw ValidationException::withMessages(['message' => $clientEmail.' user does not exists']);
        }

        $clientIpId = self::whitelistIp($clientIp);

        DB::table('user_ips')->insert([
            'user_id' => $user->id,
            'ip_id' => $clientIpId,
            'updated_at' => now(),
            'created_at' => now(),
        ]);
    }
}
