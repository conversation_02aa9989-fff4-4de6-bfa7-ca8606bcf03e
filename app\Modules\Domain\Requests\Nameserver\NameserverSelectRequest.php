<?php

namespace App\Modules\Domain\Requests\Nameserver;

use Illuminate\Foundation\Http\FormRequest;

class NameserverSelectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'domains' => ['required', 'array', 'min:1'],
        ];
    }

    public function messages(): array
    {
        return [];
    }

    public function selectNameservers()
    {
        $data['domains'] = $this->domains;

        return $data;
    }
}
