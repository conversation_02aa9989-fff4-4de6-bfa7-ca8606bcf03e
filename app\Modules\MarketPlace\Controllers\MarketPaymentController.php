<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\CartCheckoutRequest;
use App\Modules\MarketPlace\Requests\MarketPaymentRequest;
use Inertia\Inertia;
use Inertia\Response;

class MarketPaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    public function showPayment(CartCheckoutRequest $request): Response
    {
        $data = $request->getPaymentDetails();

        return Inertia::render('Market/Payment/PaymentForm', $data);
    }

    public static function store(MarketPaymentRequest $request): Response
    {
        $request->store();

        return Inertia::render('Market/Notice/SuccessPayment');
    }
}
