<?php

namespace App\Modules\Auth\Controllers\AuthenticatorApp;

use App\Http\Controllers\Controller;
use App\Modules\Auth\Requests\TwoFactorAuthenticatorAppVerificationFormRequest;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppRecoveryCodeService;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\RateLimit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatorAppRecoveryCodeController extends Controller
{
    /* TRAITS */
    // ?...

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Render Page
     */
    public function renderPage(string $token): Response
    {
        if ((new AuthenticatorAppRecoveryCodeService)->verifyToken($token) == false) {
            abort(404);
        }

        return Inertia::render(
            'Auth/Login2FaVerification/Login2FaRecoveryCode',
            [
                'token' => $token,
            ]
        );
    }

    /**
     * Verify Code
     */
    public function verifyCode(TwoFactorAuthenticatorAppVerificationFormRequest $request, string $token)
    {
        $rateLimitKey = RateLimiterKey::loginAttemptAuthenticatorAppRecoveryCode($token);

        $executed = RateLimit::attempt($rateLimitKey, 5, 30);

        if (! $executed) {
            throw ValidationException::withMessages(['code' => 'Too many attempts. Wait for 30 seconds before trying again.']);
        }

        $response = (new AuthenticatorAppRecoveryCodeService)->verifyCode($request->only(['code']), $token);

        if ($response['isVerified'] == true) {
            Auth::loginUsingId($response['userId']);

            $url = URL::temporarySignedRoute(
                'login.2fa.authenticator-app.recovery-code.reset',
                now()->addMinutes(10),
                ['token' => $token]
            );

            return Inertia::location(
                $url
            );
        } else {
            if ($response['isCodeAlreadyUsed'] == true) {
                throw ValidationException::withMessages(['code' => 'Code was already used']);
            } else {
                throw ValidationException::withMessages(['code' => 'Code is invalid']);
            }
        }
    }
}
