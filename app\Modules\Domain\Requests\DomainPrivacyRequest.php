<?php

namespace App\Modules\Domain\Requests;

use App\Models\User;
use App\Modules\Domain\Services\UpdateServices\DomainPrivacyService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Http\FormRequest;

class DomainPrivacyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'privacy' => ['required', 'boolean'],
        ];
    }

    public function updatePrivacy(int $id): string
    {
        $domain = DomainPrivacyService::instance()->getDomainById($id);

        if (! $domain) {
            throw new ModelNotFoundException('Domain not found');
        }

        $request = $this->all();
        $request['domains'] = [$domain];

        return DomainPrivacyService::instance()->updatePrivacy($request);
    }
}
