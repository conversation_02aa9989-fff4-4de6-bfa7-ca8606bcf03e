import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import { updateFieldValue } from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";

export default function Filter() {
    const { orderby, status } = route().params;

    // Map URL parameters to display names
    const orderbyDisplayMap = {
        'created_at': 'Date Created',
        'domain_name': 'Domain Name',
        'total_amount': 'Total Amount'
    };

    const statusDisplayMap = {
        'all': 'All Orders',
        'paid': 'Paid',
        'not_paid': 'Not Paid'
    };

    const config = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: orderby ? [orderbyDisplayMap[orderby] || orderby] : [],
                type: "option",
                items: [
                    'Date Created',
                    'Domain Name',
                    'Total Amount',
                ],
                name: "Order By",
            },
            status: {
                active: false,
                value: status ? [statusDisplayMap[status] || status] : [],
                type: "option",
                items: [
                    'All Orders',
                    'Paid',
                    'Not Paid',
                ],
                name: "Status",
            }
        },
    };

    const [filter, setFilter] = useState(config);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, (event) => {
        if (filter.container.active) {
            setFilter({
                ...filter,
                container: { ...filter.container, active: false }
            });
        }

        const updatedField = { ...field };
        let hasChanges = false;

        Object.keys(updatedField).forEach(key => {
            if (updatedField[key].active) {
                const dropdownElement = document.querySelector(`[data-filter-key="${key}"]`);
                if (!dropdownElement?.contains(event.target)) {
                    updatedField[key].active = false;
                    hasChanges = true;
                }
            }
        });

        if (hasChanges) {
            setFilter({
                ...filter,
                field: updatedField
            });
        }
    });

    const submit = (updatedFilter) => {
        let { orderby, status } = updatedFilter.field;
        let payload = {};

        const orderbyMap = {
            'Date Created': 'created_at',
            'Domain Name': 'domain_name',
            'Total Amount': 'total_amount'
        };

        const statusMap = {
            'All Orders': 'all',
            'Paid': 'paid',
            'Not Paid': 'not_paid'
        };

        if (orderby.value.length > 0) {
            payload.orderby = orderbyMap[orderby.value[0]] || orderby.value[0];
        }

        if (status.value.length > 0) {
            const statusValue = statusMap[status.value[0]] || status.value[0];
            if (statusValue !== 'all') {
                payload.status = statusValue;
            }
        }

        router.get(route('domain-redemption'), payload);
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value) => {
        const newValue = updateFieldValue(value, { ...filter.field[key] });
        let reload = true;

        if (newValue.value.length == 0 && value != "") reload = false;

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false, reload },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        };

        setFilter(updatedFilter);

        if (reload) {
            submit(updatedFilter);
        }
    };

    return (
        <div className="flex items-center" ref={ref}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <div className="relative">
                    <OptionFilter
                        fieldProp={field.orderby}
                        fieldKey="orderby"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        data-filter-key="orderby"
                    />
                    <OptionFilter
                        fieldProp={field.status}
                        fieldKey="status"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        data-filter-key="status"
                    />
                </div>
            </div>
        </div>
    );
}
