<?php

namespace App\Modules\AdminNotification\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminNotificationHandler
{
    private array $notificationPayload = [];

    private Carbon $timestamp;

    private function __construct()
    {
        $this->timestamp = Carbon::now();
    }

    public static function Create(): AdminNotificationHandler
    {
        return new AdminNotificationHandler;
    }

    public function store(): AdminNotificationHandler
    {
        if (count($this->notificationPayload) == 0) return $this;

        DB::table('admin_notifications')->insert($this->notificationPayload);

        $this->notificationPayload = [];

        return $this;
    }

    public function addPayload(string $adminId, string $title, string $message, string $route, string $importance): AdminNotificationHandler
    {
        $payload = [
            'admin_id' => $adminId,
            'title' => $title,
            'message' => $message,
            'redirect_url' => $route,
            'importance' => $importance,
            'created_at' => $this->timestamp,
            'updated_at' => $this->timestamp,
        ];

        $this->notificationPayload[] = $payload;

        return $this;
    }
}
