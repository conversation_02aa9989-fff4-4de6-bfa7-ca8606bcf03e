<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Domain extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'status',
        'root',
        'registrant',
        'year_length',
        'expiry',
        'transferredIn',
        'transferredOut',
        'contacts',
        'client_status',
        'hosts',
        'nameservers',
    ];
}
