<?php

namespace App\Modules\Client\Jobs;

use App\Events\SystemLogEvent;
use App\Modules\Client\Constants\ScheduleType;
use App\Modules\Client\Constants\SourceType;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\ExpirationNoticeService;
use App\Modules\Histories\Constants\SystemTransactionType;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ScheduleDomainExpiryNotice implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $userId;

    public string $type = ScheduleType::ALL;

    private string $source = SourceType::UPDATE_DOMAIN;

    public $failOnTimeout = true;

    public $uniqueFor = 60;

    public function __construct(int $userId, string $source = SourceType::UPDATE_DOMAIN, string $type = ScheduleType::ALL)
    {
        $this->userId = $userId;
        $this->type = $type;
        $this->source = $source;

        $this->onConnection(QueueConnection::DOMAIN_SCHEDULE_EXPIRY);
        $this->onQueue(QueueTypes::DEFAULT);
    }

    public function uniqueId(): int
    {
        return $this->userId;
    }

    public function handle(): void
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            app(AuthLogger::class)->error('ScheduleDomainExpiryNotice: '.$e->getMessage());
            $this->fail();
        }
    }

    // PRIVATE FUNCTIONS

    private function evaluate(): void
    {
        app(AuthLogger::class)->info('ScheduleDomainExpiryNotice: running...');

        if ($this->source !== SourceType::UPDATE_DOMAIN) {
            $this->evaluateScheduler();

            return;
        }

        $this->scheduleAllNotices();
        app(AuthLogger::class)->info('ScheduleDomainExpiryNotice: done...');
    }

    private function evaluateScheduler(): void
    {
        $domains = $this->getDomainList();
        // app(AuthLogger::class)->info('evaluateScheduler: '.json_encode($domains));
        if ($domains->isEmpty()) {
            $this->resetSchedule();

            return;
        }

        $this->scheduleNotice($domains);
    }

    private function scheduleAllNotices(): void
    {
        foreach (ScheduleType::GET_ALL as $type) {
            $this->type = $type;
            // app(AuthLogger::class)->info('scheduleAllNotices: '.json_encode($this->type));
            $this->evaluateScheduler();
        }
    }

    private function getDomainList(): Collection
    {
        try {
            $latestExpiry = $this->getLatestExpiry();
            // app(AuthLogger::class)->info('getDomainList: '.json_encode($latestExpiry));

            return ExpirationNoticeService::getList(
                $this->userId,
                $latestExpiry,
                DomainStatus::ACTIVE,
                false
            );
        } catch (Exception $e) {
            app(AuthLogger::class)->error('ScheduleDomainExpiryNotice getDomainList: '.$e->getMessage());
            throw new Exception($e->getMessage());
        }
    }

    private function scheduleNotice(Collection $domains): void
    {
        switch ($this->type) {
            case ScheduleType::FIRST:
                $this->updateSchedule($domains, ScheduleType::FIRST);
                break;
            case ScheduleType::SECOND:
                $this->updateSchedule($domains, ScheduleType::SECOND);
                break;
            case ScheduleType::THIRD:
                $this->updateSchedule($domains, ScheduleType::THIRD);
                break;
            case ScheduleType::ALL:
            default:
                $this->updateSchedule($domains, ScheduleType::FIRST);
                $this->updateSchedule($domains, ScheduleType::SECOND);
                $this->updateSchedule($domains, ScheduleType::THIRD);
                break;
        }
    }

    private function getLatestExpiry(): string
    {
        $latestExpiry = 0;
        if ($this->source === SourceType::SEND_MAIL) {
            $latestExpiry = $this->getLeadExpiryAfterEmailSent();
        }
        // app(AuthLogger::class)->info('getLatestExpiry: '.json_encode($latestExpiry));

        if (
            ($latestExpiry !== 0) &&
            ! $this->checkIfLeadExpiryPassed($latestExpiry)
        ) {
            return (string) $latestExpiry;
        }

        return $this->getNewDomainListLeadExpiry();
    }

    private function getNewDomainListLeadExpiry(): string
    {
        $latestExpiry = $this->getExpectedLeadExpiry();

        app(AuthLogger::class)->info('getExpectedLeadExpiry: done');

        $domains = ExpirationNoticeService::getList(
            $this->userId,
            $latestExpiry,
            DomainStatus::ACTIVE,
            false,
        );

        if ($domains->isEmpty()) {
            // app(AuthLogger::class)->info('getLatestExpiry: '.json_encode($domains));
            $this->resetSchedule();
            throw new Exception('No domains found');
        }

        // app(AuthLogger::class)->info('getLatestExpiry: '.json_encode($domains->first()->expiry));

        return (string) Carbon::createFromTimestampMs($domains->first()->expiry);
    }

    private function resetSchedule()
    {
        $domainExpirationNotification = [
            'expected_notification_at' => now()->addYears(10),
            'record' => '[]',
            'lead_expiry_date' => now()->addYears(10),
            'type' => $this->type,
            'updated_at' => now(),
        ];

        $this->updateScheduleOnDB($domainExpirationNotification);
    }

    private function getLeadExpiryAfterEmailSent()
    {
        $latestExpiry = $this->getSavedLeadExpiry();

        if ($latestExpiry === 0) {
            return $latestExpiry;
        }

        $nextLeadExpiry = new Carbon($latestExpiry);

        return $nextLeadExpiry->addDays(ScheduleType::MAX_LAPSE_IN_DAYS);
    }

    private function getSavedLeadExpiry(): mixed
    {
        return DB::table('domain_expiration_notifications')
            ->where('user_id', $this->userId)
            ->where('type', $this->type)
            ->first()->lead_expiry_date ?? 0;
    }

    private function checkIfLeadExpiryPassed($leadExpiryDate): bool
    {
        $date = new Carbon($leadExpiryDate);

        // app(AuthLogger::class)->info('checkIfLeadExpiryPassed: '.json_encode($date));

        if ($this->type === ScheduleType::THIRD) {
            return false;
        } else {
            return $date->isPast();
        }
    }

    private function isSendDatePassed($leadExpiryDate): bool
    {
        $sendDate = $this->getExpectedSendDate($leadExpiryDate, $this->type);
        // app(AuthLogger::class)->info('isSendDatePassed :'.json_encode($sendDate));

        return $sendDate->isPast();
    }

    private function updateSchedule(Collection $domains, string $type): void
    {

        $domainExpirationNotification = $this->getDefaultSchedule();
        $latestExpiry = Carbon::createFromTimestampMs($domains->first()->expiry);

        app(AuthLogger::class)->info('ScheduleDomainExpiryNotice: found '.$domains->count().' for user with id '.$this->userId);

        $expectedNotice = $this->getExpectedSendDate($latestExpiry, $type);
        $domainExpirationNotification['lead_expiry_date'] = $latestExpiry;
        $domainExpirationNotification['record'] = $domains->toJson();
        $domainExpirationNotification['expected_notification_at'] = $expectedNotice;
        $domainExpirationNotification['type'] = $type;

        // app(AuthLogger::class)->info('ScheduleDomainExpiryNotice: '.json_encode($expectedNotice));

        $this->updateScheduleOnDB($domainExpirationNotification);

        // Get first few domain names for the log message
        $domainNames = $domains->take(3)->pluck('name')->join(', ');
        if ($domains->count() > 3) {
            $domainNames .= ' and '.($domains->count() - 3).' more';
        }

        event(new SystemLogEvent(
            SystemTransactionType::DOMAIN_EXPIRY_EVALUATOR,
            "Scheduled {$type} notice for domains: {$domainNames}, due {$expectedNotice->format('Y-m-d')}",
            null
        ));
    }

    private function updateScheduleOnDB($domainExpirationNotification)
    {
        $affected = DB::table('domain_expiration_notifications')
            ->where('user_id', $this->userId)
            ->where('type', $domainExpirationNotification['type'])
            ->update($domainExpirationNotification);

        if (! $affected) {
            $domainExpirationNotification['user_id'] = $this->userId;
            $domainExpirationNotification['created_at'] = now();
            DB::table('domain_expiration_notifications')->insert($domainExpirationNotification);
        }
    }

    private function getExpectedSendDate(string $date, string $type): Carbon
    {
        $expiry = new Carbon($date);
        $days = $this->getLapseDays($type);
        switch ($type) {
            case ScheduleType::THIRD:
                return $expiry->addDays($days);
            case ScheduleType::FIRST:
            case ScheduleType::SECOND:
            default:
                return $expiry->subDays($days);
        }
    }

    private function getLapseDays($type): int
    {
        $days = 0;
        switch ($type) {
            case ScheduleType::SECOND:
                $days = ScheduleType::SECOND_COUNT;
                break;
            case ScheduleType::THIRD:
                $days = ScheduleType::THIRD_COUNT;
                break;
            case ScheduleType::FIRST:
            default:
                $days = ScheduleType::FIRST_COUNT;
                break;
        }

        return $days;
    }

    private function getExpectedLeadExpiry(): Carbon
    {
        $sendDate = now();
        $days = $this->getLapseDays($this->type);

        // app(AuthLogger::class)->info('getExpectedLeadExpiry: '.json_encode($sendDate));

        switch ($this->type) {
            case ScheduleType::THIRD:
                return $sendDate->subDays($days);
            case ScheduleType::FIRST:
            case ScheduleType::SECOND:
            default:
                return $sendDate->addDays($days);
        }
    }

    private function getDefaultSchedule(): array
    {
        return [
            'expected_notification_at' => now()->addYears(10),
            'record' => '[]',
            'lead_expiry_date' => now()->addYears(10),
            'type' => ScheduleType::ALL,
            'updated_at' => now(),
        ];
    }
}
