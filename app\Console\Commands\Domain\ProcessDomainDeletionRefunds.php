<?php

namespace App\Console\Commands\Domain;

use Illuminate\Console\Command;
use App\Modules\Domain\Services\DomainDeletionService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\PaymentService\Constants\PaymentServiceType;

class ProcessDomainDeletionRefunds extends Command
{
    protected $signature = 'domain:deletion-refund';

    protected $description = 'Process refunds for all approved domain deletions';

    public function handle()
    {
        $this->info('Starting domain deletion refund process...');

        $deletions = DomainDeletionService::instance()->query()
            ->where('support_agent_id', '>', 0)
            ->where('is_refunded', false)
            ->get();


        if ($deletions->isEmpty()) {
            $this->info('No eligible refunds to process.');
            return Command::SUCCESS;
        }

        foreach ($deletions as $deletion) {
            try {
                $registeredDomainId = $deletion->domain_id;
                $userId = $deletion->user_id;
                // $transactionType = 'refund'; // or set a constant/enum
                $transactionType = FeeType::TRANSACTION_TYPE[FeeType::REGISTRATION];
                // $description = 'Refund for approved domain deletion';
                $description = DomainStatus::EXPIRED;

                PaymentReimbursementService::instance()->refundByRegisteredDomain(
                    $registeredDomainId,
                    $userId,
                    $transactionType,
                    $description,
                    PaymentServiceType::ACCOUNT_CREDIT,
                    true, // false if to source, true if to refundDestination
                    false, // false if user-event, true if failed transaction
                );

                DB::table('domain_cancellation_requests')
                    ->where('domain_id', $registeredDomainId)
                    ->update([
                        'is_refunded' => true,
                        'refunded_at' => now(),
                    ]);

                $this->info("Refunded domain ID: {$registeredDomainId}");
            } catch (\Exception $e) {
                Log::error("Refund failed for domain ID {$deletion->domain_id}: " . $e->getMessage());
                $this->error("Failed to refund domain ID: {$deletion->domain_id}");
            }
        }

        return Command::SUCCESS;
    }
}
