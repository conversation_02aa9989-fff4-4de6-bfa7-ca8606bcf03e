<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentNode extends Model
{
    use HasFactory;

    protected $fillable = [
        'registered_domain_id',
        'extension_fee_id',
        'year_length',
        'rate',
        'total_icann_fee',
        'total_domain_amount',
        'redemption_fee',
        'total_amount',
        'status',
    ];
}
