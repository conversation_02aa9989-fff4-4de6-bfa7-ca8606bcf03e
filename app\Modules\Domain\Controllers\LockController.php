<?php

namespace App\Modules\Domain\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Domain\Requests\Lock\LockConfirmDomainRequest;
use App\Modules\Domain\Requests\Lock\LockSetDomainRequest;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class LockController extends Controller
{
    public function lock(LockSetDomainRequest $request): Response
    {
        $message = $request->setLock();

        return Inertia::render('Notice/ConfirmationMessage', ['message' => $message]);
    }

    public function checkAuthLock(LockSetDomainRequest $request): JsonResponse
    {
        $request->checkAuthLock();

        return response()->json(['success' => true], 200);
    }

    public function lockConfirm(LockConfirmDomainRequest $request): Response
    {
        $data = $request->getData();

        return Inertia::render('Domain/Lock', $data);
    }
}
