<?php

namespace App\Models;

use App\Util\Constant\CryptPrefixes;
use App\Util\Helper\CryptHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentSummary extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'user_id',
        'type',
        'paid_amount',
        'total_amount',
        'payment_invoice_id',
        'payment_market_place_invoice_id',
        'payment_service_id',
        'source',
        'transaction_id',
    ];

    protected static function booted()
    {
        static::creating(function ($model) {
            $model->transaction_id = CryptHelper::encrypt(CryptHelper::generate(CryptPrefixes::PAYMENT_SUMMARY));
        });
    }
}
