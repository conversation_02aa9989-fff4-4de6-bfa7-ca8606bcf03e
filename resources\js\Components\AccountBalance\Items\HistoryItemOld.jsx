import React from 'react';
import setDefaultDateFormat from '../../../Util/setDefaultDateFormat';

export const HistoryItemOld = ({ item }) => {

    console.log(item);

    // const transactionItems = [
    //     {
    //         name: 'Fund Added - Debit',
    //         amount: 100,
    //         created_at: "2025-01-30 09:59:03",
    //         status: 'Pending',
    //     },
    //     {
    //         name: 'Fund Added - Debit',
    //         amount: 50,
    //         created_at: "2025-01-30 09:59:03",
    //         status: 'Pending',
    //     },
    // ];

    return (
        <>
            <div className="mx-auto container max-w-[400px] flex flex-col space-y-2">
                <div className="flex items-center justify-between">
                    <span className="flex items-center space-x-2 text-xl">
                        {(item.type == 'DEBIT') ? 'Fund Added - Debit' :
                            (item.type == 'CREDIT') ? 'Fund Used - Credit' : 'Initial Transaction'}
                    </span>
                    <span className="flex items-center space-x-2 text-xl">
                        {(item.type == 'CREDIT') ? '-' : ''}
                        ${parseFloat(item.amount).toFixed(2)}
                    </span>
                </div>
                <div className="border-b border-gray-200">
                    <span className="flex items-center space-x-2">
                        <span className='text-gray-700'>{new Date(item.created_at * 1000).toLocaleString()}</span>
                    </span>
                </div>
            </div>
        </>

    )
}
