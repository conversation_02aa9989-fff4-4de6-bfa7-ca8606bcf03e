<?php

namespace App\Modules\BankTransfer\Requests;

use App\Modules\BankTransfer\Services\BankTransferService;
use App\Rules\AccountCreditMinimumDepositRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreWireTransferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'amount' => ['required', 'numeric', 'decimal:0,2', 'min:1', 'max:50000'],
            'name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s]+$/'],
            'company' => ['required', 'string', 'max:50', 'regex:/^[a-zA-Z0-9\s\'\-\&\.]+$/'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.regex' => 'The name should only contain letters and spaces.',
            'company.regex' => 'The company name can only contain letters, numbers, spaces, and basic punctuation (. - & \').',
            'amount.decimal' => 'The amount must have 2 or fewer decimal places.',
        ];
    }

    public function store()
    {
        BankTransferService::instance()->requestStore($this->all());
    }
}
