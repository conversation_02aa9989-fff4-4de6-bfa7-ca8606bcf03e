<?php

namespace App\Modules\Domain\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

class EppDomainService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $eppDomainService = new self;

        return $eppDomainService;
    }

    public function callCreate(object $domain, ?string $email = null): array
    {
        $name = strtolower($domain->name);
        $registry = DomainParser::getRegistryName($name);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');
        if (is_null($registry)) {
            return $error;
        }

        $payload = $this->castToCreatePayload($domain);

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.base'),
                $payload
            );
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public function updateEppDomain(array $payload, string $email): array
    {
        $payload['name'] = strtolower($payload['name']);
        $registry = DomainParser::getRegistryName($payload['name']);

        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) {
            return $error;
        }

        try {
            $request = Http::registry($registry)->put(Config::get('domain.base'), $payload);
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public function callEppMultipleCheck(array $domains_to_check, string $registry): array
    {
        if (empty($domains_to_check[$registry])) {
            return [];
        }

        try {
            $payload = ['names' => $domains_to_check[$registry]];
            $request = Http::registry($registry)->post(Config::get('domain.checks'), $payload);
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public function renewDomain(object $domain, string $email = 'Unauthorized'): array
    {
        $name = strtolower($domain->name);
        $registry = DomainParser::getRegistryName($name);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) {
            return $error;
        }

        try {
            $payload = $this->castToEppRenewPayload($domain);
            $request = Http::registry($registry)->post(Config::get('domain.renew'), $payload);
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public function databaseSyncExpiry(object $domain, string $email = 'Unauthorized'): array
    {
        $name = strtolower($domain->name);
        $registry = DomainParser::getRegistryName($name);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) {
            return $error;
        }

        try {
            $payload = ['name' => $domain->name];
            $request = Http::registry($registry)->post(Config::get('domain.database_sync_expiry'), $payload);
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public function callEppDomainCheck(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');
        if (is_null($registry)) {
            return $error;
        }

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.check'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callEppDomainInfo(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) {
            return $error;
        }

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.info'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callDatastoreDomainInfo(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) {
            return $error;
        }

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.database_info'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callEppDomainBatchAuthRequest($domains, $registry, $email)
    {
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');
        if (is_null($registry)) {
            return $error;
        }

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.auth_requests'),
                $domains
            );

            return $request->json();
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callEppDomainRestoreRequest(string $domain): array
    {
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return $error;
        }

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.restore_request'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callEppDomainRestoreReport(array $payload): array
    {
        $registry = DomainParser::getRegistryName($payload['name']);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.restore_report'),
                $payload
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }
    
    public function callEppInfo(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.epp_info'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callEppRestoreAccepted(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::registry($registry)->post(
                Config::get('domain.restore_accepted'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    // PRIVATE FUNCTIONS

    private function castToCreatePayload(object $domain): array
    {
        $payload = [
            'name' => $domain->name,
            'registrant' => $domain->registrant,
            'yearLength' => $domain->year_length,
        ];

        if ($domain->contacts) {
            $payload['contacts'] = json_decode(stripslashes($domain->contacts), true, JSON_UNESCAPED_SLASHES);
        }

        if ($domain->nameservers) {
            $payload['nameservers'] = json_decode(stripslashes($domain->nameservers), true, JSON_UNESCAPED_SLASHES);
        }

        return $payload;
    }

    private function castToEppRenewPayload(object $domain): array
    {
        $payload = [
            'name' => $domain->name,
            'registrant' => $domain->registrant,
            'yearLength' => $domain->year_length,
        ];

        return $payload;
    }

}
