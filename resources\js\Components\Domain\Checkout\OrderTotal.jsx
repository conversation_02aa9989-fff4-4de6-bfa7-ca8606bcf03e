import React from 'react';

export default function OrderTotal({ total = 0 }) {
    return (
        <div className="text-lg flex items-center justify-between mb-6">
            <span className="font-semibold text-gray-800">
                Order Total
            </span>
            <span className="font-semibold text-gray-800">
                ${Number(parseFloat(total).toFixed(2)).toLocaleString('en', { useGrouping: true, minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
        </div>
    );
}