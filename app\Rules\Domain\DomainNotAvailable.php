<?php

namespace App\Rules\Domain;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class DomainNotAvailable implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->isInRegisteredDomains($value)) {
            $fail($this->message($value));
            return;
        }
    }

    public function passes($attribute, $value)
    {
        return !$this->isInRegisteredDomains($value);
    }

    public function message($value)
    {
        if (is_string($value)) {
            return $value.' is already registered under StrangeDomains.';
        } else {
            return 'Some domains are already registered under StrangeDomains.';
        }
    }

    // PRIVATE FUNCTIONS

    private function isInRegisteredDomains(string|array $value): bool
    {
        return DB::table('domains')
            ->when(is_array($value), function ($query) use ($value) {
                return $query->whereIn('name', $value);
            })
            ->when(is_string($value), function ($query) use ($value) {
                return $query->where('name', $value);
            })
            ->whereNull('deleted_at')
            ->exists();
    }
}
