<?php

namespace App\Rules\Domain;

use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DomainOwnedArrayExists implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $exists = $this->domainIdExists($value);

        if (! $exists) {
            $fail('Items are not valid.');
        }
    }

    private function domainIdExists(array $domains): bool
    {
        if (empty($domains)) {
            return false;
        }

        $cartObj = collect($domains);
        $ids = $cartObj->pluck('id')->toArray();
        $fortyDaysAgo = Carbon::now()->subDays(40)->timestamp * 1000;

        $existingCount = DB::table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->where('user_contacts.user_id', Auth::user()->id)
            ->where('registered_domains.status', UserDomainStatus::OWNED)
            ->whereIn('domains.status', [DomainStatus::ACTIVE, DomainStatus::EXPIRED])
            ->where('domains.expiry', '>', $fortyDaysAgo)
            ->whereNull('domains.deleted_at')
            ->whereIn('domains.id', $ids)
            ->count();

        if ($existingCount !== count($ids)) {
            return false;
        }

        return true;
    }
}
