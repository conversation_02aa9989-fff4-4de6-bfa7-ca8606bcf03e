<?php

namespace App\Mail;

use App\Mail\Constants\Links;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Setting\Constants\FeeType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\ThrottlesExceptions;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class RefundSuccess extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $transaction;

    private $domain;

    private $refunded_amount;

    private $other_data;

    private $policyUrl;

    private $refundUrl;

    private $backOffMinutes = 5;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public $uniqueFor = 120; // 2 minutes

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->domain = $data['payload']['domain'];
        $this->refunded_amount = $data['payload']['refunded_amount'];
        $this->other_data = $data['payload']['other_data'];
        $this->policyUrl = config('app.url') . Links::REFUND_POLICY;
        $this->refundUrl = config('app.url') . Links::PAYMENT_REFUND_VIEW . '/' . $data['payload']['other_data']['summaryId'];

        switch ($data['payload']['refund_type']) {
            case FeeType::REGISTRATION:
                $this->transaction = 'domain registration';
                break;
            case FeeType::RENEW:
                $this->transaction = 'domain renewal';
                break;
            case FeeType::TRANSFER:
                $this->transaction = 'domain transfer';
                break;
        }
    }

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: ucwords($this->transaction) . ' Refund'
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $message = 'We are pleased to inform you that the refund process for the ' . $this->transaction . ' of "' . $this->domain . '" has been initiated. The refunded amount totals $' . $this->refunded_amount . '.';
        $text = "It can take approximately 10 days to appear on your statement. If it takes longer, please contact your bank for assistance. For other questions or concerns, please don't hesitate to reach out.";

        return new Content(
            markdown: 'Mails.RefundSuccess',
            with: [
                'greeting' => 'Greetings!',
                'body' => $message,
                'text' => $text,
                'sender' => config('mail.from.sd_name'),
                'domain' => $this->domain,
                'refunded_amount' => $this->refunded_amount,
                'data' => $this->other_data,
                'policyUrl' => $this->policyUrl,
                'refundUrl' => $this->refundUrl,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    private function failed($e)
    {
        app(AuthLogger::class)->error('RefundSuccess: ' . $e->getMessage());
        $this->fail();
    }
}
