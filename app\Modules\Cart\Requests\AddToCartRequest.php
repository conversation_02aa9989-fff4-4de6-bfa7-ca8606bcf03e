<?php

namespace App\Modules\Cart\Requests;

use App\Events\MyCartCountEvent;
use App\Modules\Cart\Services\AddToCartService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Rules\Cart\CartNotAvailable;
use App\Rules\Domain\DomainNameFormat;
use App\Rules\Domain\DomainNotAvailable;
use App\Rules\InvalidExtension;
use App\Util\Helper\Domain\DomainTld;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\MessageBag;
use Illuminate\Validation\Rule;

class AddToCartRequest extends FormRequest
{
    private $tlds;

    public function __construct()
    {
        $this->tlds = DomainTld::getTldsByExtension();
    }

    public function rules(): array
    {
        return [
            '*.name' => [
                'bail',
                'required',
                'string',
                'distinct',
                'max:30',
                new InvalidExtension($this->tlds),
                new DomainNameFormat,
                new DomainNotAvailable,
                new CartNotAvailable,
            ],
            '*.extension' => ['required', 'string', 'max:30', Rule::in(array_keys($this->tlds))],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new HttpResponseException(response()->json([
            'success' => false,
            'message' => $validator->errors()->first(),
            'errors' => $validator->errors(),
        ], 422));
    }

    public function store()
    {
        AddToCartService::instance()->store($this->all());
        MyCartCountEvent::dispatch(Auth::user()->id);

        return response('stored', 201);
    }

    // PRIVATE FUNCTIONS

    private function insertErrors(MessageBag $errors): array
    {
        $data = $this->input('data', []);

        foreach ($errors->keys() as $key) {
            $index = preg_replace('/^[^.]*\.([^.]+)\..*$/', '$1', $key);
            $data[$index]['error_message'] = $errors->get($key);
        }

        return $data;
    }
}
