<?php

namespace App\Modules\Cart\Services;

use App\Exceptions\FailedRequestException;
use App\Exceptions\InsufficientBalanceException;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\CreateServices\CreateDomainService;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Services\StripeLimiter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class CheckoutCartService
{
    private $dispatchDelayInSeconds = 180; // three minutes

    public static function instance(): self
    {
        $checkoutCartService = new self;

        return $checkoutCartService;
    }

    public function getIndexData(): array
    {
        $data = CartService::instance()->getMyCart();
        $other_fees = $this->getOtherFees($data);
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($other_fees['bill_total'] ?? 0);
        $setupIntents = $this->setPaymentDetails($stripeFeeObj['gross_amount'] ?? $other_fees['bill_total']);
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        $data['other_fees'] = $other_fees;
        $data['secret'] = $setupIntents->client_secret;
        $data['intent'] = $setupIntents->id;
        $data['promise'] = Config::get('stripe.publishable_key');
        $data['account_credit_balance'] = $accountCredit->running_balance ?? 0;
        $data['stripeFeeObj'] = $stripeFeeObj;

        $this->checkRegistryBalance($data, FeeType::REGISTRATION);

        return $data;
    }

    public function store(array $request): string
    {
        StripeLimiter::instance()->clearAttempt();

        $registryId_balance = $this->checkRegistryBalance($request, FeeType::REGISTRATION);
        $this->creditRegistryAccountBalance($registryId_balance);

        $createdDomains = $this->createRegisteredDomains();
        if (! $createdDomains) {
            return false;
        }

        $paymentPayload = $this->createPaymentPayload($request, $createdDomains); // MULTI CHECKOUT createPaymentSummary

        $invoiceId = PaymentSummaryService::instance()->createPayment($paymentPayload, $this->getUserId(), $request['payment_summary_type'], $request['payment_service_type']);

        $refundDetails = PaymentReimbursementService::instance()->createRefundDetails(PaymentSummaryType::PAYMENT_REIMBURSEMENT, $request['payment_service_type'], $invoiceId);
        CreateDomainService::instance()->registerToEpp($createdDomains['domains'], $refundDetails);
        ScheduleDomainExpiryNotice::dispatch($this->getUserId())->delay($this->dispatchDelayInSeconds);

        return $invoiceId;
    }

    public function checkAccountCreditBalance(string $amount): void
    {
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        if ($accountCredit->running_balance < $amount) {

            throw new InsufficientBalanceException(
                403,
                'Your account credit balance is insufficient to complete this transaction. Please add funds or adjust your cart.',
                'Insufficient Account Credit Balance',
                route('domain.mycart'),
                'Go to My Cart'
            );
        }
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getOtherFees(array $data): array
    {
        $register_fees = $data['settings']['registration_fees'];

        return PaymentFeeService::getOtherRegisterFees($data['domains'], $register_fees);
    }

    private function setPaymentDetails(float $bill_total): object
    {
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($bill_total);

        return PaymentIntentProvider::instance()->create($payment);
    }

    private function checkRegistryBalance(array $data, string $type): array
    {
        return RegistryAccountBalanceService::checkRegistryBalance($data['domains'], $data['other_fees'], $type);
    }

    private function creditRegistryAccountBalance(array $registryBalance): void
    {
        foreach ($registryBalance as $balance) {
            RegistryAccountBalanceService::credit($balance['balance'], $balance['amount'], RegistryTransactionType::SUB_FUND, RegistryTransactionType::DOMAIN_REGISTRATION);
        }
    }

    private function createRegisteredDomains()
    {
        $insertedDomains = CreateDomainService::instance()->storeDomains();

        if ($insertedDomains->isEmpty()) {
            throw new FailedRequestException(404, 'Nothing to process.', 'Cart is empty or duplicate domains');
        }

        return CreateDomainService::instance()->storeRegisteredDomains($insertedDomains, UserDomainStatus::OWNED);
    }

    private function createPaymentPayload(array $request, array $createdDomains): array
    {
        $invoice = PaymentInvoiceService::instance()->createInvoicePayload(FeeType::REGISTRATION, $this->getUserId(), $request['other_fees'], $request['intent'] ?? null);
        $nodeInvoice = PaymentInvoiceService::instance()->createNodeInvoicePayload(FeeType::REGISTRATION, $createdDomains['registered_domains'], $request['other_fees']);

        return PaymentInvoiceService::instance()->createPaymentPayload($invoice, $nodeInvoice);
    }
}
