<?php

namespace App\Exceptions;

use Exception;
use Inertia\Inertia;

class RegistrationThresholdException extends Exception
{
    private $customRoute;
    private $customLabel;

    public function __construct($code, $help, $error, $customRoute = null, $customLabel = null)
    {
        $this->code = $code;
        $this->message = $help;
        $this->file = $error;
        $this->customRoute = $customRoute ?? route('home');
        $this->customLabel = $customLabel ?? 'Home';
    }

    public function render()
    {
        return Inertia::render('Errors/CustomMessage', [
            'code' => $this->code,
            'help' => $this->message,
            'error' => $this->file,
            'customLink' => ['route' => $this->customRoute, 'label' => $this->customLabel],
        ]);
    }
}
