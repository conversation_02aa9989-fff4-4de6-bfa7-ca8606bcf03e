<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class UserEmailUpdateVerificationCode extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable =
    [
        'user_id',
        'email',
        'code',
        'valid_until',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden =
    [
        //?
    ];

    //** Accessors & Mutators */

    /**
     * Interact with the code column.
     */
    protected function code(): Attribute
    {
        return Attribute::make(
            set: fn(mixed $value) => Hash::make($value)
        );
    }

    //** belongsTo, belongsToMany, hasOne, hasMany relationships */ 

    /**
     * Fetch user belongsTo relationship. 
     * 
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(
            related: User::class,
            foreignKey: "user_id",
            ownerKey: "id",
        );
    }
}
