<?php

namespace App\Mail;

use App\Mail\Constants\Links;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\ThrottlesExceptions;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use App\Mail\Constants\MailDetails;

class OutboundRequestMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    private $userId;
    private $email;
    private $greeting;
    private $name;
    private $domain_name;
    private $termsUrl;
    private $supportUrl;
    private $request_date;
    private $sender_email;
    private $sender_name;
    private $redirectUrl;
    private $phone;


    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->email = $data['email'];
        $this->userId = $data['user_id'];
        $this->name = $data['name'];
        $this->greeting = 'Dear '.$this->name.',';
        $this->domain_name = $data['domain_name'];
        $this->request_date = now()->toDateTimeString();
        $this->termsUrl = config('app.url').Links::TERMS_AND_CONDITIONS;
        $this->supportUrl = config('app.url').Links::CONTACT_US_PAGE;
        $this->sender_email = config('mail.from.address');
        $this->sender_name = config('mail.from.sd_name');
        $this->redirectUrl = config('app.url').'/transfer/outbound';
        $this->phone = MailDetails::PHONE_NUMBER;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: 'Domain Transfer Request Initiated',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.OutboundRequest',
            with: [
                'greeting' => $this->greeting,
                'name' => $this->name,
                'domain_name' => $this->domain_name,
                'request_date' => $this->request_date,
                'termsUrl' => $this->termsUrl,
                'supportUrl' => $this->supportUrl,
                'senderName' => config('mail.from.sd_name'),
                'redirectUrl' => $this->redirectUrl,
                'phoneNumber' => $this->phone,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    // PRIVATE FUNCTIONS

}
