import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import { offFilter, updateFieldValue } from "@/Components/Util/Filter/FilterMethod";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import { useRef, useState } from "react";
import useOutsideClick from "@/Util/useOutsideClick";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { router } from "@inertiajs/react";



export default function Filter({ filter, setFilter }) {

    const ref = useRef();
    const { field } = filter;

    const excludedFilters = Object.keys(filter.field);
    const getOtherParam = () => {
        return Object.fromEntries(
            Object.entries(route().params).filter(([key]) => !excludedFilters.includes(key))
        );
    }


    useOutsideClick(ref, (event) => {
        if (filter.container.active) {
            setFilter({
                ...filter,
                container: { ...filter.container, active: false }
            });
        }

        const updatedField = { ...field };
        let hasChanges = false;

        Object.keys(updatedField).forEach(key => {
            if (updatedField[key].active) {
                const dropdownElement = document.querySelector(`[data-filter-key="${key}"]`);
                if (!dropdownElement?.contains(event.target)) {
                    updatedField[key].active = false;
                    hasChanges = true;
                }
            }
        });

        if (hasChanges) {
            setFilter({
                ...filter,
                field: updatedField
            });
        }
    });

    const handleSubmit = (updatedFilter = filter) => {
        const filtered = offFilter(updatedFilter);
        setFilter({ ...filtered });

        if (!filtered.container.reload) return;

        toast.info("Reloading data, please wait...");
        filtered.container.reload = false;
        submit({ ...filtered });

    };

    const submit = (updatedFilter) => {
        let filterFields = Object.keys(filter.field);
        let payload = getOtherParam()

        filterFields.forEach(element => {
            if (updatedFilter.field[element].value.length > 0) {
                payload[element] = updatedFilter.field[element].value[0];
            }
        });

        router.get(route(route().current(), payload));
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const reload = forceReload || !(newValue.value.length === 0 && value !== "");
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false, reload: reload },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        };
        setFilter(updatedFilter);

        if (reload) {
            handleSubmit(updatedFilter);
        }
    };

    const handleDisplayToggle = (newObject) => {
        const updatedFilter = { ...filter, ...newObject };
        setFilter(updatedFilter);

        if (updatedFilter.container.reload) {
            handleSubmit(updatedFilter);
        }
    };

    return (
        <div className="flex flex-wrap">
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div ref={ref}>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                    exclude={"name"}
                />
                <div className="relative">
                    <OptionFilter
                        fieldProp={field.orderby}
                        fieldKey="orderby"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        data-filter-key="orderby"
                    />
                    <OptionFilter
                        fieldProp={field.status}
                        fieldKey="status"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        data-filter-key="status"
                    />
                    <OptionFilter
                        fieldProp={field.tld}
                        fieldKey="tld"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        data-filter-key="tld"
                    />
                </div>
            </div>
        </div>
    );
}