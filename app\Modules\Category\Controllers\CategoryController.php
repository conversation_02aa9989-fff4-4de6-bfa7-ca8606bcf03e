<?php

namespace App\Modules\Category\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Category\Requests\CreateCategoryRequest;
use App\Modules\Category\Requests\DeleteCategoryRequest;
use App\Modules\Category\Requests\ShowListRequest;
use App\Modules\Category\Requests\UpdateCategoryRequest;
use App\Modules\Category\Services\CategoryService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CategoryController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('Category/Index', $request->show());
    }

    public function create()
    {
        return Inertia::render('Category/New');
    }

    public function store(CreateCategoryRequest $request)
    {
        $request->store();

        return redirect()->route('category');
    }

    public function edit(Request $request)
    {
        $data = CategoryService::instance()->getCategories($request->query('ids'));

        return Inertia::render('Category/Edit', ['categories' => $data]);
    }

    public function update(UpdateCategoryRequest $request)
    {
        $request->save();

        return redirect()->route('category');
    }

    public function setDefault(string $id)
    {
        CategoryService::instance()->setDefault($id);

        return redirect()->back();
    }

    public function warn(DeleteCategoryRequest $request)
    {
        $props = [];

        $props['targets'] = $request->getTargets();

        $props['categories'] = $request->show();

        return Inertia::render('Category/WarnDeletion', $props);
    }

    public function destroy(Request $request)
    {
        CategoryService::instance()->softDelete($request->query('ids'), $request->query('new'));

        return redirect()->route('category');
    }
}
