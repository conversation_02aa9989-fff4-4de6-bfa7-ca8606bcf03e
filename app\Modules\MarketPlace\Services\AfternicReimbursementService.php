<?php

namespace App\Modules\MarketPlace\Services;

use App\Events\EmailSent;
use App\Exceptions\UserDomainException;
use App\Mail\Constants\MailConstant;
use App\Mail\MarketRefundMail;
use App\Models\MarketPlaceReimbursement;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Services\Payments\MarketReimbursementService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\Stripe\Providers\StripeKeyProvider;
use App\Traits\UserContact;
use App\Util\Constant\QueueConnection;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class AfternicReimbursementService
{
    use UserContact, UserLoggerTrait;

    private $dispatchDelayInSeconds = 60;

    private $stripe;

    public function __construct()
    {
        $this->stripe = StripeKeyProvider::instance()->getStripeClient();
    }

    public static function instance()
    {
        $afternicReImbursementService = new self;

        return $afternicReImbursementService;
    }

    public function createReimbursement($data): int
    {
        return MarketPlaceReimbursement::create($data)->id;
    }

    public function refundFailedDomain($domain): void
    {
        $res = $this->getData($domain->id);

        MarketReimbursementService::instance()->createRefundSummary($res, $domain);

        app(AuthLogger::class)->info('AfternicReimbursementService: done');
    }

    public function sendRefundEmail($invoice_id, $user_id, $order_id, $domain, $reimbursement)
    {
        $invoice = DB::table('market_place_payment_invoices')->where('id', $invoice_id)->first();

        $user = DB::table('users')->select('email', 'first_name', 'last_name')->where('id', $user_id)->get()->first();

        $email = $user->email;
        $name = $user->first_name.' '.$user->last_name;

        $payload = [
            'email' => $email,
            'name' => $name,
            'domain' => $domain,
            'order_id' => $order_id,
            'total' => $reimbursement->total_amount ?? 0,
            'data' => $invoice,
        ];

        try {
            $queueMessage = (new MarketRefundMail($payload))->onConnection(QueueConnection::MAIL_JOB)->onQueue(MailConstant::PAYMENT_INVOICE);
            Mail::to($email)->send($queueMessage);
            // Mail::to($email)->send(new MarketRefundMail($payload));

            $payloadString = json_encode($payload);
            event(new EmailSent($user_id, $name, $email, 'Payment Refund from '.config('app.name'), ucwords(strtolower('TRANSFER')).' Payment Refund', $payloadString, null));
        } catch (Exception $e) {
            app(AuthLogger::class)->info('MarketPaymentInvoice: '.$e->getMessage());
            throw new Exception($e->getMessage());
        }
    }

    public function cancelMarketPlaceDomain($registered_domain_id): void
    {
        DB::table('registered_domains')
            ->where('id', '=', $registered_domain_id)
            ->update(['status' => MarketConstants::STATUS_CANCELLED]);

        DB::table('market_place_domains')
            ->where('registered_domain_id', '=', $registered_domain_id)
            ->update(['status' => MarketConstants::STATUS_CANCELLED]);
    }

    public function getSummaryRefundById(int $id, int $userId): array
    {
        if (! $id) {
            return [];
        }

        $reimbursement = $this->getById($id, $userId);
        $data = [
            'reimbursement' => $reimbursement,
            'status' => PaymentNodeStatus::REFUNDED,
            'summary_type' => PaymentSummaryType::MARKETPLACE_REIMBURSEMENT,
        ];

        return $data;
    }

    public function getById(int $id, int $userId): object
    {
        $reimbursement = DB::table('market_place_reimbursements as pr')
            ->join('market_place_node_invoices', 'market_place_node_invoices.id', '=', 'pr.marketplace_node_invoice_id')
            ->join('market_place_payment_invoices as pi', 'pi.id', '=', 'market_place_node_invoices.marketplace_payment_invoice_id')
            ->join('market_place_domains', 'market_place_domains.id', '=', 'market_place_node_invoices.marketplace_payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'market_place_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('payment_services', 'payment_services.id', '=', 'pr.payment_service_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->where('pr.id', $id)
            ->where('payment_services.user_id', $userId)
            ->select(
                'pr.id',
                'pr.marketplace_node_invoice_id as payment_node_invoice_id',
                'pr.total_amount',
                'pr.total_amount as current_balance',
                'pr.status as reimbursement_status',
                'pr.payment_service_id as payment_service_id',
                'pr.created_at',
                'payment_services.user_id as user_id',
                'pi.id as payment_invoice_id',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.extension_id',
                'registered_domains.status as registered_domain_status',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'domains.name',
                'domains.root',
                'domains.registrant',
                'domains.expiry',
                'domains.contacts',
            )->get()->first();

        if (! $reimbursement) {
            throw new UserDomainException(403, 'This action is not authorized.', 'Unauthorized');
        }

        $reimbursement->node_type = PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_REIMBURSEMENT];

        return $reimbursement;
    }

    // PRIVATE FUNCTIONS

    private function getData($id): ?object
    {
        return DB::table('market_place_node_invoices AS mni')
            ->where('mni.marketplace_payment_node_id', $id)
            ->join('market_place_payment_invoices AS mpi', 'mni.marketplace_payment_invoice_id', '=', 'mpi.id')
            ->first();
    }
}
