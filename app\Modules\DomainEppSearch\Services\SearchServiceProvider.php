<?php

namespace App\Modules\DomainEppSearch\Services;

use App\Modules\Cart\Services\MultiCheckout\MultiCartService;
use App\Modules\Cart\Services\UserCart;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\ExtensionFees;

class SearchServiceProvider
{
    public static function getSearchData(): array
    {
        $cart = new UserCart;
        $marketData = MultiCartService::instance()->getViewMarketCart();
        $registration_fees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::REGISTRATION);
        $transfer_fees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::TRANSFER);

        return ['fees' => $registration_fees, 'basic_cart' => $cart, 'market_cart' => $marketData, 'transfer_fees' => $transfer_fees];
    }
}
