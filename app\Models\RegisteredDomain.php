<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RegisteredDomain extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_contact_registrar_id',
        'extension_id',
        'domain_id',
        'status',
        'locked_until',
        'user_category_id',
        'contacts_id',
    ];

    public function extension()
    {
        return $this->belongsTo(Extension::class);
    }

    public function domain()
    {
        return $this->belongsTo(Domain::class);
    }
}
