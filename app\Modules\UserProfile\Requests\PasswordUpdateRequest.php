<?php

namespace App\Modules\UserProfile\Requests;

use App\Rules\PasswordConfirmed;
use App\Rules\StrongPasswordRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class PasswordUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'string', 'max:100', new StrongPasswordRule(), 'different:current_password'],
            'password_confirmation' => ['required', 'string', new PasswordConfirmed($this->password)],
        ];
    }

    public function messages(): array
    {
        return [
            'password.different' => 'The password field and current password must be different.',
            'password.max' => 'The password field must not be greater than 100 characters.',
            'current_password.required' => 'Please enter your current password.',
            'password.required' => 'Please enter a new password.',
            'password_confirmation.required' => 'Please confirm your new password.',
        ];
    }
}
