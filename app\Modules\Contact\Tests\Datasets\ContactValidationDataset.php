<?php

namespace App\Modules\Contact\Tests\Datasets;

class ContactValidationDataset
{
    #region Create Contact
    public static function validContactData(): array
    {
        return [
            'registry' => ['verisign'],
            "contact_name" => "test",
            'organization_name' => 'test',
            'voice_number' => '09123456789',
            'fax_number' => null,
            'email' => '<EMAIL>',
            'unit' => null,
            'street' => 'Bilwang',
            'city' => 'Isabel',
            'state_province' => 'Leyte',
            'postal_code' => '6539',
            "country_code" => "PH",
            "ext_fax_number" => "123",
            "ext_voice_number" => "63",
            "name" => "test contact"
        ];
    }

    public static function invalidContactData(): array
    {
        return [
            'field_too_short' =>
                [
                    'data' => [
                        'contact_name' => 'q',
                        'organization_name' => 'q',
                        'postal_code' => '1',
                        'voice_number' => '1'
                    ],
                    'assertion' => 'field must be at least'
                ]
            ,
            'field_too_long' =>
                [
                    'data' => [ // Data
                        'contact_name' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'organization_name' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'unit' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'street' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'city' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'state_province' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'postal_code' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'ext_voice_number' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                        'voice_number' => "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Possimus esse, cupiditate repellat eum dolorum voluptatem sit magnam cum veniam? Eius consectetur tempora architecto mollitia unde aperiam expedita consequuntur nemo at.",
                    ],
                    'assertion' => 'field must not be greater than' // Expected Assertion
                ],
            'field_must_only_contain_numeric' =>
                [
                    'data' => [ // Data
                        'ext_voice_number' => 'qwe',
                        'voice_number' => 'qwqwee',
                    ],
                    'assertion' => 'field format is invalid' // Expected Assertion
                ],
            'field_has_invalid_email' =>
                [
                    'data' => [ // Data
                        'email' => 'invalid',
                    ],
                    'assertion' => 'field must be a valid email address' // Expected Assertion
                ],
        ];
    }

    #endregion

    #region Update Contact
    public static function validUpdateContactData(): array
    {
        return [
            'registry' => 'verisign',
            "name" => "test contact",
            'organization_name' => 'test',
            "registry_contact" => "123",
            'voice_number' => '09123456789',
            'email' => '<EMAIL>',
            'unit' => null,
            'street' => 'teststreet',
            'city' => 'testcity',
            'state_province' => 'testprovince',
            'postal_code' => '6539',
            "country_code" => "PH",
            "ext_fax_number" => "123",
            "ext_voice_number" => "63"
        ];
    }

    public static function invalidUpdateContactData()
    {
        return [
            'invalid_registry' =>
                [
                    'data' => [
                        'registry' => 'invalid'
                    ],
                    'assertion' => 'The selected registry is invalid'
                ]
            ,
            'field_too_short' =>
                [
                    'data' => [
                        'name' => 'q',
                        'registry_contact' => 'q',
                        'voice_number' => '1',
                        'postal_code' => 'q',
                    ],
                    'assertion' => 'field must be at least'
                ]
            ,
            'field_too_long' =>
                [
                    'data' => [
                        'name' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'organization_name' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'registry_contact' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'voice_number' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'unit' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'street' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'city' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'state_province' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'postal_code' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                        'ext_voice_number' => 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Facilis nisi magnam tempore, veniam saepe rerum enim, ducimus numquam molestiae aliquid nulla provident quis illum rem alias sapiente consequatur temporibus a.',
                    ],
                    'assertion' => 'field must not be greater than'
                ]
            ,
        ];
    }

    #endregion

    #region Show List

    public static function validShowListData(): array
    {
        return [
            'orderby' => 'name:asc',
            'status' => 'In process',
            'registry' => 'verisign',
            'contact' => 'test contact',
            'email' => '<EMAIL>',
        ];
    }

    public static function invalidShowListData(): array
    {
        return [
            'data_not_in_rules' =>
                [
                    'data' => [
                        'orderby' => 'invalid',
                        'status' => 'invalid',
                        'registry' => 'invalid',
                    ],
                    'assertion' => 'is invalid'
                ]
            ,
            'field_has_invalid_email' =>
                [
                    'data' => [ // Data
                        'email' => 'invalid',
                    ],
                    'assertion' => 'field must be a valid email address' // Expected Assertion
                ],
        ];
    }

    #endregion


    #region Set Default Contact

    public static function validSetDefaultContactData(): array
    {
        return [
            'sortby' => 'name',
            'userContactId' => '',
            'userId' => '',
            'registryId' => '',
            'defaultContactType' => 'default_registrar_contact',
        ];
    }

    public static function invalidSetDefaultContactData()
    {
        return [
            'not_existing_ids' =>
                [
                    'data' => [
                        'userContactId' => '99',
                        'userId' => '99',
                        'registryId' => '99',
                    ],
                    'assertion' => 'is invalid'
                ],
            'invalid_contact_type' =>
                [
                    'data' => [
                        'defaultContactType' => "invalid"
                    ],
                    'assertion' => 'is invalid'
                ],
        ];
    }

    #endregion
}