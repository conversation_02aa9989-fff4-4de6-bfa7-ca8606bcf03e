<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Services\MarketOfferService;
use Illuminate\Foundation\Http\FormRequest;

class OfferCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer|exists:afternic_offers,id',
        ];
    }

    public function checkout()
    {
        return MarketOfferService::instance()->getCheckoutData($this->id);
    }
}
