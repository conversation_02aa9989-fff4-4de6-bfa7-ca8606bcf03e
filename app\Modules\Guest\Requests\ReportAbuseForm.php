<?php

namespace App\Modules\Guest\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\Guest\Services\ReportAbuseService;

class ReportAbuseForm extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s\'\-]+$/'],
            'email' => ['required', 'string', 'email'],
            'domain' => [
                'required', 
                'string', 
                'regex:/^(?:[-A-Za-z0-9]+\.)+[A-Za-z]{2,}$/',
                'exists:domains,name'
            ],
            'message' => ['required', 'string', 'min:10', 'max:500'],
            'category' => ['required', 'string', 'in:inappropriate content,malware,phishing,spam,other'],
            'attachment' => ['nullable', 'file', 'mimes:jpeg,jpg,pdf', 'max:2048'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.regex' => 'Name can only contain letters, spaces, hyphens and apostrophes.',
            'domain.regex' => 'Please enter a valid domain name.',
            'domain.exists' => 'This domain is not registered in our system.',
            'message.min' => 'Your message must be at least 10 characters long.',
            'message.max' => 'Your message cannot exceed 500 characters.',
            'category.in' => 'Please select a valid abuse category.',
            'attachment.mimes' => 'Only JPEG and PDF files are allowed.',
            'attachment.max' => 'The attachment must not be larger than 2MB.',
        ];
    }

    public function sendData()
    {
        $data = $this->validated();
        if ($this->hasFile('attachment')) {
            $data['attachment'] = $this->file('attachment');
        }
        ReportAbuseService::getInstance()->sendReportAbuse($data);
    }
}
