<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountCredit extends Model
{
    /** @use HasFactory<\Database\Factories\AccountCreditFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'block_index',
        'payment_service_id',
        'type',
        'running_balance',
        'amount',
        'previous_hash',
        'hash',
        'created_at',
    ];
}
