<?php

namespace App\Mail;

use App\Mail\Constants\Links;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class MarketRefundMail extends Mailable implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $data;

    private $domain;

    private $order_id;

    private $created_at;

    private $policyUrl;

    private $refunded_amount;

    private $transaction = "domain transfer";

    private $backOffMinutes = 5;

    public $failOnTimeout = true;

    public $uniqueFor = 300; // 5 minutes

    public $tries = 5;

    public $maxExceptions = 5;

    /**
     * Create a new message instance.
     */
    public function __construct($data)
    {
        $this->data = $data['data'];
        $this->order_id = $data['order_id'];
        $this->data = $data['data'];
        $this->domain = $data['domain'];
        $this->refunded_amount = $data['total'];
        $this->policyUrl = config('app.url').Links::REFUND_POLICY;
    }

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: ucwords($this->transaction).' Refund'
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $message = 'We regret to inform you that the refund process for the ' .  $this->transaction.' of "' . $this->domain.'" has been initiated as the transaction has failed. The refunded amount totals $' . $this->refunded_amount.'.';
        $text = "It can take approximately 10 days to appear on your statement. If it takes longer, please contact your bank for assistance. For other questions or concerns, please don't hesitate to reach out.";

        return new Content(
            markdown: 'Mails.MarketInvoiceRefund',
            with: [
                'greeting' => 'Greetings!',
                'body' => $message,
                'text' => $text,
                'sender' => config('mail.from.sd_name'),
                'domain' => $this->domain,
                'refunded_amount' => $this->refunded_amount,
                'policyUrl' => $this->policyUrl,
                'order_id' => $this->order_id,
                'created_at' => $this->uniqueId()
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
