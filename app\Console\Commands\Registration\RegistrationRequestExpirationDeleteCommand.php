<?php

namespace App\Console\Commands\Registration;

use App\Models\RegistrationRequest; 

use Illuminate\Console\Command;

class RegistrationRequestExpirationDeleteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:registration-request-expiration-delete-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Expiration Registration Requests';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        RegistrationRequest::query()
            ->where('expires_at', '<', now())
            ->delete();
    }
}
