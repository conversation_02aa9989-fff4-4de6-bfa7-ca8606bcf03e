<?php

namespace App\Modules\Guest\Requests;

use App\Mail\ContactUsMessage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Mail;
use App\Events\EmailSent;

class ClientQueryForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s\'\-]+$/'],
            'email' => ['required', 'string', 'email'],
            'message' => ['required', 'string', 'min:10', 'max:500'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.regex' => 'Name can only contain letters, spaces, hyphens and apostrophes.',
            'message.min' => 'Your message must be at least 10 characters long.',
            'message.max' => 'Your message cannot exceed 500 characters.',
        ];
    }

    public function send()
    {
        Mail::to(config('mail.from.support'))->send(new ContactUsMessage([
            'sender' => $this->email,
            'name' => $this->name,
            'body' => $this->message,
        ]));

        $payload = [
            'sender' => $this->email,
            'name' => $this->name,
            'body' => $this->message,
            'ip' => request()->ip(),
        ];

        $message = json_encode($payload);

        event(new EmailSent(
            auth()->user()->id ?? null,
            $this->name,
            $this->email,
            'Clients Query',
            'Clients Query',
            $message,
            null
        ));
    }
}
