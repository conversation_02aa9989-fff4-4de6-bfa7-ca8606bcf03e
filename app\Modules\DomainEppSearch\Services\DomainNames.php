<?php

namespace App\Modules\DomainEppSearch\Services;

use App\Modules\Domain\Constants\RegistryExtension;
use App\Util\Helper\Domain\DomainParser;

class DomainNames
{
    public string $input;

    public array $extensions;

    public array $domains;

    public array $names;

    public array $registryArrays;

    public function __construct(string $input, array $extensions)
    {
        $this->input = $input;
        $this->extensions = $extensions;
    }

    public function parse(): DomainNames
    {
        $this->names = preg_split("/\s*[,\s\n]\s*/", trim($this->input));

        return $this;
    }

    public function create(): DomainNames
    {
        $this->createRegistryArray();
        $this->createDomainNames();

        return $this;
    }

    /**
     * @return ['registry1' => [], 'registry2' => []]
     */
    public function getRegistryArrays(): array
    {
        return $this->registryArrays;
    }

    /**
     * @return ['domain1.com', 'domain2.net', 'domain3.org']
     */
    public function getDomains(): array
    {
        return $this->domains;
    }

    private function createRegistryArray(): void
    {
        $this->registryArrays = DomainParser::createRegistryArray();
    }

    private function createDomainNames(): void
    {
        $this->domains = [];

        foreach ($this->names as &$name) {

            $domain = DomainParser::getNameAndExtension(trim($name));

            if (! empty($domain['extension']) && ! empty($domain['name'])) {
                $dname = $domain['name'].'.'.$domain['extension'];

                if (! in_array($dname, $this->domains) && in_array($domain['extension'], $this->extensions)) {
                    $registryName = DomainParser::getRegistryName($dname);
                    array_push($this->registryArrays[$registryName], $dname);
                    array_push($this->domains, $dname);
                }
            }

            foreach (RegistryExtension::SUPPORTED as $sup_ext) {

                if (in_array($sup_ext, $this->extensions)) {
                    $dname = $domain['name'].'.'.$sup_ext;

                    if (! empty($domain['name']) && ! in_array($dname, $this->domains)) {
                        $registryName = DomainParser::getRegistryName($dname);
                        array_push($this->registryArrays[$registryName], $dname);
                        array_push($this->domains, $dname);
                    }
                }
            }
        }
    }
}
