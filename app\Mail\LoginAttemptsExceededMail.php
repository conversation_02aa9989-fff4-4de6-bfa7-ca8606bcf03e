<?php

namespace App\Mail;

use App\Mail\Constants\AuthenticationAttemptsConstant;
use App\Util\Constant\QueueConnection;

use Illuminate\Mail\Mailables\Address;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class LoginAttemptsExceededMail extends Mailable implements ShouldQueue, ShouldBeUnique
{
    use Queueable, SerializesModels;

    /**
     * Data Information for email
     * 
     * @var array 
     */
    private array $data;

    /**
     * The number of seconds after which the job's unique lock will be released.
     *
     * @var int
     */
    public $uniqueFor = 300;

    /**
     * Create a new message instance.
     */
    public function __construct()
    {
        $this->onConnection(QueueConnection::AUTHENTICATION_ATTEMPTS_JOBS);
        $this->onQueue(AuthenticationAttemptsConstant::LOGIN_ATTEMPTS_EXCEEDED);
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from   : new Address(config('mail.from.address'), config('mail.from.sd_name')),
            subject: 'Login Attempts Exceeded',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.LoginAttemptsExceededMail',
            view    : 'view.name',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
