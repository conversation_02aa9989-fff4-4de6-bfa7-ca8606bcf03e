<?php

namespace App\Modules\Auth\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppSessionService;
use App\Modules\Auth\Services\EmailOtpService;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use Inertia\Inertia;
use Inertia\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyHttpResponse;

class TwoFactorAuthenticationController extends Controller
{
    /* TRAITS */
    // ?...

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Choose 2FA Verification Type
     */
    public function chooseVerificationType(Request $request, int $userId): Response
    {
        session(['rememberMe' => $request->query('rememberMe', false)]);

        $user = User::find($userId);

        if ($user == null) {
            abort(404);
        }

        $enabledMethods =
        [
            'email' => false,
            'authenticator' => false,
            'passcode' => false,
        ];

        if ($user->enabled_login_auth_options != null) {
            $options = json_decode($user->enabled_login_auth_options);

            if (in_array('email', $options)) {
                $enabledMethods['email'] = true;
            }

            if (in_array('authApp', $options)) {
                $enabledMethods['authenticator'] = true;
            }
        }

        return Inertia::render(
            'Auth/Login2FaVerification/Login2FaVerification',
            [
                'userId' => $user->id,
                'isEnabledMethod' => [
                    'email' => $enabledMethods['email'],
                    'authenticator' => $enabledMethods['authenticator'],
                    'passcode' => $enabledMethods['passcode'],
                ],
            ]
        );
    }

    /**
     * Choose Email Authentication
     */
    public function chooseEmailAuthentication(Request $request, int $userId): SymfonyHttpResponse
    {
        $user = User::findOrFail($userId);

        $entry = (new EmailOtpService)->generateEntry($user, ClientIp::getClientIp($request));

        (new EmailOtpService)->sendEmail(
            $user->email,
            $entry
        );

        $url = URL::temporarySignedRoute(
            'login.2fa.email.auth',
            now()->addMinutes(15),
            ['token' => $entry['sessionToken']]
        );

        return Inertia::location($url);
    }

    /**
     * Choose Authenticator App
     */
    public function chooseAuthenticatorApp(Request $request, int $userId): SymfonyHttpResponse
    {
        $user = User::findOrFail($userId);

        $entry = (new AuthenticatorAppSessionService)->generateEntry($user, ClientIp::getClientIp($request));

        $url = URL::temporarySignedRoute(
            'login.2fa.authenticator-app.auth',
            now()->addMinutes(15),
            ['token' => $entry['sessionToken']]
        );

        return Inertia::location($url);
    }

    /**
     * Choose Passcode
     *
     * @param  int  $userId
     *
     * //@return SymfonyHttpResponse
     */
    public function choosePasscode(Request $request, int $userId)// : SymfonyHttpResponse
    {
        // $user = User::findOrFail($userId);

        // $entry = (new EmailOtpService())->generateEntry($user, $request->ip());

        // (new EmailOtpService())->sendEmail(
        //     $user->email,
        //     $entry
        // );

        // $url =  URL::temporarySignedRoute(
        //     'login.2fa.email.auth',
        //     now()->addMinutes(15),
        //     ['token' => $entry['sessionToken']]
        // );

        // return Inertia::location(
        //     $url
        // );
    }
}
