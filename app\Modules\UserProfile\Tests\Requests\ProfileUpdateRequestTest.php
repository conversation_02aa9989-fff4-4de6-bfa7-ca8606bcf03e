<?php
namespace App\Modules\UserProfile\Tests\Requests;

use Illuminate\Support\Facades\Validator;
use App\Modules\UserProfile\Requests\ProfileUpdateRequest;
use App\Modules\UserProfile\Tests\Datasets\CommonValidationDataset;

it('passes validation with valid data', function () {
    $request = new ProfileUpdateRequest;
    $validDatasets = CommonValidationDataset::validProfileData();

    foreach ($validDatasets as $testName => $data) {
        $validator = Validator::make($data, $request->rules());
        expect($validator->passes())->toBeTrue("Failed validation for {$testName}: " . json_encode($validator->errors()));
    }
});

it('fails validation when required fields are missing', function () {
    $request = new ProfileUpdateRequest;

    $validator = Validator::make([], $request->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('first_name'))->toBeTrue("Failed to validate required field for first_name");
    expect($validator->errors()->has('last_name'))->toBeTrue("Failed to validate required field for last_name");
    expect($validator->errors()->has('email'))->toBeTrue("Failed to validate required field for email");
});

it('fails validation when name field has invalid data', function () {
    $request = new ProfileUpdateRequest;
    $invalidDatasets = CommonValidationDataset::invalidNameData();

    foreach ($invalidDatasets as $testName => $data) {
        $validator = Validator::make($data, $request->rules());

        foreach (array_keys((array) $data) as $key) {
            expect($validator->errors()->has($key))->toBeTrue("Failed to validate {$key} field for test case: {$testName}");
        }
        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
    }
});

it('fails validation when email field has invalid data', function () {
    $request = new ProfileUpdateRequest;
    $invalidDatasets = CommonValidationDataset::invalidEmailData();

    foreach ($invalidDatasets as $testName => $data) {
        $validator = Validator::make($data, $request->rules());

        foreach (array_keys((array) $data) as $key) {
            expect($validator->errors()->has($key))->toBeTrue("Failed to validate {$key} field for test case: {$testName}");
        }
        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
    }
});