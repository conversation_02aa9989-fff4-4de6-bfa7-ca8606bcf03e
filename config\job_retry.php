<?php

use App\Util\Constant\QueueConnection;

$max_attempt_key = 'MAX_ATTEMPT_';
$max_attempt_default = env($max_attempt_key.strtoupper(QueueConnection::DEFAULT), 2);

return [
    'default' => $max_attempt_default,
    'max_attempt' => [
        QueueConnection::DEFAULT => env($max_attempt_key.strtoupper(QueueConnection::DEFAULT), $max_attempt_default),
        QueueConnection::DOMAIN_REGISTRATION => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_REGISTRATION), $max_attempt_default),
        QueueConnection::DOMAIN_CONTACTS_UPDATE => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_CONTACTS_UPDATE), $max_attempt_default),
        QueueConnection::DOMAIN_TRANSFER => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_TRANSFER), $max_attempt_default),
        QueueConnection::DOMAIN_TRANSFER_RESPONSE => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_TRANSFER_RESPONSE), $max_attempt_default),
        QueueConnection::DOMAIN_TRANSFER_CANCEL => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_TRANSFER_CANCEL), $max_attempt_default),
        QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE), $max_attempt_default),
        QueueConnection::CONTACT_REGISTRATION => env($max_attempt_key.strtoupper(QueueConnection::CONTACT_REGISTRATION), $max_attempt_default),
        QueueConnection::CONTACT_UPDATE => env($max_attempt_key.strtoupper(QueueConnection::CONTACT_UPDATE), $max_attempt_default),
        QueueConnection::DOMAIN_RENEWAL => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_RENEWAL), $max_attempt_default),
        QueueConnection::DOMAIN_UPDATE => env($max_attempt_key.strtoupper(QueueConnection::DOMAIN_UPDATE), $max_attempt_default),
    ],
];
