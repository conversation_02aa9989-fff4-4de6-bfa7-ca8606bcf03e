<?php

namespace App\Rules;

use App\Models\UserPaymentMethod; 

use Illuminate\Support\Facades\Auth; 
use Illuminate\Contracts\Validation\ValidationRule;

use Closure;

class PaymentMethodUniqueCardNicknamePerUserRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $paymentMethod = UserPaymentMethod::query()
                            ->where('user_id', '=', Auth::user()->id)
                            ->where('card_nickname', '=', $value) 
                            ->first(); 

        if ($paymentMethod != null)
        {
            $fail("Card with this nickname already exists");
        }
    }
}
