<?php

namespace App\Console\Commands\Domain;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Jobs\UpdateDomainAuthCode;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Modules\Histories\Constants\SystemTransactionType;
use App\Events\SystemLogEvent;

class DomainAuthRegenerator extends Command
{
    private $authCodeValidity = 40; // value in days

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'domain:domain-auth-regenerator';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'check for invalid auth codes and regenerate.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DomainAuthRegenerator: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('DomainAuthRegenerator: Running...');

        $lastDateValid = Carbon::now()->subDays($this->authCodeValidity);

        app(AuthLogger::class)->info('DomainAuthRegenerator: Checking for expired auth codes...');

        $domainExpiredAuthCodes = DB::table('domains')->whereNull('deleted_at')
            ->where('auth_code_updated_at', '<', $lastDateValid)
            ->select('id', 'name')->get()->toArray();

        if (empty($domainExpiredAuthCodes)) {
            app(AuthLogger::class)->info('DomainAuthRegenerator: Terminating, no expired auth code found...');
            app(AuthLogger::class)->info('DomainAuthRegenerator: Done');

            return;
        }

        app(AuthLogger::class)->info('DomainAuthRegenerator: Dispatching UpdateDomainAuthCode job...');

        foreach ($domainExpiredAuthCodes as $domain) {
            $payload = ['name' => $domain->name, 'regenerateAuthCode' => true];
            UpdateDomainAuthCode::dispatch($domain->id, $payload);
        }

        app(AuthLogger::class)->info('DomainAuthRegenerator: Done');
        event(new SystemLogEvent(
            SystemTransactionType::DOMAIN_AUTH_REGENERATOR,
            sprintf('Expired auth codes regenerated for %d domain(s) (older than %d days)', 
                count($domainExpiredAuthCodes), 
                $this->authCodeValidity
            ),
            null
        ));
    }
}
