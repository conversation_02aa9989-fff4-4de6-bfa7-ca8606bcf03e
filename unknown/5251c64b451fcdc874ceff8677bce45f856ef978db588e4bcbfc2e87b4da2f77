<?php

namespace App\Console\Commands\AuthenticatorApp;

use App\Models\AuthenticatorAppUserSessions; 
use Illuminate\Console\Command;

class AuthenticatorAppDeleteUnathenticatedEntriesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:authenticator-app-delete-unathenticated-entries-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Unathenticated Entries Command';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        AuthenticatorAppUserSessions::query()
            ->whereNull('session_valid_until')
            ->where('created_at', '<', now()->subHours(2))
            ->delete();
    }
}
