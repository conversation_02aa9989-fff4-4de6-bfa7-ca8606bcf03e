<?php

namespace App\Console\Commands\Client;

use App\Modules\Client\Constants\ScheduleType;
use App\Modules\Client\Jobs\DomainExpirySender;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\ExpirationNoticeService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class DomainExpiryEvaluator extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'client:domain-expiry-evaluator';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'check domain_expiration_notifications for possible notification';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DomainExpiryEvaluator: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    // PRIVATE FUNCTIONS

    private function evaluate(): void
    {
        app(AuthLogger::class)->info('DomainExpiryEvaluator: Running...');

        $lists = ExpirationNoticeService::getUserExpiryNotification();
        if ($lists->isEmpty()) {
            return;
        }
        
        $this->sendNotification($lists);
        $this->removeListFromScheduler($lists->pluck('id')->toArray());

        app(AuthLogger::class)->info('DomainExpiryEvaluator: done');
    }

    private function removeListFromScheduler(array $ids): void
    {
        ExpirationNoticeService::setUserExpectationNotificationAt($ids, now()->addDays(ScheduleType::MAX_LAPSE_IN_DAYS));
    }

    // private function getLists(): Collection
    // {
    //     $lists = ExpirationNoticeService::getUserExpiryNotification();
    //     if ($lists->isEmpty()) {
    //         app(AuthLogger::class)->info('DomainExpiryEvaluator: Terminating, nothing to process..');
    //         throw new Exception('Terminating, nothing to process..');
    //     }

    //     return $lists;
    // }

    private function sendNotification(Collection $lists)
    {
        app(AuthLogger::class)->info('DomainExpiryEvaluator: found ' . $lists->count() . ', start sending notification to queue...');
        foreach ($lists as $notice) {
            DomainExpirySender::dispatch($notice);
        }
    }
}
