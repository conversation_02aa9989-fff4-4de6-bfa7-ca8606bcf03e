<?php

namespace App\Console\Commands\Domain;

use App\Models\UserDomainExport;
use App\Modules\UserDomainExport\Services\UserDomainExportService; 

use Illuminate\Console\Command;

class DomainExportFileDeleteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:domain-export-file-delete-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Domain Export Files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $items = UserDomainExport::query()
            ->where('created_at', '<=', now()->subDays(30))
            ->get(); 

        foreach ($items as $item)
        {
            (new UserDomainExportService())->deleteFile($item->id); 
        }
    }
}
