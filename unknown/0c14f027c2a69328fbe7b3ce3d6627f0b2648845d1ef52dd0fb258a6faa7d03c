<?php

namespace App\Console\Commands\Domain;

use App\Console\Commands\Constants\SchedulerTypes;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\UpdateServices\PostAutoRenewalGracePeriodService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Exception;

class PostAutoRenewalGracePeriodHandler extends Command
{
    private $limit = 500;
    private $maxDelay = 6; // in hours
    private $maxDaysBeforeDeletion = 45;
    private $gracePeriodDays = 40;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'domain:post-auto-renewal-grace-period-handler';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles domains that have expired after the auto-renewal grace period.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'PostAutoRenewalGracePeriodHandler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('PostAutoRenewalGracePeriodHandler: Checking for domains...');

        $skip = $this->validateCronHistory();

        if ($skip) return;

        $domains = $this->getExpiredDomains();
        $hasData = $domains->isNotEmpty();

        if ($hasData) {
            app(AuthLogger::class)->info('PostAutoRenewalGracePeriodHandler: Found ' . $domains->count() . ' domains...');
            PostAutoRenewalGracePeriodService::instance()->setForDeletion($domains);
        } else app(AuthLogger::class)->info('PostAutoRenewalGracePeriodHandler: No domains found...');

        $this->updateCronHistory($hasData);
        app(AuthLogger::class)->info('PostAutoRenewalGracePeriodHandler: Done');
    }

    /**
     * Get all domains that have been expired for 40–45 days.
     */
    private function getExpiredDomains(): Collection
    {
        return DB::table('registered_domains')
            ->select(
                'domains.*',
                'registered_domains.id as registered_domain_id',
                'users.email as user_email',
                'users.id as user_id'
            )
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->where('registered_domains.status', '=', UserDomainStatus::OWNED)
            ->where('domains.status', '=', DomainStatus::EXPIRED)
            ->where('domains.server_renew_at', '<', Carbon::now()->subDays($this->maxDaysBeforeDeletion))
            ->where('domains.client_renew_at', '<', Carbon::now()->subDays($this->gracePeriodDays))
            // ->whereBetween('domains.expiry', [
            //     Carbon::now()->subDays($this->maxDaysBeforeDeletion)->getTimestampMs(),
            //     Carbon::now()->subDays($this->gracePeriodDays)->getTimestampMs()
            // ])
            ->where('domains.expiry', '<', Carbon::now()->subDays($this->gracePeriodDays)->getTimestampMs())
            ->limit($this->limit)->get();
    }

    private function getCron(): object
    {
        $cron = DB::table('cron_run_histories')->where('name', SchedulerTypes::POST_AUTO_RENEWAL_GRACE_PERIOD)->first();

        if ($cron) return $cron;

        $errorMsg = 'PostAutoRenewalGracePeriodHandler: No cron data found';
        app(AuthLogger::class)->error($errorMsg);
        throw new Exception($errorMsg);
    }

    private function validateCronHistory(): bool
    {
        $cron = $this->getCron();
        $lastRunAt = Carbon::parse($cron->last_run_at);
        $shouldRun = $cron->has_data || $lastRunAt->lessThanOrEqualTo(Carbon::now()->subHours($this->maxDelay));

        if ($shouldRun) return false;

        $errorMsg = 'PostAutoRenewalGracePeriodHandler: Skipping for ' . $this->maxDelay . ' hours...';
        app(AuthLogger::class)->info($errorMsg);

        return true;
    }

    private function updateCronHistory(bool $hasData): void
    {
        DB::table('cron_run_histories')
            ->where('name', SchedulerTypes::POST_AUTO_RENEWAL_GRACE_PERIOD)
            ->update(['has_data' => $hasData, 'last_run_at' => Carbon::now()]);
    }
}
