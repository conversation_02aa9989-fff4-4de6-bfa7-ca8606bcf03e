<?php

namespace App\Console\Commands\Client;

use App\Modules\Contact\Jobs\RegisterEppContact;
use App\Modules\Contact\Jobs\UpdateEppContact;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Domain\Services\JobServices\JobRecord;
use App\Modules\JobRetry\Services\RetryJobService;
use App\Modules\Push\Jobs\UpdateEppDomainContacts;
use App\Modules\Transfer\Jobs\CancelDomainTransfer;
use App\Modules\Transfer\Jobs\SendEppTransferRequestResponse;
use App\Modules\Transfer\Jobs\TransferEppDomain;
use App\Modules\Transfer\Jobs\UpdateTransferFromPoll;
use App\Util\Constant\QueueConnection;
use Exception;
use Illuminate\Console\Command;
use App\Events\SystemLogEvent;
use App\Modules\Histories\Constants\SystemTransactionType;

class JobRetryScheduler extends Command
{
    private $dispatchDelayInSeconds = 60;

    private $size = 5;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'client:job-retry-scheduler';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'checking jobs to retry...';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'JobRetryScheduler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('JobRetryScheduler: Running...');

        $lists = RetryJobService::instance()->getRetryJobs($this->size);
        if ($lists->isEmpty()) {
            app(AuthLogger::class)->info('JobRetryScheduler: Terminating, nothing to process..');

            return;
        }

        app(AuthLogger::class)->info('JobRetryScheduler: found ' . $lists->count() . ', retrying jobs...');
        try {
            $count = 0;
            foreach ($lists as $item) {
                $type = $item->type;
                switch ($type) {
                    case QueueConnection::DOMAIN_REGISTRATION:
                        $this->createRetryRegistration($item, $count);
                        break;
                    case QueueConnection::DOMAIN_CONTACTS_UPDATE:
                        $this->createRetryPushDomainUpdate($item, $count);
                        break;
                    case QueueConnection::DOMAIN_TRANSFER:
                        $this->createRetryTransferEppDomain($item, $count);
                        break;
                    case QueueConnection::DOMAIN_TRANSFER_RESPONSE:
                        $this->createRetrySendEppTransferRequestResponse($item, $count);
                        break;
                    case QueueConnection::DOMAIN_TRANSFER_CANCEL:
                        $this->createRetryCancelDomainTransfer($item, $count);
                        break;
                    case QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE:
                        $this->createRetryUpdateTransferFromPoll($item, $count);
                        break;
                    case QueueConnection::CONTACT_REGISTRATION:
                        $this->createRetryContactRegistration($item, $count);
                        break;
                    case QueueConnection::CONTACT_UPDATE:
                        $this->createRetryContactUpdate($item, $count);
                        break;
                    case QueueConnection::DOMAIN_RENEWAL:
                        $this->createRetryRenewal($item, $count);
                        break;
                    case QueueConnection::DOMAIN_UPDATE:
                        $this->createRetryDomainUpdate($item, $count);
                        break;
                    case QueueConnection::DOMAIN_REFRESH:
                        $this->createRetryDomainRefresh($item, $count);
                        break;
                    default:
                        return;
                }
                $count++;
            }
        } catch (Exception $e) {
            $errorMsg = 'JobRetryScheduler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }

        app(AuthLogger::class)->info('JobRetryScheduler: done');
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Completed retry scheduling for {$lists->count()} jobs",
            null
        ));
    }

    private function createRetryRegistration($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = new JobRecord(json_decode($item->record, true));

        $registrationPayload = $record->getRegisterRecord();
        $updatePayload = $record->getUpdateAfterRegisterRecord();

        JobDispatchService::instance()->registerEppDispatch($registrationPayload, $updatePayload, $seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for domain registration by {$record->getRecord()['email']} in {$seconds} seconds",
            null
        ));
    }

    private function createRetryPushDomainUpdate($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = json_decode($item->record);

        $pushRequest = $record->push_request;
        $action = $record->action;
        $email = $record->email;

        UpdateEppDomainContacts::dispatch($pushRequest, $action, $email)->delay($seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for domain contacts update by {$email} in {$seconds} seconds",
            null
        ));
    }

    private function createRetryCancelDomainTransfer($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = json_decode($item->record, true);

        $cancellationType = $record['cancellationType'];
        $transferDomainId = $record['transferDomainId'];
        $userId = $record['userId'];
        $email = $record['email'];

        CancelDomainTransfer::dispatch($cancellationType, $transferDomainId, $userId, $email)->delay($seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for domain transfer cancellation by {$email} in {$seconds} seconds",
            null
        ));
    }

    private function createRetrySendEppTransferRequestResponse($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = json_decode($item->record, true);

        $transferId = $record['transferId'];
        $domainName = $record['domainName'];
        $domainId = $record['domainId'];
        $registeredDomainId = $record['registeredDomainId'];
        $userId = $record['userId'];
        $email = $record['email'];
        $action = $record['action'];

        SendEppTransferRequestResponse::dispatch($transferId, $domainName, $domainId, $registeredDomainId, $userId, $email, $action)
            ->delay($seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for transfer response of domain '{$domainName}' by {$email} in {$seconds} seconds",
            null
        ));
    }

    private function createRetryTransferEppDomain($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = json_decode($item->record, true);

        $domainId = $record['domainId'];
        $domainName = $record['domain'];
        $authCode = $record['authCode'];
        $registeredDomainId = $record['registeredDomainId'];
        $userId = $record['userId'];
        $email = $record['email'];

        TransferEppDomain::dispatch($domainId, $domainName, $authCode, $registeredDomainId, $userId, $email)
            ->delay($seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for domain transfer of '{$domainName}' by {$email} in {$seconds} seconds",
            null
        ));
    }

    private function createRetryUpdateTransferFromPoll($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = json_decode($item->record, true);

        $pollId = $record['pollId'];
        $status = $record['status'];
        $domainName = $record['name'];

        UpdateTransferFromPoll::dispatch($pollId, $status, $domainName)->delay($seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for transfer poll update of domain '{$domainName}' in {$seconds} seconds",
            null
        ));
    }

    private function createRetryContactRegistration($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = json_decode($item->record, true);

        $user_contact_id = $record['userContactId'];
        $epp_data = $record['eppData'];
        $default_contacts = $record['defaultContacts'];
        $registry = $record['registry'];
        $registry_id = $record['registryId'];
        $user_id = $record['userId'];
        $email = $record['email'];

        RegisterEppContact::dispatch($user_contact_id, $epp_data, $default_contacts, $registry, $registry_id, $user_id, $email)->delay($seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for contact registration by {$email} in {$seconds} seconds",
            null
        ));
    }

    private function createRetryContactUpdate($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = json_decode($item->record, true);

        $contact_id = $record['contactId'];
        $local_data = $record['localData'];
        $epp_data = $record['eppData'];
        $registry = $record['registry'];
        $email = $record['email'];
        $user_id = $record['userId'];

        UpdateEppContact::dispatch($contact_id, $local_data, $epp_data, $registry, $email, $user_id)->delay($seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for contact update by {$email} in {$seconds} seconds",
            null
        ));
    }

    private function createRetryRenewal($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = new JobRecord(json_decode($item->record, true));

        JobDispatchService::instance()->renewEppDispatch($record->getRecord(), $seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for domain renewal in {$seconds} seconds",
            null
        ));
    }

    private function createRetryDomainUpdate($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = new JobRecord(json_decode($item->record, true));

        JobDispatchService::instance()->updateEppDispatch($record->getRecord(), $seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for domain update in {$seconds} seconds",
            null
        ));
    }

    private function createRetryDomainRefresh($item, $count)
    {
        $seconds = ($this->dispatchDelayInSeconds * $count) + $this->dispatchDelayInSeconds;
        $record = new JobRecord(json_decode($item->record, true));

        JobDispatchService::instance()->refreshEppDispatch($record->getRecord(), $seconds);
        event(new SystemLogEvent(
            SystemTransactionType::JOB_RETRY_SCHEDULER,
            "Scheduled retry for domain refresh in {$seconds} seconds",
            null
        ));
    }
}
