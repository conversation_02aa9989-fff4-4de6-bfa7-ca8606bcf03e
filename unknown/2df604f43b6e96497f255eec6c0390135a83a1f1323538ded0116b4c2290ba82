<?php

namespace App\Console\Commands\Afternic;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Services\MarketDomainService;
use Exception;
use Illuminate\Console\Command;

class AfternicJobRetry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:afternic-job-retry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'AfternicJobRetry: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('AfternicJobRetry: Running...');

        $pending = MarketDomainService::instance()->getAllJobRetrys();

        if($pending->isEmpty()) return app(AuthLogger::class)->info('AfternicJobRetry: Terminating, nothing to process..');

        app(AuthLogger::class)->info('AfternicJobRetry: found ' . $pending->count());

        foreach ($pending as $domain) {
            app(AuthLogger::class)->info("AfternicJobRetry: Attempt retry transfer request for order_id: " . $domain->id . ", domain: " . $domain->domain);
            MarketDomainService::instance()->retryJob($domain);
        }

        app(AuthLogger::class)->info('AfternicJobRetry: done');
    }
}
