<?php

namespace App\Console\Commands\Afternic;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Jobs\AfternicManualTransferJob;
use App\Modules\MarketPlace\Services\AfternicManualService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Crypt;

class AfternicManualTransfer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:afternic-get-error';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'AfternicManualTransfer: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('AfternicManualTransfer: Running...');

        $failed = AfternicManualService::instance()->getFailedDomains();

        if($failed->isEmpty()) return app(AuthLogger::class)->info('AfternicManualTransfer: Terminating, nothing to process..');

        app(AuthLogger::class)->info('AfternicManualTransfer: found ' . $failed->count());

        foreach ($failed as $domain) {
            app(AuthLogger::class)->info("AfternicManualTransfer: Attempt manual transfer request for domain: " . $domain->name );
            AfternicManualService::instance()->initiateManualTransfer($domain->name, Crypt::encryptString($domain->auth));
        }

        app(AuthLogger::class)->info('AfternicManualTransfer: done');
    }
}
