<?php

namespace App\Console\Commands\EmailOtp;

use App\Models\EmailOtp; 

use Illuminate\Console\Command;

class EmailOtpDeleteUnathenticatedEntriesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:email-otp-delete-unathenticated-entries-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Unathenticated Entries Command';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        EmailOtp::query()
            ->whereNull('session_valid_until')
            ->where('created_at', '<', now()->subHours(2))
            ->delete();
    }
}
