<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('domain_threshold', function (Blueprint $table) {
            $table->id(); // auto increment
            $table->integer('threshold_limit')->default(50);
            $table->string('action')->default('notify');
            $table->string('period_type')->default('monthly');
            $table->string('send_notif')->default('<EMAIL>'); // email
            $table->integer('times_triggered')->default(0);
            // Add timestamps with default current time
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP'));
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('domain_threshold');
    }
};
