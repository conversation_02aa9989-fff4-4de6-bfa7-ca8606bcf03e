<?php

namespace App\Modules\Contact\Requests;

use App\Modules\Contact\Services\ContactService;
use App\Modules\Contact\Constants\ContactType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SetDefaultContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'sortby' => ['string', 'min:3', 'max:30'],
            'userContactId' => ['required', 'string', 'exists:user_contacts,id'],
            'userId' => ['required', 'string', 'exists:users,id'],
            'registryId' => ['required', 'string', 'exists:registries,id'],
            'defaultContactType' => ['required', 'string', Rule::in(ContactType::DEFAULTS)],
        ];
    }

    public function setDefault()
    {
        $userContactId = $this->userContactId;
        $userId = $this->userId;
        $registryId = $this->registryId;
        $defaultContactType = $this->defaultContactType;

        ContactService::instance()->setDefaultContact($userContactId, $userId, $registryId, [$defaultContactType => true]);
    }
}
