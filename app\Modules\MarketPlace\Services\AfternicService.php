<?php

namespace App\Modules\MarketPlace\Services;

use App\Modules\MarketPlace\Constants\MarketConstants;
use Illuminate\Support\Facades\Http;

class AfternicService
{
    public static function instance()
    {
        $afternicService = new self;

        return $afternicService;
    }

    public static function placeHold($domain)
    {
        return Http::afternic()->put("/listings/$domain/hold");
    }

    public static function retrieveHold($domain)
    {
        return Http::afternic()->get("/listings/$domain/hold")->object();
    }

    public static function releaseHold($domain)
    {
        return Http::afternic()->delete("/listings/$domain/hold")->object();
    }

    public static function createOrder($domain, $price)
    {
        return Http::afternic()->post("/listings/$domain/purchase", ['price' => intval($price) * MarketConstants::MICRO])->object();
    }

    public static function getOrder($orderid)
    {
        return Http::afternic()->get("/orders/$orderid")->object();
    }

    public static function getCommission($orderid)
    {
        return json_decode(Http::afternic()->get("/orders/$orderid/commission")->body());

        /**
            $commission = AfternicService::getCommission('19047796');
            $reason = $commission->commissionReason;
        */
    }

    public static function sendRequested($orderid)
    {
        return Http::afternic()->patch("/orders/$orderid", [
            'status' => MarketConstants::STATUS_REQUESTED,
        ]);
    }

    public static function sendCompleted($orderid)
    {
        return Http::afternic()->patch("/orders/$orderid", [
            'status' => MarketConstants::STATUS_COMPLETED,
        ]);
    }

    // ERROR CASES

    /*
        When the transfer request is not successful at the registry due to lock
        afternic doc page 23
        https://partnerportal.afternic.com/ppdoc/docs/type-seven/Integration%20Guide%20for%20Aftermarket%20Purchase%20API/pdf/Integration%20Guide%20for%20Aftermarket%20Purchase%20API.pdf
    */

    public static function sendDomainLocked($orderid)
    {
        return Http::afternic()->patch("/orders/$orderid", [
            'status' => MarketConstants::STATUS_FAILED,
            'errorCode' => MarketConstants::ERROR_REQUESTED_DOMAIN_LOCKED,
        ]);
    }

    /*
        When the transfer request is not successful at the registry due to an invalid auth code
        afternic doc page 24
        https://partnerportal.afternic.com/ppdoc/docs/type-seven/Integration%20Guide%20for%20Aftermarket%20Purchase%20API/pdf/Integration%20Guide%20for%20Aftermarket%20Purchase%20API.pdf
    */

    public static function sendInvalidAuthCode($orderid)
    {
        return Http::afternic()->patch("/orders/$orderid", [
            'status' => MarketConstants::STATUS_FAILED,
            'errorCode' => MarketConstants::ERROR_REQUESTED_INVALID_AUTH_CODE,
        ]);
    }

    /*
        When the transfer request is not successful at the registry and it’s not due to an invalid auth code or a locked domain
        afternic docs page 27
        https://partnerportal.afternic.com/ppdoc/docs/type-seven/Integration%20Guide%20for%20Aftermarket%20Purchase%20API/pdf/Integration%20Guide%20for%20Aftermarket%20Purchase%20API.pdf
    */

    public static function sendRequestedFailureOther($orderid)
    {
        return Http::afternic()->patch("/orders/$orderid", [
            'status' => MarketConstants::STATUS_FAILED,
            'errorCode' => MarketConstants::ERROR_REQUESTED_FAILURE_OTHER,
        ]);
    }

    /*
        The transfer is rejected at the Losing Registrar for some unknown reason
        afternic docs page 28
        https://partnerportal.afternic.com/ppdoc/docs/type-seven/Integration%20Guide%20for%20Aftermarket%20Purchase%20API/pdf/Integration%20Guide%20for%20Aftermarket%20Purchase%20API.pdf
    */

    public static function sendCompletionFailureOther($orderid)
    {
        return Http::afternic()->patch("/orders/$orderid", [
            'errorCode' => MarketConstants::ERROR_COMPLETION_FAILURE_OTHER,
        ]);
    }
}
