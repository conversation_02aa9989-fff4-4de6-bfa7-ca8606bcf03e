<?php

namespace App\Modules\UserProfile\Tests\Requests;

use App\Modules\UserProfile\Requests\UserProfileUpdateProfileInformationFormRequest;
use App\Modules\UserProfile\Tests\Datasets\CommonValidationDataset;
use Illuminate\Support\Facades\Validator;


// it('fails validation when required fields are missing', function () {
//     $validator = Validator::make([], (new UserProfileUpdateProfileInformationFormRequest)->rules());

//     expect($validator->fails())->toBeTrue();
//     expect($validator->errors()->has('firstName'))->toBeTrue();
//     expect($validator->errors()->has('lastName'))->toBeTrue();
// });

// it('fails validation when first name is too short', function () {
//     $validator = Validator::make([
//         'firstName' => 'A',
//         'lastName' => 'Valid-Name',
//     ], (new UserProfileUpdateProfileInformationFormRequest)->rules());

//     expect($validator->fails())->toBeTrue();
// });

// it('fails validation when last name is too short', function () {
//     $validator = Validator::make([
//         'firstName' => 'Valid-Name',
//         'lastName' => 'A',
//     ], (new UserProfileUpdateProfileInformationFormRequest)->rules());

//     expect($validator->fails())->toBeTrue();
//     expect($validator->errors()->has('lastName'))->toBeTrue();
// });
