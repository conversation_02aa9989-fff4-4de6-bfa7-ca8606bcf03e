<?php

namespace App\Rules\Payment;

use App\Modules\Payment\Constants\OtherFees;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateStripeFees implements ValidationRule
{
    private $keys;

    public function __construct()
    {
        $this->keys = OtherFees::STRIPE_FEES;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $exists = $this->otherFeesExists($value);

        if (! $exists) {
            $fail('There are some fees missing.');
        }
    }

    public function otherFeesExists(array $otherFees)
    {
        foreach ($this->keys as $key) {
            if (! isset($otherFees[$key])) {
                // Log::info($key);

                return false;
            }

            if ($otherFees[$key] <= 0) {
                return false;
            }
        }

        return true;
    }
}
