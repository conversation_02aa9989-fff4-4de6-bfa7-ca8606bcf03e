<x-mail::message>
# Payment Invoice

@component('mail::table')
| Sold to: {{$name}} |
| :--------- |
| Transaction ID: {{$summaryData->transaction_id ?? 'N/A'}}|
| Date: {{ \Carbon\Carbon::parse($data[0]->created_at)->format('F j, Y h:i:s A') }} |
| Payment Method: {{$paymentIntent}} |
@endcomponent

<hr>

@component('mail::table')
| Domain | Type | Duration |Amount |
| :--------- | :------------- | :------------- |:----------- |
@foreach ($data as $item)
| {{$item->name}} | {{$item->node_type ?? 'PREMIUM'}} | {{$item->year_length}} | ${{number_format($item->total_amount,2)}} |
@endforeach
@endcomponent

<hr>

@if ($summaryData->registration_subtotal > 0)
Registration Subtotal: ${{number_format($summaryData->registration_subtotal,2)}}
@endif

@if ($summaryData->premium_subtotal > 0)
Premium Subtotal: ${{number_format($summaryData->premium_subtotal,2)}}
@endif

@if ($summaryData->subtotal > 0)
Subtotal: ${{number_format($summaryData->subtotal,2)}}
@endif

@if ($data[0]->redemption_fee > 0)
## Total Redemption Fee: ${{number_format($data[0]->redemption_fee,2)}}
@endif

Miscellaneous Fees: ${{number_format($summaryData->total_fees,2)}} 

<hr>

## Total Paid: ${{number_format($paidAmount,2)}}

##### For more information, please see {{ config('app.name') }}'s <a href="{{$termsUrl}}">Terms and Conditions</a>.<br>
</x-mail::message>