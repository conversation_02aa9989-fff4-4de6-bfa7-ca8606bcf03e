<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ReportAbuseMessage extends Mailable
{
    use Queueable, SerializesModels;

    private $payload;

    /**
     * Create a new message instance.
     */
    public function __construct(array $payload)
    {
        $this->payload = $payload;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address($this->payload['sender'], $this->payload['name']),
            subject: "Client's Abuse Report",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.ReportAbuseQuery',
            with: [
                'sender' => $this->payload['sender'],
                'name' => $this->payload['name'],
                'domain' => $this->payload['domain'],
                'category' => $this->payload['category'],
                'body' => $this->payload['body'],
                'attachmentPath' => $this->payload['attachmentPath'],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        if (!empty($this->payload['attachmentPath'])) {
            $attachments[] = \Illuminate\Mail\Mailables\Attachment::fromPath(
                storage_path('app/' . $this->payload['attachmentPath'])
            );
        }
        return $attachments;
    }
}
