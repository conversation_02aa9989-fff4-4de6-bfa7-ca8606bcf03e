<?php

namespace App\Modules\Category\Requests;

use App\Modules\Category\Services\CategoryService;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name.*' => [
                'required',
                'string',
                'min:3',
                'max:20',
                'distinct',
                'regex:/^[a-zA-Z0-9\s\-\'"]*$/',
                Rule::unique('user_categories', 'name')
                    ->where('user_id', Auth::user()->id)
                    ->whereNull('deleted_at'),
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name.*' => 'name',
        ];
    }

    public function store()
    {
        CategoryService::instance()->store($this->name);
    }
}
