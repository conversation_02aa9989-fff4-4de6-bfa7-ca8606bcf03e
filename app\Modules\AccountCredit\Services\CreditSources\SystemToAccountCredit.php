<?php

namespace App\Modules\AccountCredit\Services\CreditSources;

use App\Events\ClientActivityEvent;
use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Exceptions\FailedRequestException;
use App\Models\User;
use App\Modules\AccountCredit\Constants\AccountCreditSourceType;
use App\Modules\AccountCredit\Constants\AccountCreditType;
use App\Modules\AccountCredit\Contracts\AccountCreditInterface;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentService\Services\PaymentServiceHelper;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\SystemCredit\Constants\SystemCreditType;
use App\Modules\SystemCredit\Services\SystemCreditService;

class SystemToAccountCredit implements AccountCreditInterface
{
    public function store(array $data, int $userId, string $paymentServiceType)
    {
        $user = User::findOrFail($userId);
        $clientInvite = SystemCreditService::instance()->getUserInvite($user);
        $this->checkGenesisBlock($user);
        $clienInviteBalance = SystemCreditService::instance()->getClientInviteBalance($clientInvite, $user->email);
        $systemCreditData = $this->getSystemCreditData($user, $clientInvite);

        $createdPaymentService = PaymentServiceHelper::instance()
            ->pay($systemCreditData, $user->id, PaymentServiceType::SYSTEM_CREDIT);
        $paymentServiceObj = is_array($createdPaymentService) ? $createdPaymentService['payment_service'] : $createdPaymentService;
        $this->createClientActivityEvent($userId, $clienInviteBalance, $paymentServiceObj->id);

        return [
            'payload' => [
                'source_id' => $paymentServiceObj->system_credit_id,
                'payment_service_id' => $paymentServiceObj->id,
            ],
            'userId' => $userId,
            'summaryType' => PaymentSummaryType::ACCOUNT_BALANCE,
            'paymentServiceType' => PaymentServiceType::SYSTEM_CREDIT,
        ];
    }

    public function deposit(int $sourceId, int $userId)
    {
        $user = User::findOrFail($userId);
        $userInvite = SystemCreditService::instance()->getUserInvite($user);
        $this->checkGenesisBlock($user);
        $accountCreditPaymentService = $this->depositToAccountBalance($sourceId, $user, $userInvite);

        return $accountCreditPaymentService;
    }

    public function credit(int $sourceId, int $userId)
    {
        throw new FailedRequestException(400, 'Credit not supported.', 'Error');
    }

    public function createPaymentSummary(object $paymentService, int $userId)
    {
        $sourceId = $paymentService->system_credit_id;
        $systemCredit = SystemCreditService::instance()->getSystemCreditById($sourceId, $userId);
        $paymentServiceType = PaymentServiceType::SYSTEM_CREDIT;

        $data = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::ACCOUNT_BALANCE],
            'paid_amount' => $systemCredit->amount,
            'total_amount' => $systemCredit->amount,
            'payment_service_id' => $paymentService->id,
            'source' => $paymentServiceType,
        ];

        event(new CreatePaymentSummaryEvent($data, $userId, PaymentSummaryType::ACCOUNT_BALANCE));
    }

    // PRIVATE FUNCTIONS

    private function depositToAccountBalance(int $sourceId, object $user, object $clientInvite)
    {
        $clienInviteBalance = $this->getClientInviteBalance($clientInvite, $user->email);
        $this->checkSystemCredit($sourceId, $user->id, $clienInviteBalance);

        $data = [
            'user_id' => $user->id,
            'type' => AccountCreditType::DEBIT,
            'amount' => $clienInviteBalance,
            'sourceId' => $sourceId,
            'sourceType' => AccountCreditSourceType::SYSTEM_CREDIT,
        ];

        $createdPaymentService = PaymentServiceHelper::instance()
            ->pay($data, $user->id, PaymentServiceType::ACCOUNT_DEPOSIT);

        return $createdPaymentService;
    }

    private function checkSystemCredit(int $sourceId, int $userId, float $amount)
    {
        $systemCreditData = SystemCreditService::instance()->getSystemCreditById($sourceId, $userId);
        if (! $systemCreditData) {
            throw new FailedRequestException(400, 'System credit not found', 'Bad request');
        }

        if (floatval($systemCreditData->amount) !== floatval($amount)) {
            throw new FailedRequestException(400, 'System credit not the same amount', 'Bad request');
        }
    }

    private function checkGenesisBlock(object $user)
    {
        $previousBlock = AccountCreditService::instance()->getLatestBlock($user->id);

        if (! $previousBlock) {
            AdminNotificationService::instance()->sendNoGenesisBlockFound(['email' => $user->email]);
            throw new FailedRequestException(400, 'Genesis block not found for this user: '.$user->email, 'Bad request');
        }
    }

    private function getClientInviteBalance(object $clientInvite, string $email)
    {
        $options = json_decode($clientInvite->additional_options);

        if (! isset($options->balance)) {
            throw new FailedRequestException(400, 'No system credit balance given for this user: '.$email, 'Bad request');
        }

        if (! $options->balance) {
            throw new FailedRequestException(400, 'No system credit balance given for this user: '.$email, 'Bad request');
        }

        return $options->balance;
    }

    private function getSystemCreditData(object $user, object $clientInvite)
    {
        $clienInviteBalance = SystemCreditService::instance()->getClientInviteBalance($clientInvite, $user->email);

        return [
            'type' => SystemCreditType::CREDIT,
            'amount' => $clienInviteBalance ?? 0,
            'note' => 'System credit of $'.$clienInviteBalance.' from user invite for: '.$user->email,
            'user_id' => $user->id,
            'email' => $user->email,
        ];
    }

    private function createClientActivityEvent(int $userId, mixed $clienInviteBalance, int $createdPaymentServiceId)
    {
        $message = 'Added $'.$clienInviteBalance.' to account balance via '.PaymentServiceType::TEXT[PaymentServiceType::SYSTEM_CREDIT].'.';

        $clientData = [
            'name' => PaymentSummaryType::TEXT[PaymentSummaryType::ACCOUNT_BALANCE],
            'paid_amount' => $clienInviteBalance ?? 0,
            'total_amount' => $clienInviteBalance ?? 0,
            'payment_service_id' => $createdPaymentServiceId,
            'source' => PaymentServiceType::SYSTEM_CREDIT,
        ];

        event(new ClientActivityEvent(
            $userId,
            UserTransactionType::PAYMENT_SUMMARY,
            $message,
            '',
            $clientData,
        ));
    }
}
