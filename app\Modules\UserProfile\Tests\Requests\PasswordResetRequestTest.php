<?php
namespace App\Modules\UserProfile\Tests\Requests;

use App\Modules\UserProfile\Requests\PasswordResetRequest;
use App\Modules\UserProfile\Tests\Datasets\CommonValidationDataset;
use Illuminate\Support\Facades\Validator;

it('passes validation with valid email', function () {
    $request = new PasswordResetRequest;

    $validator = Validator::make(['email' => '<EMAIL>'], $request->rules());

    expect($validator->passes())->toBeTrue(json_encode($validator->errors()));
});

it('fails validation when required fields are missing', function () {
    $request = new PasswordResetRequest;

    $validator = Validator::make([], $request->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue("Failed to validate required field for email");
});

it('fails validation when email field has invalid data', function () {
    $request = new PasswordResetRequest;
    $invalidDatasets = CommonValidationDataset::invalidEmailData();

    foreach ($invalidDatasets as $testName => $data) {
        $validator = Validator::make($data, $request->rules());

        foreach (array_keys((array) $data) as $key) {
            expect($validator->errors()->has($key))->toBeTrue("Failed to validate {$key} field for test case: {$testName}");
        }
        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
    }
});