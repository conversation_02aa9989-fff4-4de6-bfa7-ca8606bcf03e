<?php

namespace App\Modules\UserProfile\Tests\Requests;

require 'App/Modules/UserProfile/Tests/Datasets/CommonValidationDataset.php';
use App\Modules\UserProfile\Requests\PasswordUpdateRequest;

it('has correct validation rules structure', function () {
    $request = new PasswordUpdateRequest();
    $rules = $request->rules();

    expect($rules)->toHave<PERSON>ey('current_password');
    expect($rules)->toHave<PERSON>ey('password');
    expect($rules)->toHaveKey('password_confirmation');

    expect($rules['current_password'])->toContain('required');
    expect($rules['password'])->toContain('required');
    expect($rules['password_confirmation'])->toContain('required');
});

it('has correct validation messages', function () {
    $request = new PasswordUpdateRequest();
    $messages = $request->messages();

    expect($messages)->toHave<PERSON>ey('password.different');
    expect($messages)->toHave<PERSON>ey('password.max');
    expect($messages)->toHave<PERSON><PERSON>('current_password.required');
    expect($messages)->toHave<PERSON><PERSON>('password.required');
    expect($messages)->toHaveKey('password_confirmation.required');

    expect($messages['password.different'])->toBe('The password field and current password must be different.');
    expect($messages['current_password.required'])->toBe('Please enter your current password.');
});

it('password field has max length validation', function () {
    $request = new PasswordUpdateRequest();
    $rules = $request->rules();

    expect($rules['password'])->toContain('max:100');
});

it('password field has string validation', function () {
    $request = new PasswordUpdateRequest();
    $rules = $request->rules();

    expect($rules['password'])->toContain('string');
});

it('password field has different validation from current password', function () {
    $request = new PasswordUpdateRequest();
    $rules = $request->rules();

    expect($rules['password'])->toContain('different:current_password');
});

it('current password field has current_password validation', function () {
    $request = new PasswordUpdateRequest();
    $rules = $request->rules();

    expect($rules['current_password'])->toContain('current_password');
});

it('password confirmation field has required validation', function () {
    $request = new PasswordUpdateRequest();
    $rules = $request->rules();

    expect($rules['password_confirmation'])->toContain('required');
    expect($rules['password_confirmation'])->toContain('string');
});