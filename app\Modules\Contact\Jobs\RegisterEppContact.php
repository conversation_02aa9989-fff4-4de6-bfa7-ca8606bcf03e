<?php

namespace App\Modules\Contact\Jobs;

use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\Contact\Services\JobContactService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class RegisterEppContact implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(string $userContactId, array $eppData, array $defaultContacts, string $registry, string $registryId, string $userId, string $email)
    {
        $this->params = [
            'userContactId' => $userContactId,
            'eppData' => $eppData,
            'defaultContacts' => $defaultContacts,
            'registry' => $registry,
            'registryId' => $registryId,
            'userId' => $userId,
            'email' => $email,
        ];

        $this->onConnection(QueueConnection::CONTACT_REGISTRATION);
        $this->onQueue(QueueTypes::CONTACT_CREATE[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return $this->params['userContactId'];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            JobContactService::instance()->store($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $this->params['email']));

            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();

                return;
            }

            $this->fail();
        }
    }

    public function failed(?Throwable $exception): void
    {
        // Send user notification of failure, etc...
    }

    public function retry(): void
    {
        $status = ContactStatus::IN_PROCESS;
        $type = QueueConnection::CONTACT_REGISTRATION;
        $jobId = $this->params['userContactId'];

        JobContactService::instance()->addRetryLogs($type, $status, $this->params, $jobId);
    }
}
