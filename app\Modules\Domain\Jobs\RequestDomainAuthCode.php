<?php

namespace App\Modules\Domain\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\JobServices\JobAuthCodeService;
use App\Util\Constant\QueueConnection;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class RequestDomainAuthCode implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $id;

    private $email;

    private $domains;

    private $domains_for_update;

    private $userId;

    private $maxDomainsPerEmail;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct($email, $domains, $domains_for_update, $userId, $maxDomainsPerEmail)
    {
        $this->id = $userId;
        $this->email = $email;
        $this->domains = $domains;
        $this->domains_for_update = $domains_for_update;
        $this->userId = $userId;
        $this->maxDomainsPerEmail = $maxDomainsPerEmail;

        $this->onConnection(QueueConnection::DOMAIN_AUTHCODE_REQUEST);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            JobAuthCodeService::instance()->sendAuthCode($this->email, $this->domains, $this->domains_for_update, $this->userId, $this->maxDomainsPerEmail);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            $this->fail();
        }
    }

    public function failed(Throwable $exception): void
    {
        // Send user notification of failure, etc...
    }
}
