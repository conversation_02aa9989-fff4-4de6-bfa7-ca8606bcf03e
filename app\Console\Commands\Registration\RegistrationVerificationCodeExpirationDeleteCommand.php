<?php

namespace App\Console\Commands\Registration;

use App\Models\RegistrationEmailVerificationCode; 

use Illuminate\Console\Command;

class RegistrationVerificationCodeExpirationDeleteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:registration-verification-code-expiration-delete-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Expired Registration Verification Codes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        RegistrationEmailVerificationCode::query()
            ->where('valid_until', '<', now())
            ->delete();
    }
}
