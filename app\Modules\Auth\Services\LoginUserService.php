<?php

namespace App\Modules\Auth\Services;

use App\Models\User;
use App\Modules\Auth\Requests\LoginRequest;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class LoginUserService
{
    public static function validate(LoginRequest $request)
    {
        self::allowed($request);
        self::is_active($request);
        self::attempt($request);
    }

    public static function attempt(LoginRequest $request)
    {
        if (! Auth::attempt($request->only('email', 'password'), $request->boolean('remember'))) {
            RateLimiter::hit($request->throttleKey());
            app(AuthLogger::class)->info($request->email.' failed attempt.');

            throw ValidationException::withMessages([
                'message' => trans('auth.failed'),
            ]);
        }
    }

    public static function allowed(LoginRequest $request)
    {

        $validEmail = true;
        $errorKey = 'message';
        $errorMessage = '';
        $clientIp = ClientIp::getClientIp($request);

        $user = User::where('email', '=', $request->email)->first();

        if ($user == null) {
            $validEmail = false;
            $errorMessage = trans('auth.failed');
        }

        // $email = User::join('user_ips', 'user_ips.user_id', '=', 'users.id')
        //     ->join('ips', 'ips.id', '=', 'user_ips.ip_id')
        //     ->where('users.email', $request->email);

        // if ($email->first() == null) {

        //     $validEmail = false;
        //     $errorMessage = trans('auth.failed');
        // }

        // if ($validEmail)
        // {
        //     $user = $email->where('ips.ip', $clientIp)
        //         ->where('ips.is_active', true)
        //         ->select('users.id as user_id')
        //         ->first();

        //     if ($user == null) {
        //         $validEmail = false;
        //         $errorKey = 'ip';
        //         $errorMessage = 'Your IP does not match our records. Register your IP';
        //     }
        // }

        if (! $validEmail) {
            RateLimiter::hit($request->throttleKey());

            throw ValidationException::withMessages([
                $errorKey => $errorMessage,
            ]);
        }
    }

    public static function is_active(LoginRequest $request)
    {
        $user = User::where('email', $request->email)
            ->where('is_active', true)
            ->first();

        if ($user == null) {
            RateLimiter::hit($request->throttleKey());

            throw ValidationException::withMessages([
                'message' => 'Sorry, this email ('.$request->email.') is currently disabled.',
            ]);
        }
    }
}
