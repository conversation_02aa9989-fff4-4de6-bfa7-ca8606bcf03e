<?php

require_once 'app\Modules\Contact\Tests\Helper.php';

use Illuminate\Support\Facades\Validator;
use App\Modules\Contact\Requests\CreateContactRequest;
use App\Modules\Contact\Tests\Datasets\ContactValidationDataset;



it('passes validation with valid data', function () {
    $data = ContactValidationDataset::validContactData();

    $request = new CreateContactRequest;

    $request->registry = $data['registry'];

    $validator = Validator::make($data, $request->rules());

    expect($validator->passes())->toBeTrue(json_encode($validator->errors()));
});


it('fails validation when required fields are missing', function () {
    $request = new CreateContactRequest;

    $request->registry = 'invalid';

    $validator = Validator::make([], $request->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('registry'))->toBeTrue("Failed to validate required failed for registry");
    expect($validator->errors()->has('contact_name'))->toBeTrue("Failed to validate required failed for contact_name");
    expect($validator->errors()->has('organization_name'))->toBeTrue('Failed to validate required failed for organization_name');
    expect($validator->errors()->has('voice_number'))->toBeTrue('Failed to validate required failed for voice_number');
    expect($validator->errors()->has('email'))->toBeTrue('Failed to validate required failed for email');
    expect($validator->errors()->has('street'))->toBeTrue('Failed to validate required failed for street');
    expect($validator->errors()->has('city'))->toBeTrue('Failed to validate required failed for city');
    expect($validator->errors()->has('state_province'))->toBeTrue('Failed to validate required failed for state_province');
    expect($validator->errors()->has('postal_code'))->toBeTrue('Failed to validate required failed for postal_code');
    expect($validator->errors()->has('country_code'))->toBeTrue('Failed to validate required failed for country_code');
    expect($validator->errors()->has('ext_voice_number'))->toBeTrue('Failed to validate required failed for ext_voice_number');
});

it('fails validation when contact name already exist', function () {
    $request = new CreateContactRequest;
    $contact_name = "qwe";
    $registry = createTestRegistry('verisign');
    $contact = createTestContact(['name' => $contact_name]);
    $userContact = createTestUserContact($this->user->id, $contact->id, $registry->id, []);

    $data = [
        'contact_name' => $contact_name,
        'registry' => $registry->name
    ];
    $request->registry = $data['registry'];

    $validator = Validator::make($data, $request->rules());

    expect($validator->errors()->has('contact_name'))->toBeTrue("Failed to validate contact_name field");
    expect($validator->fails())->toBeTrue();
});


testInvalidContactData(new CreateContactRequest(), ContactValidationDataset::invalidContactData());
