<?php

namespace App\Listeners;

use App\Events\SystemLogEvent;
use App\Models\SystemTransactionHistory;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SystemLogListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(SystemLogEvent $event): void
    {
        SystemTransactionHistory::create([
            'type' => $event->type,
            'message' => $event->message,
            'link' => $event->link,
        ]);
    }
}
