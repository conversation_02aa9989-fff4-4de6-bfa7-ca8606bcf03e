<?php

namespace App\Http\Middleware;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Helper\Client\ClientIp;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminAccessFilterMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        // Define the list of allowed IP addresses
        $allowedIPs =
            explode(',', trim(env('ALLOWED_LOG_ACCESS_IP')));
        // Add more IP addresses as needed

        // Get the current client IP address
        $clientIP = ClientIp::getClientIp($request);

        // Check if the client IP is in the allowed list
        if (! in_array($clientIP, $allowedIPs)) {
            // IP not allowed, return a response with an error message or redirect to a specific route
            $message = 'Access denied.';
            $errorMsg = 'Admin '.$clientIP. ' : '. $message;
            app(AuthLogger::class)->error($errorMsg);
            
            return response()->json(['message' => $message], 403);
        }

        return $next($request);
    }
}
