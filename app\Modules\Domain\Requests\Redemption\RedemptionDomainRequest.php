<?php

namespace App\Modules\Domain\Requests\Redemption;

use App\Exceptions\FailedRequestException;
use App\Modules\Cart\Services\CheckoutCartService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\UpdateServices\RedemptionDomainService;
use App\Modules\Payment\Constants\CheckoutType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\TransactionThreshold\Services\TransactionThresholdService;
use App\Rules\Domain\DomainRedemptionArrayExists;
use App\Rules\Payment\ValidateOtherFees;
use App\Rules\Payment\ValidateStripeFees;
use App\Util\Constant\Transaction;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RedemptionDomainRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return $this->getValidationRules();
    }

    public function prepareForValidation()
    {
        $this->merge(['payment_summary_type' => PaymentSummaryType::PAYMENT_INVOICE]);
    }

    public function passedValidation()
    {
        if ($this->payment_service_type === PaymentServiceType::ACCOUNT_CREDIT) {
            CheckoutCartService::instance()->checkAccountCreditBalance($this->amount_to_use);
            $this->cancelIntent();
        }

        RedemptionDomainService::instance()->checkAndCreditRegistryBalance($this->all());
        TransactionThresholdService::instance()->validateTransaction(Transaction::REDEEM, $this->other_fees['domain_count'], $this->intent);

        $this->captureIntent();
    }

    protected function failedValidation(Validator $validator)
    {
        $this->cancelIntent();
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    public function update(): string
    {
        return RedemptionDomainService::instance()->update($this->all());
    }

    private function captureIntent()
    {
        if ($this->payment_service_type === PaymentServiceType::STRIPE) {
            PaymentIntentProvider::instance()->captureIntent($this->intent);
        }
    }

    private function cancelIntent()
    {
        if ($this->intent) {
            PaymentIntentProvider::instance()->cancelIntent($this->intent);
        }
    }

    private function getValidationRules(): array
    {
        $rules = [
            'domains' => ['required', 'array', 'min:1', new DomainRedemptionArrayExists],
            'other_fees' => ['required', 'array', 'min:1', new ValidateOtherFees(CheckoutType::REDEMPTION)],
            'payment_service_type' => ['required', Rule::in([PaymentServiceType::STRIPE, PaymentServiceType::ACCOUNT_CREDIT])],
            'payment_summary_type' => ['required', Rule::in(PaymentSummaryType::ALL)],
        ];

        return match ($this->payment_service_type) {
            PaymentServiceType::STRIPE => array_merge(
                $rules,
                [
                    'intent' => ['required', 'string'],
                    'stripe_fees' => ['required', 'array', 'min:1', new ValidateStripeFees],
                ]
            ),
            PaymentServiceType::ACCOUNT_CREDIT => array_merge($rules, ['amount_to_use' => ['required', 'numeric']]),
            default => $rules
        };
    }
}
