<?php

namespace App\Modules\Stripe\Helpers;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Stripe\Constants\StripeFees;
use App\Modules\Stripe\Constants\StripeFeeType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Providers\PaymentProvider;

class StripeFeeHelper
{
    use UserLoggerTrait;

    public static function calculateTransactionFee(float $goalAmount, string $type = StripeFeeType::INTERNATIONAL_CARD)
    {
        $percentFee = self::getPercentFee($type);
        $amountWithFee = $goalAmount + StripeFees::FIXED_FEE;

        $grossAmount = $amountWithFee / $percentFee;
        $serviceFee = $grossAmount - $goalAmount;

        return [
            'net_amount' => round($goalAmount, 2) ?? 0,
            'gross_amount' => round($grossAmount, 2) ?? 0,
            'service_fee' => round($serviceFee, 2) ?? 0,
        ];
    }

    public static function getStripeIntentById(string $paymentIntent)
    {
        $paymentIntentDetails = PaymentIntentProvider::instance()->retrieveIntent($paymentIntent);

        $balance = self::getBalanceTransaction($paymentIntentDetails);
        $grossAmount = self::convertFromStripeAmount($balance->amount ?? 0);
        $netAmount = self::convertFromStripeAmount($balance->net ?? 0);
        $serviceFee = self::convertFromStripeAmount($balance->fee ?? 0);
        $chargeId = $balance->source ?? '';

        app(AuthLogger::class)->info('getStripeIntentById paymentIntentDetails:: '.json_encode($paymentIntentDetails));
        app(AuthLogger::class)->info('getStripeIntentById balance:: '.json_encode($balance));

        return [
            'gross_amount' => $grossAmount,
            'net_amount' => $netAmount,
            'service_fee' => $serviceFee,
            'charge_id' => $chargeId,
            'payment_intent' => $paymentIntent,
        ];
    }

    public static function convertFromStripeAmount(int $amount)
    {
        return round($amount / 100, 2);
    }

    public static function getStripeAmount(float $amount)
    {
        return round($amount * 100, 2);
    }

    public static function getPercentFee(string $type)
    {
        switch ($type) {
            case StripeFeeType::INTERNATIONAL_CARD:
                return 1 - StripeFees::LOCAL_CARD_FEE - StripeFees::INTL_CARD_FEE;
            case StripeFeeType::INTL_CARD_CONVERSION:
                return 1 - StripeFees::LOCAL_CARD_FEE - StripeFees::INTL_CARD_FEE - StripeFees::CONVERSION_FEE;
            case StripeFeeType::LOCAL_CARD:
                return 1 - StripeFees::LOCAL_CARD_FEE;
            default:
                return 1 - StripeFees::LOCAL_CARD_FEE;
        }
    }

    private static function getBalanceTransaction(object $paymentIntentDetails)
    {
        $balance = $paymentIntentDetails->latest_charge->balance_transaction;
        if ($balance != null) {
            return $balance;
        }

        $chargeId = $paymentIntentDetails->latest_charge->id;

        $chargeDetails = PaymentProvider::instance()->retrieveCharge($chargeId);
        app(AuthLogger::class)->info('getBalanceTransaction chargeDetails:: '.json_encode($chargeDetails));
        $balanceId = $chargeDetails->balance_transaction;
        $balanceDetails = PaymentProvider::instance()->retrieveBalanceTransaction($balanceId);
        app(AuthLogger::class)->info('getBalanceTransaction balanceDetails:: '.json_encode($balanceDetails));

        return $balanceDetails;
    }
}
