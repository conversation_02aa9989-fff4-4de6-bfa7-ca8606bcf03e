<?php

namespace App\Listeners;

use App\Events\UserLoginEvent;
use App\Modules\Client\Jobs\ClientAccountMaintenance;

class UpdateExpiredPushRequests
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserLoginEvent $event): void
    {
        ClientAccountMaintenance::dispatch($event->user, $event->email);
    }
}
