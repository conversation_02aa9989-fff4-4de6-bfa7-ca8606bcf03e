<?php

namespace App\Listeners\Payment;

use App\Events\ClientActivityEvent;
use App\Events\Payment\GenesisAccountBalanceEvent;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Histories\Constants\UserTransactionType;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class GenesisAccountBalanceListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(GenesisAccountBalanceEvent $event)
    {
        try {
            $previousBlock = AccountCreditService::instance()->getLatestBlock($event->userId);

            if ($previousBlock) {
                return;
            }

            $this->addGenesisBlock($event->userId, $previousBlock);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }

    private function addGenesisBlock(int $userId, $previousBlock)
    {
        AccountCreditService::instance()->createGenesis($userId);
        event(new ClientActivityEvent(
            $userId,
            UserTransactionType::PAYMENT_SUMMARY,
            'Account credit block genesis created.',
            '',
            $previousBlock ?? null
        ));
    }
}
