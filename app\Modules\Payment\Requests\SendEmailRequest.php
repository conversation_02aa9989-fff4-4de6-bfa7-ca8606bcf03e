<?php

namespace App\Modules\Payment\Requests;

use App\Modules\Payment\Jobs\EmailPaymentInvoice;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SendEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required'],
        ];
    }

    public function send()
    {
        EmailPaymentInvoice::dispatch($this->id, Auth::user()->id)
            ->onConnection(QueueConnection::PAYMENT_JOB)
            ->onQueue(QueueTypes::PAYMENT_EMAIL);
    }
}
