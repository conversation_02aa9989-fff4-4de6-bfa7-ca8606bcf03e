<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Services\MarketDomainService;
use Inertia\Inertia;
use Inertia\Response;

class MarketDomainController extends Controller
{
    public function index(): Response
    {
        return Inertia::render('Market/Domains/Domains', MarketDomainService::instance()->getAllDomains());
    }
}
