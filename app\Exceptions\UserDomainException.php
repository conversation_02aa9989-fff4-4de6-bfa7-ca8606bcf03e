<?php

namespace App\Exceptions;

use Exception;
use Inertia\Inertia;

class UserDomainException extends Exception
{
    public function __construct($code, $help, $error)
    {
        $this->code = $code;
        $this->message = $help;
        $this->file = $error;
    }

    public function render()
    {
        return Inertia::render('Notice/DomainUnsuccessMessage', [
            'code' => $this->code,
            'help' => $this->message,
            'message' => $this->message,
            'error' => $this->file,
            'errors' => $this->file,
        ]);
    }

    /**
     * Report the exception.
     */
    public function report(): bool
    {
        return true;
    }
}
