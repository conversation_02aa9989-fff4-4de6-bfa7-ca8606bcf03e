<?php

namespace App\Modules\DomainClassificationApi\Jobs;

use App\Models\User;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\DomainClassificationApi\Services\DomainClassificationApiService;
use App\Util\Constant\QueueConnection;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class DomainClassificationApiInsertClientDomainsJob implements ShouldBeUnique, ShouldQueue
{
    use Queueable;

    private int $userId;

    public int $uniqueFor = 3600;

    /**
     * Create a new job instance.
     */
    public function __construct(int $userId)
    {
        $this->userId = $userId;

        $this->onConnection(QueueConnection::DOMAIN_CLASSIFICATION_JOBS);
        $this->onQueue('insert');
    }

    /**
     * Job Unique Id
     */
    public function uniqueId(): int
    {
        return intval(now()->timestamp.$this->userId);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $chunkSize = 100; 

        $user = User::findOrFail($this->userId);

        $domains = DB::table('domains')
            ->select('domains.name')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('registries', 'registries.id', '=', 'user_contacts.registry_id')
            ->whereNull('domains.deleted_at')
            ->where('user_contacts.user_id', $user->id)
            ->where('registered_domains.status', UserDomainStatus::OWNED)
            ->pluck('domains.name')
            ->toArray();

        //! BREAK INTO CHUNKS
        $chunks = array_chunk($domains, $chunkSize);

        //! PROCESS EACH CHUNK
        foreach ($chunks as $chunk) 
        {
            (new DomainClassificationApiService)->bulkInsertDomain($chunk, $user->email);
        }
    }
}
