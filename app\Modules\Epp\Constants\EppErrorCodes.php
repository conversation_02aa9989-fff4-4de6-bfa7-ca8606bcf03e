<?php

namespace App\Modules\Epp\Constants;

final class EppErrorCodes
{
    public const MISSING_PARAM = '2003';

    public const INVALID_FORMAT = '2005';

    public const BILLING_FAILURE = '2104';

    public const AUTHORIZATION_ERROR = '2201';

    public const INVALID_AUTH_CODE = '2202';

    public const PENDING_STATE_ERROR = '2300';

    public const DOES_NOT_EXIST = '2303';

    public const LOCKED_ERROR = '2304';

    public const ALL = [
        self::MISSING_PARAM,
        self::INVALID_FORMAT,
        self::BILLING_FAILURE,
        self::AUTHORIZATION_ERROR,
        self::INVALID_AUTH_CODE,
        self::PENDING_STATE_ERROR,
        self::DOES_NOT_EXIST,
        self::LOCKED_ERROR,
    ];

    public const ERROR_MESSSAGE = [
        EppErrorCodes::DOES_NOT_EXIST => 'does not exist',
        EppErrorCodes::PENDING_STATE_ERROR => 'is already in a pending state',
    ];
}
