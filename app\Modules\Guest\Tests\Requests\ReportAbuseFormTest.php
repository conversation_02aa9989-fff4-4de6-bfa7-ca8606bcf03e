<?php

namespace App\Modules\Guest\Tests\Requests;

use App\Models\Domain;
use App\Modules\Guest\Requests\ReportAbuseForm;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class)->in(__DIR__);

beforeEach(function() {
    Domain::factory()->create([
        'name' => 'example.com',
        'server_renew_at' => now(),
        'client_renew_at' => now()
    ]);
});

it('fails validation when required fields are missing', function () {
    $validator = Validator::make([], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue();
    expect($validator->errors()->has('domain'))->toBeTrue();
    expect($validator->errors()->has('message'))->toBeTrue();
    expect($validator->errors()->has('category'))->toBeTrue();
});

it('fails validation with invalid name format', function () {
    $validator = Validator::make([
        'name' => 'a',
        'email' => '<EMAIL>',
        'domain' => 'example.com',
        'message' => 'This is a valid message',
        'category' => 'spam'
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
});

it('fails validation with invalid email', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => 'invalid-email',
        'domain' => 'example.com',
        'message' => 'This is a valid message',
        'category' => 'spam'
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue();
});

it('fails validation with invalid domain format', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'domain' => 6,
        'message' => 'This is a valid message',
        'category' => 'spam'
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('domain'))->toBeTrue();
});

it('fails validation with short message', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'domain' => 'example.com',
        'message' => 'short',
        'category' => 'spam'
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('message'))->toBeTrue();
});

it('fails validation with invalid category', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'domain' => 'example.com',
        'message' => 'This is a valid message',
        'category' => 'invalid-category'
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('category'))->toBeTrue();
});

it('fails validation with invalid attachment type', function () {
    $file = UploadedFile::fake()->create('document.doc', 100);
    
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'domain' => 'example.com',
        'message' => 'This is a valid message',
        'category' => 'spam',
        'attachment' => $file
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('attachment'))->toBeTrue();
});

it('passes validation with valid data without attachment', function () {
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'domain' => 'example.com',
        'message' => 'This is a valid message for testing purposes',
        'category' => 'spam'
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeFalse();
});

it('passes validation with valid data including attachment', function () {
    $file = UploadedFile::fake()->image('screenshot.jpg', 100, 100);
    
    $validator = Validator::make([
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'domain' => 'example.com',
        'message' => 'This is a valid message for testing purposes',
        'category' => 'spam',
        'attachment' => $file
    ], (new ReportAbuseForm)->rules());

    expect($validator->fails())->toBeFalse();
});

it('validates domain existence', function () {
    $validator = Validator::make([
        'domain' => 'nonexistent-domain.com'
    ], ['domain' => ['required', 'exists:domains,name']]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('domain'))->toBe('The selected domain is invalid.');

    DB::statement("
        INSERT INTO domains (name, status, root, registrant, year_length, expiry, server_renew_at, client_renew_at, created_at, updated_at) 
        VALUES ('existing-domain.com', 'ACTIVE', '1', '1', 1, extract(epoch from now() + interval '1 year')::bigint, now(), now(), now(), now())
    ");

    $validator = Validator::make([
        'domain' => 'existing-domain.com'
    ], ['domain' => ['required', 'exists:domains,name']]);

    expect($validator->fails())->toBeFalse();
});