import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import SearchNameFilter from "@/Components/Util/Filter/SearchNameFilter";

export default function SearchName({
    hasContactSearch = false,
    setHasContactSearch,
    setContactSearchValue,
    searchedContacts = ""
}) {
    const handleRemoveSearch = () => {
        setHasContactSearch(false);
        setContactSearchValue('contacts', '');
        router.get(route('contact'));
    };

    const handleSearch = (e) => {
        const value = e.target.value;
        setContactSearchValue('contacts', value);
    };

    const handleSubmitSearch = () => {
        const searchTerm = searchedContacts.trim();

        if (!searchTerm) {
            toast.error("Please enter a name to search.");
            return;
        }

        let payload = {
            contact: searchTerm
        };

        // Email search functionality - uncomment if needed in the future
        // if (searchTerm.includes('@')) {
        //     payload = { email: searchTerm };
        // } else {
        //     payload = { contact: searchTerm };
        // }

        router.get(route('contact'), payload);
        setHasContactSearch(true);
        toast.info("Searching contacts, please wait...");
    };

    const handleOutsideClick = () => {
        if (searchedContacts.trim() && !hasContactSearch) {
            handleSubmitSearch();
        }
    };

    return (
        <SearchNameFilter
            value={searchedContacts}
            onChange={handleSearch}
            onClear={handleRemoveSearch}
            onSubmit={handleSubmitSearch}
            onOutsideClick={handleOutsideClick}
            hasSearch={hasContactSearch}
        />
    );
} 