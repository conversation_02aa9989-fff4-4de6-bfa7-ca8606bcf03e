<?php

require_once 'app\Modules\Contact\Tests\Helper.php';

use Illuminate\Support\Facades\Validator;
use App\Modules\Contact\Requests\ShowListRequest;
use App\Modules\Contact\Tests\Datasets\ContactValidationDataset;

it('passes validation with valid data', function () {
    $data = ContactValidationDataset::invalidShowListData();
    $request = new ShowListRequest;

    $validator = Validator::make($data, $request->rules());

    expect($validator->passes())->toBeTrue(json_encode($validator->errors()));
});


testInvalidContactData(new ShowListRequest(), ContactValidationDataset::invalidShowListData());