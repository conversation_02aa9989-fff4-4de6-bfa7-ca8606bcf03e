<?php

namespace App\Modules\Auth\Services;

use App\Events\ClientActivityEvent;
use App\Exceptions\FailedRequestException;
use App\Models\User;
use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\Auth\Constants\InviteStatus;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\Stripe\Constants\StripeIdentityEvent;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class RegisterUserService
{
    public static function store($request)
    {
        $user = new User;
        $user->first_name = $request->first_name;
        $user->last_name = $request->last_name;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->street = $request->street;
        $user->city = $request->city;
        $user->state_province = $request->state_province;
        $user->country_code = $request->country_code;
        $user->postal_code = $request->postal_code;
        $user->save();

        // send notif
        AdminNotificationService::instance()->sendClientRegistrationNotif(['email' => $request->email]);

        event(new Registered($user));

        $payload = collect($request->all())->except(['password'])->toArray();

        event(new ClientActivityEvent($user->id, UserTransactionType::REGISTER, "New user registered with email {$user->email} from {$request->country_code}", '', $payload));

        return $user;
    }

    public static function updateUser($request)
    {
        $user = User::findOrFail($request->user_id);

        $user->first_name = $request->first_name;
        $user->last_name = $request->last_name;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->street = $request->street;
        $user->city = $request->city;
        $user->state_province = $request->state_province;
        $user->country_code = $request->country_code;
        $user->postal_code = $request->postal_code;
        $user->is_active = true;

        // Save changes
        $user->save();

        // send notif
        AdminNotificationService::instance()->sendClientRegistrationNotif(['email' => $request->email]);

        event(new Registered($user));

        $payload = collect($request->all())->except(['password'])->toArray();

        event(new ClientActivityEvent($user->id, UserTransactionType::REGISTER, "User {$user->email} completed registration from invitation", '', $payload));

        return $user;
    }

    public static function whitelistIp($clientIp)
    {

        $whitelistedIp = DB::table('ips')->where('ip', $clientIp)->first();

        if ($whitelistedIp == null) {
            $whitelistedIp = DB::table('ips')->insertGetId(['ip' => $clientIp, 'is_active' => true, 'updated_at' => now(), 'created_at' => now()]);
        } elseif (strcmp($whitelistedIp->is_active, true) == 0) {
            $whitelistedIp = $whitelistedIp->id;
        } else {
            throw ValidationException::withMessages(['message' => $clientIp.' is blocked']);
        }

        return $whitelistedIp;
    }

    public static function setUserIp($clientEmail, $clientIp)
    {
        $user = User::where('email', $clientEmail)->first();

        if ($user == null) {
            throw ValidationException::withMessages(['message' => $clientEmail.' user does not exists']);
        }

        $clientIpId = self::whitelistIp($clientIp);

        DB::table('user_ips')->insert([
            'user_id' => $user->id,
            'ip_id' => $clientIpId,
            'updated_at' => now(),
            'created_at' => now(),
        ]);
    }

    public static function markURLInvite($urlInviteStatus, $userId)
    {
        // update vip tracking table
        DB::table('user_invites')
            ->where('user_id', $userId)
            ->update([
                'status' => $urlInviteStatus,
            ]);
    }

    public static function SkipIdentityVerification($userId, $email, $ip)
    {
        $id = DB::table('identity_verification_events')->insertGetId([
            'user_id' => $userId,
            'identity_id' => 'vip_'.hash_hmac('sha256', $userId, $email),
            'status' => StripeIdentityEvent::VERIFIED,
            'metadata' => json_encode(['ip' => $ip, 'user_id' => $userId, 'email' => $email]),
            'payload' => json_encode(['ip' => $ip, 'user_id' => $userId, 'email' => $email]),
        ]);
    }

    public static function authorize(array $data)
    {
        $uuid = $data['uuid'] ?? null;

        if (! Str::isUuid($uuid)) {
            throw new FailedRequestException(400, 'Valid ID is required.', 'Bad Request');
        }

        $user = DB::table('user_invites')
            ->where('uuid', $uuid)
            ->where('status', '!=', InviteStatus::REVOKED)
            ->whereNull('deleted_at')
            ->first();

        return $user;
    }

    public static function userInviteOptions($data)
    {
        return json_decode($data);
    }

    public static function getUserInvited(array $data)
    {
        return self::authorize($data);
    }
}
