import { Link, router } from "@inertiajs/react";
import convertToTitleCase from "@/Util/convertToTitleCase";
import { useState } from "react";

export default function ProductStatusNav({ status_type, pageRoute }) {
    const params = route().params;
    const activeStatus = params.status ?? "All"
    const [hasSpinner, setSpinner] = useState(false);

    const getParams = (status) => {
        return {
            ...params,
            status: status
        };
    };

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const [hasActive, setActivetab] = useState(null);

    const removeactivetab = () => {

        const parent = document.getElementById("parentElement");
        const children = parent.querySelectorAll(".child.bg-gray-100");
        children.forEach(child => {
            child.classList.remove("bg-gray-100");
        });

    };

    return (
        <div id="parentElement" className="flex items-center flex-wrap cursor-pointer border-b text-default">
            {Object.values(status_type).map((e) => {
                return (
                    <Link
                        key={e}
                        as="button"
                        href={route(
                            pageRoute,
                            getParams(e)
                        )}
                        onClick={() => {
                            setSpinner(true);
                            removeactivetab();
                            setActivetab(e);
                        }}
                    >
                        <div
                            className={`child px-5 py-1 rounded-sm ${activeStatus == e || hasActive == e
                                ? "bg-gray-100 text-link"
                                : "hover:bg-gray-100 hover:text-link"
                                }`}
                        >
                            <span className=" text-inherit capitalize">
                                {e}
                            </span>
                        </div>
                    </Link>
                );
            })}
        </div>
    );
}
