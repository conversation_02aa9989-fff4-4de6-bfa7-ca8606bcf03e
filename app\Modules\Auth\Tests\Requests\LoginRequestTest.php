<?php
namespace App\Modules\Auth\Tests\Requests;

use App\Modules\Auth\Requests\LoginRequest;
use Illuminate\Support\Facades\Validator;

it('fails validation when required fields are missing', function () {
     $validator = Validator::make([], (new LoginRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue();
    expect($validator->errors()->has('password'))->toBeTrue();
});

it('fails validation with invalid email', function () {
    $request = new LoginRequest();

    $validator = Validator::make([
        'email' => 'invalid-email',
        'password' => 'secret',
    ], $request->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('email'))->toBeTrue();
});

it('passes validation with valid data', function () {
    $request = new LoginRequest();

    $validator = Validator::make([
        'email' => '<EMAIL>',
        'password' => 'secret',
    ], $request->rules());
    expect($validator->fails())->toBeFalse();
});