<?php

namespace App\Modules\Push\Requests;

use App\Modules\Push\Services\OutgoingPushDomainService;
use Illuminate\Foundation\Http\FormRequest;

class OutgoingPushDomainSummaryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'registered_domains' => ['array'],
            'registered_domains.*' => ['integer']
        ];
    }

    public function search(): array
    {
        return OutgoingPushDomainService::instance()->searchByRegisteredDomainId($this->registered_domains);
    }
}
