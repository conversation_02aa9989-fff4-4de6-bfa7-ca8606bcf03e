<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Cart\Requests\AddToCartRequest;
use App\Modules\MarketPlace\Requests\CartAddRequest;
use App\Modules\MarketPlace\Requests\CartRemoveRequest;
use App\Modules\MarketPlace\Services\MarketCartService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MarketCartController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return Inertia::render('Market/Cart/Cart', MarketCartService::instance()->getAll());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CartAddRequest $request)
    {
        $request->add();
    }

    public function toEPPCart(AddToCartRequest $request)
    {
        $request->store();
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CartRemoveRequest $request)
    {
        $request->remove();

        return redirect()->route('marketcart')->with('Removed from cart', true);
    }
}
