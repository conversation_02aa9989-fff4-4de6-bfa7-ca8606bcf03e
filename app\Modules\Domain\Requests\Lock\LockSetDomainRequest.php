<?php

namespace App\Modules\Domain\Requests\Lock;

use App\Modules\Domain\Services\UpdateServices\LockDomainService;
use App\Rules\EmailConfirmed;
use App\Rules\EmailDoesNotExist;
use App\Rules\ValidateOwner;
use App\Rules\ValidFormat;
use Illuminate\Foundation\Http\FormRequest;

class LockSetDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'user_id'   => ['required', 'integer', 'exists:users,id'],
            'domains'   => ['required', 'array', 'min:1'],
            'isDisable' => ['required', 'boolean'],
            // 'email' => [
            //     'bail',
            //     'required',
            //     'email:rfc,dns',
            //     new ValidFormat(),
            //     new ValidateOwner(),
            //     new EmailDoesNotExist(),
            // ],
            // 'email_confirmation' => ['required', 'email:rfc,dns', new EmailConfirmed($this->email)],
            // 'password' => ['required', 'string'],
        ];
    }

    public function checkAuthLock(): void
    {
        LockDomainService::instance()->authenticateUser($this->password);
        LockDomainService::instance()->getOwner($this->email);
    }

    public function setLock()
    {
        return LockDomainService::instance()->setLockData($this->all());
    }
}
