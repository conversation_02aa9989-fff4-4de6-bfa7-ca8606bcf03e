<?php

namespace App\Modules\Auth\Controllers\AuthenticatorApp;

use App\Http\Controllers\Controller;
use App\Modules\Auth\Requests\TwoFactorAuthenticatorAppVerificationFormRequest;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppSessionService;
use App\Modules\Auth\Services\UserIpAddressSessionService;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\Client\ClientIp;
use App\Util\Helper\RateLimit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatorAppController extends Controller
{
    /* TRAITS */
    // ?...

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Render Page
     */
    public function renderPage(string $token): Response
    {
        return Inertia::render(
            'Auth/Login2FaVerification/Login2FaAuthenticatorApp',
            [
                'token' => $token,
            ]
        );
    }

    /**
     * Verify Code
     */
    public function verifyCode(TwoFactorAuthenticatorAppVerificationFormRequest $request, string $token)
    {
        $rateLimitKey = RateLimiterKey::loginAttemptAuthenticatorApp($token);

        $executed = RateLimit::attempt($rateLimitKey, 5, 30);

        if (! $executed) {
            throw ValidationException::withMessages(['code' => 'Too many attempts. Wait for 30 seconds before trying again.']);
        }

        $userId = (new AuthenticatorAppSessionService)->verifyCode($request->only(['code']), $token);

        if ($userId) {
            Auth::loginUsingId($userId, session('rememberMe', false));

            (new UserIpAddressSessionService)->recordEntry($userId, ClientIp::getClientIp($request));

            return Inertia::location(route('domain'));
        } else {
            throw ValidationException::withMessages(['code' => 'Code is Invalid']);
        }
    }
}
