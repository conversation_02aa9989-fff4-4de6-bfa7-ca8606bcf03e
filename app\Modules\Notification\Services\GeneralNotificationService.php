<?php

namespace App\Modules\Notification\Services;

use App\Models\ScheduleNotification;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Notification\Constants\NotificationScheduleType;
use App\Modules\Notification\Constants\NotificationStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GeneralNotificationService
{
    private $now;

    private $payload = [
        'id',
        'user_id',
        'title',
        'message',
        'link_name',
        'created_at',
        'time',
        'start_date',
        'schedule_type',
        'status',
        'type',
        'expiration',
        'updated_at',
        'min_registration_period',
        'max_registration_period',
        'redirect_url',
        'read_at',
    ];

    private const SCHEDULE_TYPE_HANDLERS = [
        'weekly' => 'updateStartDateWeekly',
        'monthly' => 'updateStartDateMonthly',
        'yearly' => 'updateStartDateYearly',
    ];

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    /**
     * Get notifications with filters and pagination
     */
    public function getNotifications($userId, array $filters = [], $paginate = false, $page = 1, $perPage = 10)
    {
        $query = ScheduleNotification::query()
            ->where('user_id', $userId)
            ->where('status', NotificationStatus::ACTIVE)
            ->orderBy('updated_at', 'desc');

        if (! $paginate) {
            return $query->get();
        }

        $total = $query->count();
        $items = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        return [
            'items' => $items,
            'total' => $total,
        ];
    }

    /**
     * Get unread count with filters
     */
    public function getUnreadCount($userId, array $filters = [])
    {
        return ScheduleNotification::where('user_id', $userId)
            ->where('status', NotificationStatus::ACTIVE)
            ->whereNull('read_at')
            ->count();
    }

    /**
     * Update notification fields
     */
    public function updateNotification($notificationId, array $fields)
    {
        return ScheduleNotification::where('id', $notificationId)
            ->update($fields);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId): bool
    {
        return DB::table('schedule_notifications')
            ->where('id', $notificationId)
            ->update([
                'read_at' => now(),
            ]);
    }

    /**
     * Mark selected notifications as read
     */
    public function markSelectedAsRead(array $ids): bool
    {
        return DB::table('schedule_notifications')
            ->whereIn('id', $ids)
            ->update([
                'read_at' => now(),
            ]);
    }

    /**
     * Update notification status
     */
    public function updateNotificationStatus($notificationId, $status)
    {
        return $this->updateNotification($notificationId, [
            'status' => $status,
        ]);
    }

    /**
     * Update start date based on schedule type
     */
    public function updateStartDate($notificationId, $scheduleType): void
    {
        if (! isset(self::SCHEDULE_TYPE_HANDLERS[$scheduleType])) {
            return;
        }

        $handler = self::SCHEDULE_TYPE_HANDLERS[$scheduleType];
        $this->$handler($notificationId);
    }

    /**
     * Check and update notification expiration
     */
    public function checkAndUpdateExpiration($notification): bool
    {
        if (! $notification->expiration || ! Carbon::now()->greaterThan($notification->expiration)) {
            return false;
        }

        $this->updateNotificationStatus($notification->id, NotificationStatus::EXPIRED);

        return true;
    }

    /**
     * Create notifications for users
     */
    public function createNotificationsForUsers(array $notificationData, $users): int
    {
        $notifications = [];
        $duplicateCount = 0;

        foreach ($users as $user) {
            if (! $this->notificationExists($user->id, $notificationData)) {
                $notifications[] = array_merge($notificationData, [
                    'user_id' => $user->id,
                    'created_at' => $this->now,
                    'updated_at' => $this->now,
                ]);
            } else {
                $duplicateCount++;
            }
        }

        if (! empty($notifications)) {
            // app(AuthLogger::class)->info(sprintf(
            //     'Creating notifications - Total Users: %d, New Notifications: %d, Duplicates Skipped: %d',
            //     count($users),
            //     count($notifications),
            //     $duplicateCount
            // ));

            ScheduleNotification::insert($notifications);
        }

        return count($notifications);
    }

    /**
     * Get all notifications for scheduler processing
     */
    public function getStatus()
    {
        return ScheduleNotification::where(function ($query) {
            $query->where([
                ['schedule_type', '=', NotificationScheduleType::ONE_TIME],
                ['status', '=', NotificationStatus::PENDING],
            ])
                ->orWhere([
                    ['schedule_type', '<>', NotificationScheduleType::ONE_TIME],
                    ['status', 'in', [NotificationStatus::PENDING, NotificationStatus::ACTIVE]],
                ]);
        })->get();
    }

    /**
     * Update a specific notification field
     */
    public function updateNotificationField($notificationId, $field, $value)
    {
        return $this->updateNotification($notificationId, [
            $field => $value,
        ]);
    }

    /**
     * Check if notification has registration period
     */
    public function hasRegistrationPeriod($notification): bool
    {
        return ! empty($notification->min_registration_period) ||
            ! empty($notification->max_registration_period);
    }

    /**
     * Check if notification exists
     */
    private function notificationExists(int $userId, array $notificationData): bool
    {
        return ScheduleNotification::where('user_id', $userId)
            ->where('title', $notificationData['title'])
            ->where('message', $notificationData['message'])
            ->where('min_registration_period', $notificationData['min_registration_period'])
            ->where('max_registration_period', $notificationData['max_registration_period'])
            ->where('schedule_type', $notificationData['schedule_type'])
            ->exists();
    }

    private function updateScheduledDate($notificationId, callable $dateCalculator): void
    {
        $notification = ScheduleNotification::find($notificationId);
        $newStartDate = $dateCalculator(Carbon::parse($notification->start_date));
        $this->updateNotification($notificationId, [
            'start_date' => $newStartDate->format('Y-m-d'),
            'read_at' => null,
        ]);
    }

    // Schedule type handlers...
    private function updateStartDateWeekly($notificationId): void
    {
        $this->updateScheduledDate($notificationId, function ($date) {
            while ($date->isPast() || $date->isToday()) {
                $date->addWeek();
            }

            return $date;
        });
    }

    private function updateStartDateMonthly($notificationId): void
    {
        $this->updateScheduledDate($notificationId, function ($date) {
            return $date->addMonth();
        });
    }

    private function updateStartDateYearly($notificationId): void
    {
        $this->updateScheduledDate($notificationId, function ($date) {
            while ($date->startOfDay()->lte(Carbon::now()->startOfDay())) {
                $date->addYear();
            }

            return $date;
        });
    }

    public function getNotificationById($notificationId)
    {
        return ScheduleNotification::find($notificationId);
    }

    private function getUsersQuery($notification)
    {
        $query = ScheduleNotification::query()
            ->select(['u.id', 'u.created_at', 'u.updated_at'])
            ->join('users AS u', 'schedule_notifications.user_id', '=', 'u.id')
            ->where('u.created_at', '<=', $this->now);

        // app(AuthLogger::class)->info('User Query SQL: ' . $query->toSql());
        // app(AuthLogger::class)->info('User Query Bindings: ' . json_encode($query->getBindings()));

        return $query;
    }

    public function getNewUsers($notification)
    {
        $query = $this->getUsersQuery($notification);

        if ($this->hasRegistrationPeriod($notification)) {
            $this->applyRegistrationPeriodFilters($query, $notification);
        }

        $users = $query->get();

        $users = $users->filter(function ($user) use ($notification) {
            return ! $this->notificationExists($user->id, [
                'title' => $notification->title,
                'message' => $notification->message,
                'min_registration_period' => $notification->min_registration_period,
                'max_registration_period' => $notification->max_registration_period,
                'schedule_type' => $notification->schedule_type,
            ]);
        });

        // app(AuthLogger::class)->info(sprintf(
        //     'Found %d users matching criteria for notification ID %d',
        //     $users->count(),
        //     $notification->id
        // ));

        return $users;
    }

    private function applyRegistrationPeriodFilters($query, $notification): void
    {
        if (! $this->hasRegistrationPeriod($notification)) {
            return;
        }

        $this->applyDateRangeFilters($query, $notification);
    }

    private function applyDateRangeFilters($query, $notification): void
    {
        $baseDate = Carbon::parse($notification->created_at);

        if ($notification->min_registration_period || $notification->max_registration_period) {
            // app(AuthLogger::class)->info(sprintf(
            //     'Notification ID %d - Registration Period Range: Base Date: %s',
            //     $notification->id,
            //     $baseDate->format('Y-m-d')
            // ));
        }

        if ($notification->min_registration_period) {
            $minDate = $baseDate->copy()->addDays($notification->min_registration_period);
            $query->where('u.created_at', '>=', $minDate);
            // app(AuthLogger::class)->info(sprintf(
            //     'Min Registration Period: %d days, Valid From: %s',
            //     $notification->min_registration_period,
            //     $minDate->format('Y-m-d')
            // ));
        }

        if ($notification->max_registration_period) {
            $maxDate = $baseDate->copy()->addDays($notification->max_registration_period);
            $query->where('u.created_at', '<=', $maxDate);
            // app(AuthLogger::class)->info(sprintf(
            //     'Max Registration Period: %d days, Valid Until: %s',
            //     $notification->max_registration_period,
            //     $maxDate->format('Y-m-d')
            // ));
        }
    }

    public function updateNotificationWithTimestamp($notificationId, array $updates): bool
    {
        return ScheduleNotification::where('id', $notificationId)
            ->update(array_merge($updates, [
                'updated_at' => $this->now,
            ]));
    }

    /**
     * Get all unread notification IDs for a user
     */
    public function getUnreadNotificationIds($userId): array
    {
        return ScheduleNotification::where('user_id', $userId)
            ->where('status', NotificationStatus::ACTIVE)
            ->whereNull('read_at')
            ->pluck('id')
            ->toArray();
    }

    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsRead($userId): bool
    {
        return ScheduleNotification::where('user_id', $userId)
            ->where('status', NotificationStatus::ACTIVE)
            ->whereNull('read_at')
            ->update([
                'read_at' => now(),
            ]);
    }
}
