<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Modules\Epp\Constants\EppDomainStatus;
use App\Util\Helper\Domain\DomainStatusHelper;

class RestrictionsPayload
{
    public object $domain;

    public object $eppInfo;

    public array $status;

    public array $statusRemove;

    public string $name;

    public string $registrant;

    public function __construct(object $domain, array $eppInfo)
    {
        $this->domain = $domain;
        $this->eppInfo = json_decode(json_encode($eppInfo), false);

        $this->name = $domain->name;
        $this->registrant = $domain->registrant;
    }

    public function getDefault()
    {
        return [
            'name' => $this->name,
            'registrant' => $this->registrant,
            'regenerateAuthCode' => true,
            'status' => EppDomainStatus::CLIENT_LOCK_STATUS,
            'statusRemove' => [],
        ];
    }

    public function get()
    {
        $this->filter();

        return [
            'name' => $this->name,
            'registrant' => $this->registrant,
            'regenerateAuthCode' => false,
            'status' => $this->status,
            'statusRemove' => $this->statusRemove,
        ];
    }

    // PRIVATE FUNCTIONS

    private function filter()
    {
        $inStatus = DomainStatusHelper::filterClientStatus($this->eppInfo->status);
        $statusDomain = DomainStatusHelper::filterClientStatus($this->domain->client_status);

        if (empty($statusDomain)) {
            $this->status = [];
            $this->statusRemove = $inStatus;
        } else {
            $this->status = array_values(array_diff($statusDomain, $inStatus));
            $this->statusRemove = array_values(array_diff($inStatus, $statusDomain));
        }

        return $this;
    }
}
