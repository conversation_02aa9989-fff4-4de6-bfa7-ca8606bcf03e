<?php

use App\Http\Controllers\ProfileController;
use App\Modules\Guest\Controllers\ClientQueryController;
use App\Modules\Guest\Controllers\ClientReportAbuseController;
use App\Modules\WhoIs\Controllers\WhoIsController;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
        'status' => session('status'),
    ]);
})->name('home');

Route::get('/bad-request', function () {
    return Inertia::render('Errors/BadRequest');
})->name('bad-request');

Route::get('/forbidden-request', function () {
    throw new AccessDeniedHttpException;
})->name('forbidden-request');

Route::get('/invalid-password-reset-token', function () {
    return Inertia::render('Errors/InvalidPasswordResetToken');
})->name('invalid-password-reset-token');

Route::prefix('contact')->group(function () {
    Route::get('/', function () {
        return Inertia::render('ContactUs', [
            'contactEmail' => config('mail.from.support'),
            'contactPhone' => '+****************',
        ]);
    })->name('contact.us');

    Route::post('/query', [ClientQueryController::class, 'store'])->name('contact.query');
});

Route::prefix('abuse')->group(function () {
    Route::get('/', function () {
        return Inertia::render('ReportAbuse', ['abuseReportEmail' => config('mail.from.abuse'), 'phoneWhoIs' => null]);
    })->name('abuse');
    Route::post('/send-report', [ClientReportAbuseController::class, 'store'])->name('abuse.send-report');
});

Route::get('/about-us', function () {
    return Inertia::render('AboutUs');
})->name('about.us');

Route::get('/domains', function () {
    return Inertia::render('Domains');
})->name('domains');

Route::get('/privacy-policy', function () {
    return Inertia::render('PrivacyPolicy');
})->name('privacy.policy');

Route::get('/refund-policy', function () {
    return Inertia::render('RefundPolicy');
})->name('refund.policy');

Route::get('/lockin-policy', function () {
    return Inertia::render('LockinPolicy');
})->name('lockin.policy');

Route::get('/abuse-policy', function () {
    return Inertia::render('AbusePolicy');
})->name('abuse.policy');

Route::get('/terms', function () {
    return Inertia::render('TermsAndConditions');
})->name('terms');

Route::get('/rdap-terms-of-use', function () {
    return Inertia::render('RdapTermsOfUse');
})->name('rdap.tou');

Route::prefix('whois')->group(function () {
    Route::get('/', function () {
        return Inertia::render('WhoIs');
    })->name('whois');

    // Route::post('/', [WhoIsController::class, 'search'])->name('whois.search');
    // Route::get('/{domain}', [WhoIsController::class, 'search'])->name('whois.search');
    Route::get('/search', [WhoIsController::class, 'search'])->name('whois.search');
});

Route::get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Route::middleware(['auth', 'auth.active'])->group(function () {
//     // Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
//     // Route::patch('/profile', [ProfileController::class, 'update'])
//     //     ->middleware([HandlePrecognitiveRequests::class])
//     //     ->name('profile.update');
//     // Route::patch('/address', [ProfileController::class, 'updateAddress'])
//     //     ->middleware([HandlePrecognitiveRequests::class])
//     //     ->name('address.update');

//     // Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

// });

require __DIR__.'/payment_method.php';
require __DIR__.'/push.php';
require __DIR__.'/domain.php';
require __DIR__.'/user_domain_export.php';
require __DIR__.'/contact.php';
require __DIR__.'/category.php';
require __DIR__.'/payment.php';
require __DIR__.'/notification.php';
require __DIR__.'/guest.php';
require __DIR__.'/transfer.php';
require __DIR__.'/cart.php';
require __DIR__.'/domain_search.php';
require __DIR__.'/domain_cancellation.php';
require __DIR__.'/mailable.php';
require __DIR__.'/auth.php';
require __DIR__.'/user_settings.php';
require __DIR__.'/two_factor_authentication.php';
require __DIR__.'/account_security_settings.php';
require __DIR__.'/user_account_setup.php';
require __DIR__.'/user_sessions.php';
require __DIR__.'/user_profile.php';
require __DIR__.'/user_pin_code.php';
require __DIR__.'/stripe.php';
require __DIR__.'/session.php';
require __DIR__.'/account_balance.php';
require __DIR__.'/wire_transfer.php';
require __DIR__.'/saved_card.php';
require __DIR__.'/user_verification.php';
require __DIR__.'/registration.php';
require __DIR__.'/user_api_key.php';
require __DIR__.'/market.php';

// Route::domain(Config::get('subdomains.home'))->group(function () {
// });

// Route::domain(Config::get('subdomains.mydomain'))->group(function () {
// });
