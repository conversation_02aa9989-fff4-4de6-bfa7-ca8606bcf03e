<?php

namespace App\Console\Commands\Transaction;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class MonthlyTransactionsAggregator extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transactions:monthly-transactions-aggregator';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles aggregation of user transactions per week.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'MonthlyTransactionsAggregator: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('MonthlyTransactionsAggregator: Aggregating monthly transactions...');

        $now = Carbon::now()->subDay();
        [$startAt, $endAt] = $this->getDateRange($now);

        $userTransactionsPayload = $this->buildUserTransactionsPayload($now, $startAt, $endAt);
        $systemTransactionsPayload = $this->buildSystemTransactionsPayload($now, $startAt, $endAt);

        if (empty($userTransactionsPayload) || empty($systemTransactionsPayload)) {
            app(AuthLogger::class)->info('MonthlyTransactionsAggregator: Done. No transactions for this week.');

            return;
        }

        DB::table('user_monthly_transactions')->insert($userTransactionsPayload);
        DB::table('system_monthly_transactions')->insert($systemTransactionsPayload);

        app(AuthLogger::class)->info('MonthlyTransactionsAggregator: Done');
    }

    private function getDateRange(Carbon $now): array
    {
        $startAt = $now->copy()->startOfMonth()->toDateString();
        $endAt = $now->copy()->endOfMonth()->toDateString();

        return [$startAt, $endAt];
    }

    private function buildUserTransactionsPayload(Carbon $now, string $startAt, string $endAt): array
    {
        return DB::table('user_weekly_transactions')
            ->select('user_transaction_id', DB::raw('SUM(counter) as total_counter'))
            ->whereMonth('start_at', $now->month)
            ->whereYear('start_at', $now->year)
            ->groupBy('user_transaction_id')
            ->get()->map(fn($summary) => [
                'user_transaction_id' => $summary->user_transaction_id,
                'counter' => $summary->total_counter,
                'start_at' => $startAt,
                'end_at' => $endAt,
            ])->toArray();
    }

    private function buildSystemTransactionsPayload(Carbon $now, string $startAt, string $endAt): array
    {
        return DB::table('system_weekly_transactions')
            ->select('transaction_id', DB::raw('SUM(counter) as total_counter'))
            ->whereMonth('start_at', $now->month)
            ->whereYear('start_at', $now->year)
            ->groupBy('transaction_id')
            ->get()->map(fn($summary) => [
                'transaction_id' => $summary->transaction_id,
                'counter' => $summary->total_counter,
                'start_at' => $startAt,
                'end_at' => $endAt,
            ])->toArray();
    }
}
