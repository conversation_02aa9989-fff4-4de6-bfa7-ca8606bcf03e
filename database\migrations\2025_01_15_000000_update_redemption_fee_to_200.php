<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use App\Modules\Setting\Constants\FeeType;

return new class extends Migration
{
    public function up(): void
    {
        DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->update(['value' => 200]);

        $redemptionFeeId = DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->value('id');

        if ($redemptionFeeId) {
            DB::table('extension_fees')
                ->where('fee_id', $redemptionFeeId)
                ->where('is_default', true)
                ->whereNull('user_id')
                ->update(['value' => 0]);
        }
    }

    public function down(): void
    {
        DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->update(['value' => 100]);

        $redemptionFeeId = DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->value('id');

        if ($redemptionFeeId) {
            DB::table('extension_fees')
                ->where('fee_id', $redemptionFeeId)
                ->where('is_default', true)
                ->whereNull('user_id')
                ->update(['value' => 0]);
        }
    }
};
