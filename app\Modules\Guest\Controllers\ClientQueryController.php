<?php

namespace App\Modules\Guest\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Guest\Requests\ClientQueryForm;
use Inertia\Inertia;

class ClientQueryController extends Controller
{
    public function store(ClientQueryForm $request)
    {
        $request->send();

        return Inertia::render('Notice/GuestMessage', [
            'message' => "Query sent. We will get back to you once we've reached your message.",
        ]);
    }
}
