<?php

namespace App\Modules\Stripe\Helpers;

use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Helper\CryptHelper;
use Illuminate\Support\Str;

class StripeEncryptHelper
{
    use UserLoggerTrait;

    public static function getId(string $prefix, string $id)
    {
        if (empty($id)) {
            return null;
        }

        if (Str::startsWith($id, $prefix)) {
            return $id;
        }

        return CryptHelper::decrypt($id);
    }
}
