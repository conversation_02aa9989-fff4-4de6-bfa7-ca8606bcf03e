<?php

namespace App\Modules\DomainEppSearch\Services;

use App\Modules\Cart\Services\UserCart;
use App\Modules\Domain\Constants\RegistryName;
use App\Modules\Domain\Services\EppDomainService;
use Illuminate\Support\Str;

class SearchDomainService
{
    public static function instance(): self
    {
        $searchDomainService = new self;

        return $searchDomainService;
    }

    public function searchDomains(array $request)
    {
        $domainNames = new DomainNames(strtolower($request['domains']), $request['extensions']);
        $result = $this->callBatchCheck($domainNames->parse()->create()->getRegistryArrays());

        $cart = new UserCart;

        return [
            'data' => $result,
            'domains_list' => $domainNames->getDomains(),
            'cartList' => $cart->getOnCartNames(),
            'deletedCartList' => $cart->getDeletedCartNames(),
            'comp_id' => Str::random(10),
            'cartCount' => $cart->getTotalDomain(),
        ];
    }

    public function callBatchCheck(array $domains_to_check): array
    {
        if (empty($domains_to_check)) {
            return [];
        }
        $result_array = [];
        $count = 0;
        foreach (RegistryName::SUPPORTED as $registry) {
            $registry_data = EppDomainService::instance()->callEppMultipleCheck($domains_to_check, $registry);
            $results = array_key_exists('data', $registry_data) ? $registry_data['data'] : [];
            if ($count == 0) {
                $count++;
                $result_array = $results;

                continue;
            }

            $result_array = array_merge($result_array, $results);
        }

        return $result_array;
    }

    // public function callBatchCheck(array $domains_to_check): array
    // {
    //     if (empty($domains_to_check)) return [];

    //     $result_array = [];
    //     $existing_domains = [];

    //     foreach (RegistryName::SUPPORTED as $registry) {
    //         $registry_data = $this->tempCallEppMultipleCheck($domains_to_check);
    //         foreach ($registry_data['data'] ?? [] as $result) {
    //             if (!in_array($result['name'], $existing_domains)) {
    //                 $existing_domains[] = $result['name'];
    //                 $result_array[] = $result;
    //             }
    //         }
    //     }

    //     return $result_array;
    // }

    // public function tempCallEppMultipleCheck(array $domains_to_check): array
    // {
    //     $response = [
    //         "data" => [],
    //         "message" => "List of domain availabilities",
    //         "status" => "OK",
    //         "statusCode" => 200
    //     ];

    //     foreach ($domains_to_check as $domains) {
    //         foreach ($domains as $domain) {
    //             $response['data'][] = ["name" => $domain, "available" => true];
    //         }
    //     }

    //     return $response;
    // }
}
