<?php

namespace App\Util\Constant;

final class QueueTypes
{
    public final const PUSH_REQUEST_EXPIRED = 'PUSH-REQUEST-EXPIRED';
    public final const PAYMENT_EMAIL = 'email';
    public final const PAYMENT_REFUND = 'refund';
    public final const DEFAULT = 'default';
    public final const EXPIRY_SEND = 'send';

    public final const PUSH = [
        'verisign' => 'VERISIGN-PUSH',
        'pir' => 'PIR-PUSH',
    ];

    public final const CONTACT_CREATE = [
        'verisign' => 'VERISIGN-CONTACT-CREATE',
        'pir' => 'PIR-CONTACT-CREATE',
    ];

    public final const CONTACT_UPDATE = [
        'verisign' => 'VERISIGN-CONTACT-UPDATE',
        'pir' => 'PIR-CONTACT-UPDATE',
    ];

    public final const DOMAIN_REGISTRATION = [
        'verisign' => 'VERISIGN-REGISTRATION',
        'pir' => 'PIR-REGISTRATION',
    ];

    public final const DOMAIN_RENEWAL = [
        'verisign' => 'VERISIGN-RENEW',
        'pir' => 'PIR-RENEW',
    ];

    public final const DOMAIN_UPDATE = [
        'verisign' => 'VERISIGN-UPDATE',
        'pir' => 'PIR-UPDATE',
    ];

    public final const DOMAIN_AUTHCODE_UPDATE = [
        'verisign' => 'VERISIGN-AUTHCODE-UPDATE',
        'pir' => 'PIR-AUTHCODE-UPDATE',
    ];

    public final const DOMAIN_TRANSFER = [
        'verisign' => 'VERISIGN-TRANSFER',
        'pir' => 'PIR-TRANSFER',
    ];

    public final const DOMAIN_REDEMPTION = [
        'verisign' => 'VERISIGN-REDEMPTION',
        'pir' => 'PIR-REDEMPTION',
    ];

    public final const DOMAIN_TRANSFER_RESPONSE = [
        'verisign' => 'VERISIGN-TRANSFER-RESPONSE',
        'pir' => 'PIR-TRANSFER-RESPONSE',
    ];

    public final const DOMAIN_TRANSFER_POLL_UPDATE = [
        'verisign' => 'VERISIGN-POLL-TRANSFER-UPDATE',
        'pir' => 'PIR-POLL-TRANSFER-UPDATE',
    ];
    public final const DOMAIN_REFRESH = [
        'verisign' => 'VERISIGN-REFRESH',
        'pir' => 'PIR-REFRESH',
    ];
}
