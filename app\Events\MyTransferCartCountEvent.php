<?php

namespace App\Events;

use App\Modules\Transfer\Services\UserTransferCart;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MyTransferCartCountEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user_id;

    public $cartCount;

    /**
     * Create a new event instance.
     */
    public function __construct(int $user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $cart = new UserTransferCart();
        $this->cartCount = $cart->getTotalDomain();

        return [
            new PrivateChannel('MyTransferCart.' . $this->user_id),
        ];
    }
}
