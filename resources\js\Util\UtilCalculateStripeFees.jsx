export default function UtilCalculateStripeFees(value, cardCountry = 'US')
{
    if (!value)
    {
        return 0;
    }

    const FIXED_FEE  = 0.30;                     // $0.30

    const type       = getCardType(cardCountry)
    const percentFee = getPercentFee(type);
    
    return ((value * percentFee) + FIXED_FEE).toFixed(2);  
}

function getCardType(cardCountry) 
{
    switch (cardCountry)
    {
        case 'US':
            return 'LOCAL'; 
            break; 
        
        default:
            return 'INTERNATIONAL';
            break; 
    }
}

function getPercentFee(type='INTERNATIONAL')
{
    const LOCAL_CARD_FEE         = 0.029;  // 2.9%
    const INTERNATIONAL_CARD_FEE = 0.015;  // 1.5%

    //? UNUSED
    const CONVERSION_FEE         = 0.01;   // 1.0%
    const MANUAL_FEE             = 0.05;   // 0.5%

    switch (type)
    {
        case 'INTERNATIONAL':
            return LOCAL_CARD_FEE + INTERNATIONAL_CARD_FEE;
        case 'LOCAL':
            return LOCAL_CARD_FEE;  
    }
}