<?php

namespace App\Modules\Guest\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Guest\Requests\NewIpForm;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NewIpController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Guest/RegisterIp', ['ip' => ClientIp::getClientIp($request)]);
    }

    public function store(NewIpForm $request)
    {
        $request->send();

        return Inertia::render('Notice/GuestMessage', ['message' => "Request sent. We will send you a link once we've confirmed your request."]);
    }
}
