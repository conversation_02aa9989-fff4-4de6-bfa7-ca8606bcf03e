<?php

namespace App\Events;

use App\Modules\Cart\Services\UserCart;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MyCartCountEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user_id;

    public $cartCount;

    /**
     * Create a new event instance.
     */
    public function __construct(int $user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $cart = new UserCart();
        $this->cartCount = $cart->getTotalDomain();

        // app(AuthLogger::class)->info('MyCartCountEvent: '.$this->user_id.' - '.$this->cartCount);
        return [
            new PrivateChannel('MyCart.' . $this->user_id),
        ];
    }
}
