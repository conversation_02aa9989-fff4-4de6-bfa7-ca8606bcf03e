<?php

namespace App\Modules\MarketPlace\Requests;

use App\Events\MyCartCountEvent;
use App\Exceptions\FailedRequestException;
use App\Modules\Cart\Services\CheckoutCartService;
use App\Modules\Cart\Services\MultiCheckout\MultiCheckoutService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Services\MarketOfferService;
use App\Modules\Payment\Constants\CheckoutType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Rules\Cart\CartArrayExists;
use App\Rules\Domain\DomainNotAvailable;
use App\Rules\MarketPlace\MarketCartArrayExists;
use App\Rules\Payment\ValidateOtherFees;
use App\Rules\Payment\ValidateStripeFees;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class OfferCheckoutStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return $this->getValidationRules();
    }

    public function prepareForValidation()
    {
        $this->merge(['payment_summary_type' => PaymentSummaryType::MULTI_CHECKOUT_INVOICE]);
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (empty($this->domains) && empty($this->market_domains)) {
                $validator->errors()->add('', 'Cart is empty.');
            }

            $cartExistsRule = new CartArrayExists;
            $marketCartExistsRule = new MarketCartArrayExists;
            $domainNotAvailableRule = new DomainNotAvailable;

            $domainNames = $this->getDomainNames();

            if (! empty($this->domains) && ! $cartExistsRule->passes('domains', $this->domains['domains'])) {
                $validator->errors()->add('domains', $cartExistsRule->message());
            }

            if (! empty($this->market_domains) && ! $marketCartExistsRule->passes('market_domains', $this->market_domains['domains'])) {
                $validator->errors()->add('market_domains', $marketCartExistsRule->message());
            }

            if (! empty($domainNames) && ! $domainNotAvailableRule->passes('domains', $domainNames)) {
                $validator->errors()->add('domains', $domainNotAvailableRule->message($domainNames));
            }
        });
    }

    public function passedValidation()
    {
        if ($this->payment_service_type === PaymentServiceType::ACCOUNT_CREDIT) {
            CheckoutCartService::instance()->checkAccountCreditBalance($this->amount_to_use);
            $this->cancelIntent();
        }

        MultiCheckoutService::instance()->checkMultiRegistryBalance($this->all());

        $this->captureIntent();
    }

    protected function failedValidation(Validator $validator)
    {
        $this->cancelIntent();
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, $validator->errors()->first(), 'Page not found');
    }

    public function store(): string
    {
        app(AuthLogger::class)->info('multicheckout request store');
        $invoiceId = MarketOfferService::instance()->store($this->all());
        MyCartCountEvent::dispatch(Auth::user()->id);

        return $invoiceId;
    }

    private function getValidationRules(): array
    {
        $rules = [
            // 'domains' => ['required', 'array'],
            // 'market_domains' => ['required', 'array'], // TODO to enable market
            'other_fees' => ['required', 'array', 'min:1', new ValidateOtherFees(CheckoutType::MULTI_CHECKOUT)],
            'payment_service_type' => ['required', Rule::in([PaymentServiceType::STRIPE, PaymentServiceType::ACCOUNT_CREDIT])],
            'payment_summary_type' => ['required', Rule::in(PaymentSummaryType::ALL)],
        ];

        return match ($this->payment_service_type) {
            PaymentServiceType::STRIPE => array_merge(
                $rules,
                [
                    'intent' => ['required', 'string'],
                    'stripe_fees' => ['required', 'array', 'min:1', new ValidateStripeFees],
                ]
            ),
            PaymentServiceType::ACCOUNT_CREDIT => array_merge($rules, ['amount_to_use' => ['required', 'numeric']]),
            default => $rules
        };
    }

    private function cancelIntent()
    {
        if ($this->input('intent')) {
            PaymentIntentProvider::instance()->cancelIntent($this->input('intent'));
        }
    }

    private function captureIntent(): void
    {
        if (($this->payment_service_type === PaymentServiceType::STRIPE) &&
            $this->input('intent')
        ) {
            PaymentIntentProvider::instance()->captureIntent($this->input('intent'));
        }
    }

    private function getDomainNames()
    {
        $cartNames = [];
        $marketCartNames = [];

        if (! empty($this->domains['domains'])) {
            $cartObj = collect($this->domains['domains']);
            $cartNames = $cartObj->pluck('name')->toArray();
        }

        if (! empty($this->market_domains['domains'])) {
            $marketCartObj = collect($this->market_domains['domains']);
            $marketCartNames = $marketCartObj->pluck('name')->toArray();
        }

        return array_merge($cartNames, $marketCartNames);
    }
}
