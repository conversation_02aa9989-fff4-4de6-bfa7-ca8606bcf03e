<?php

namespace App\Http\Middleware;

use App\Exceptions\FailedRequestException;
use App\Exceptions\UnauthorizedRequestException;
use App\Models\Ip;
use App\Models\User;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\Client\ClientIp;
use App\Util\Helper\Client\ClientRedirect;
use App\Util\Helper\RateLimit;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckUserActiveStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::hasUser()) {
            ClientRedirect::redirectPreviousUrl($request);
            throw new UnauthorizedRequestException(401, 'Unauthorized. You need to log in first.', '');
            // return redirect()->route('login');
        }

        $userActive = Auth::user()->is_active;
        $clientIp = ClientIp::getClientIp($request);
        //$ipActive = Ip::where('ip', $clientIp)->value('is_active');

        if (
            !$userActive
            //! || $ipActive
        ) {
            $logStatus = !$userActive ? 'disabled' : 'blocked';
            app(AuthLogger::class)->info('User is ' . $logStatus . '. Logging out at ' . $clientIp);

            RateLimit::clear(RateLimiterKey::activeStatus(Auth::id()));

            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('home');
        }

        $attempt = 1;
        $decay = 180; // in seconds

        if (RateLimit::attempt(RateLimiterKey::activeStatus(Auth::id()), $attempt, $decay)) {
            $user = User::find(Auth::id());
            $user->last_active_at = Carbon::now();
            $user->save();
        }

        return $next($request);
    }
}
