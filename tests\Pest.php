<?php

use App\Models\User;
use Laravel\Sanctum\Sanctum;

validateEnvironment();

// uses(Tests\TestCase::class)->in('Feature');
uses(Tests\ModuleTestCase::class)->in('../app/Modules/*/Tests');

pest()
    ->beforeEach(function () {
        $this->user = createUserAndLogin();
    })
    ->in('../app/Modules/*/Tests');

function something()
{
    // ..
}

function createUserAndLogin()
{
    $user = User::factory()->create(TestUserData::defaultUserData());
    Sanctum::actingAs($user);
    return $user;
}

class TestUserData
{
    public static function defaultUserData(): array
    {
        return [
            'first_name' => "First",
            'last_name' => "Last",
            'email' => "<EMAIL>",
            'password' => "test",
            'street' => "test_street",
            'city' => 'test_city',
            'state_province' => 'test_province',
            'postal_code' => '6539',
            'country_code' => "PH"
        ];
    }

    public static function alternativeUserData(): array
    {
        return [
            'first_name' => "<PERSON>",
            'last_name' => "Doe",
            'email' => "<EMAIL>",
            'password' => "password123",
            'street' => "123 Main Street",
            'city' => 'New York',
            'state_province' => 'NY',
            'postal_code' => '10001',
            'country_code' => "US"
        ];
    }
}

function validateEnvironment(): void
{
    $envTestingPath = __DIR__ . '/../.env.testing';

    if (!file_exists($envTestingPath)) {
        throw new RuntimeException('.env.testing file not found in directory. ');
    }

}