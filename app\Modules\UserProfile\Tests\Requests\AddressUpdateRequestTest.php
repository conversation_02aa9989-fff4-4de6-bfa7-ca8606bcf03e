<?php
namespace App\Modules\UserProfile\Tests\Requests;

use Illuminate\Support\Facades\Validator;
use App\Modules\UserProfile\Requests\AddressUpdateRequest;
use App\Modules\UserProfile\Tests\Datasets\CommonValidationDataset;

it('fails validation when required fields are missing', function () {
    $request = new AddressUpdateRequest;

    $validator = Validator::make([], $request->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('street'))->toBeTrue("Failed to validate required field for street");
    expect($validator->errors()->has('city'))->toBeTrue("Failed to validate required field for city");
    expect($validator->errors()->has('stateProvince'))->toBeTrue("Failed to validate required field for stateProvince");
    expect($validator->errors()->has('postalCode'))->toBeTrue("Failed to validate required field for postalCode");
    expect($validator->errors()->has('countryCode'))->toBeTrue("Failed to validate required field for countryCode");
});

it('passes validation with valid data', function () {
    $request = new AddressUpdateRequest;
    $validDatasets = CommonValidationDataset::validAddressData();

    foreach ($validDatasets as $testName => $data) {
        $validator = Validator::make($data, $request->rules());
        expect($validator->passes())->toBeTrue("Failed validation for {$testName}: " . json_encode($validator->errors()));
    }
});

it('fails validation when field has invalid data', function () {
    $request = new AddressUpdateRequest;
    $invalidDatasets = CommonValidationDataset::invalidAddressData();

    foreach ($invalidDatasets as $testName => $data) {
        $validator = Validator::make($data, $request->rules());

        foreach (array_keys((array) $data) as $key) {
            expect($validator->errors()->has($key))->toBeTrue("Failed to validate {$key} field for test case: {$testName}");
        }
        expect($validator->fails())->toBeTrue("Expected validation to fail for test case: {$testName}");
    }
});
