<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Events\DomainHistoryEvent;
use App\Events\UpdateDomainsTableEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Payment\Services\PaymentNodeService;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Util\Constant\QueueErrorTypes;
use Exception;
use Illuminate\Support\Facades\Config;

class JobRegisterEppService
{
    use UserLoggerTrait;

    private $dispatchDelayInSeconds = 180;

    private JobRecord $jobRecord;

    public static function instance(): self
    {
        $jobRegisterEppService = new self;

        return $jobRegisterEppService;
    }

    public function handle(JobRecord $record): void
    {
        $this->jobRecord = $record;

        app(AuthLogger::class)->info($this->fromWho('Domain registration start...', $this->jobRecord->email));

        $response = EppDomainService::instance()->callCreate($this->jobRecord->domain);
        $this->evaluateResponse($response);

        app(AuthLogger::class)->info($this->fromWho('Domain registration end...', $this->jobRecord->email));
    }

    // PRIVATE FUNCTIONS

    private function evaluateResponse(array $response)
    {
        $status = array_key_exists('status', $response) ? $response['status'] : Config::get('domain.status.error');

        if ($status === Config::get('domain.status.created')) {
            return $this->evaluateRegistrationSuccess();
        }

        $this->evaluateRegistrationFailed($response);
    }

    private function evaluateRegistrationSuccess()
    {
        DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::ACTIVE, false, $this->jobRecord->email);
        PaymentNodeService::instance()->updateByRegisteredDomainId($this->jobRecord->registeredDomainId, 'status', PaymentNodeStatus::COMPLETED, $this->jobRecord->email);
        DomainNotificationService::instance()->sendDomainActiveStatusNotif($this->jobRecord->name, $this->jobRecord->userId);
        $this->jobRecord->stopJobRetry(DomainStatus::ACTIVE);

        event(new DomainHistoryEvent([
            'domain_id' => $this->jobRecord->domainId,
            'type' => 'DOMAIN_CREATED',
            'user_id' => $this->jobRecord->userId,
            'status' => 'success',
            'message' => 'Domain "'.$this->jobRecord->name.'" registered for '.$this->jobRecord->domain->year_length.' year(s)',
            'payload' => $this->jobRecord,
        ]));

        return true;
    }

    private function evaluateRegistrationFailed(array $response)
    {
        $errorCode = array_key_exists('eppCode', $response) ? $response['eppCode'] : Config::get('domain.code.error');

        event(new DomainHistoryEvent([
            'domain_id' => $this->jobRecord->domainId,
            'type' => 'DOMAIN_CREATED',
            'user_id' => $this->jobRecord->userId,
            'status' => 'failed',
            'message' => 'Failed to register domain "'.$this->jobRecord->name.'" - Error code: '.$errorCode,
            'payload' => $this->jobRecord,
        ]));

        switch ($errorCode) {
            case Config::get('domain.code.billing_failure'):
                return $this->evaluateBillingFailure();
            case Config::get('domain.code.object_exists'):
                return $this->evaluateDomainAlreadyRegistered();
            case Config::get('domain.code.unprocessable_entity'):
                return $this->evaluateUnprocessableRequest();
            default:
                throw new Exception(QueueErrorTypes::RETRY);
        }
    }

    private function evaluateDomainAlreadyRegistered()
    {
        try {
            $this->jobRecord->refundDetails['description'] = FeeType::TRANSACTION_TYPE[FeeType::REGISTRATION].' - '.DomainStatus::NOT_AVAILABLE;
            PaymentSummaryService::instance()->createRefund($this->jobRecord->refundDetails, $this->jobRecord->registeredDomainId, $this->jobRecord->userId);
            DomainNotificationService::instance()->sendDomainFailedStatusNotif($this->jobRecord->name, $this->jobRecord->userId);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('error in execute refund: '.$e->getMessage());
        }

        $this->throwNotAvailableException($this->jobRecord->name.' domain is not available.');
    }

    private function evaluateBillingFailure(): never
    {
        RegistryAccountBalanceService::setBalanceToZero($this->jobRecord->registryId);
        app(AuthLogger::class)->error($this->fromWho('Billing failure', $this->jobRecord->email));

        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function evaluateUnprocessableRequest()
    {
        try {
            $this->jobRecord->refundDetails['description'] = FeeType::TRANSACTION_TYPE[FeeType::REGISTRATION].' - '.DomainStatus::FAILED;
            PaymentSummaryService::instance()->createRefund($this->jobRecord->refundDetails, $this->jobRecord->registeredDomainId, $this->jobRecord->userId);
            DomainNotificationService::instance()->sendDomainFailedStatusNotif($this->jobRecord->name, $this->jobRecord->userId);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('error in execute refund: '.$e->getMessage());
        }

        $this->throwFailedException($this->jobRecord->name.' has unprocessable entities: 422 data management violation.');
    }

    private function throwNotAvailableException(string $error)
    {
        $this->jobRecord->stopJobRetry(DomainStatus::NOT_AVAILABLE);
        DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::NOT_AVAILABLE, false, $this->jobRecord->email);
        UpdateDomainsTableEvent::dispatch($this->jobRecord->userId);
        app(AuthLogger::class)->error($this->fromWho($error, $this->jobRecord->email));

        throw new Exception($error);
    }

    private function throwFailedException(string $error)
    {
        $this->jobRecord->stopJobRetry(DomainStatus::FAILED);
        DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::FAILED, false, $this->jobRecord->email);
        UpdateDomainsTableEvent::dispatch($this->jobRecord->userId);
        app(AuthLogger::class)->error($this->fromWho($error, $this->jobRecord->email));

        throw new Exception($error);
    }
}
