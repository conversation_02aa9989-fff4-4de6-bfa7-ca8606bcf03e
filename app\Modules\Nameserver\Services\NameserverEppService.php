<?php

namespace App\Modules\Nameserver\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

class NameserverEppService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $nameserverEppService = new self;

        return $nameserverEppService;
    }

    public function check(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');
        if (is_null($registry)) {
            return $error;
        }

        try {
            $request = Http::host($registry)->post(
                Config::get('host.check'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));

            return $error;
            // throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }
}
