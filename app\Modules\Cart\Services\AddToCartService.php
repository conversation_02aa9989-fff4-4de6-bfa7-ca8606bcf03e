<?php

namespace App\Modules\Cart\Services;

use App\Models\Cart;
use App\Modules\Cart\Constants\CartDeleteType;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Helper\Domain\DomainTld;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AddToCartService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $addToCartService = new self;

        return $addToCartService;
    }

    public function store(array $request): bool
    {
        $isCreated = false;

        if (! $request) {
            return $isCreated;
        }

        $items = $this->createCartItems($request);
        $lists = $this->filterUniqueItems($items);
        $isCreated = $this->saveItems($lists, $this);

        return $isCreated;
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function createCartItems(array $request): array
    {
        if (! $request) {
            return [];
        }

        $userId = $this->getUserId();

        $defaultValue = [
            'user_id' => $userId,
            'year_length' => 1,
            'tld_id' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        $names = [];
        $lists = [];
        $tlds = DomainTld::getTldsByExtension();
        $tld_keys = array_keys($tlds);

        foreach ($request as $key => $value) {
            $default = $defaultValue;
            $default['name'] = $value['name'];
            $ext = strtolower($value['extension']);
            $default['tld_id'] = (in_array($ext, $tld_keys)) ? $tlds[$ext]->id : 0;
            $names[] = $value['name'];
            $lists[] = $default;
        }

        return
            [
                'names' => $names,
                'lists' => $lists,
            ];
    }

    private function filterUniqueItems(array $items): array
    {
        $names = $items['names'];
        $lists = $items['lists'];

        $notUnique = $this->getCartNamesFromDB($names);

        if (! empty($notUnique)) {
            $lists = array_filter($lists, function ($i) use ($notUnique) {
                return ! in_array($i['name'], $notUnique);
            });
        }

        return $lists;
    }

    private function getCartNamesFromDB(array $names): array
    {
        $inCart = DB::table('carts')
            ->whereIn('name', $names)
            ->get();

        $notUnique = [];

        foreach ($inCart as $item) {
            if ($item->deleted_at == null) {
                $notUnique[] = $item->name;
            } elseif ($item->deleted_at != null && $item->delete_type != CartDeleteType::DELETED) {
                $notUnique[] = $item->name;
            }
        }

        return $notUnique;
    }

    private function saveItems(array $lists, self $addToCartService): bool
    {
        $isCreated = false;

        if (count($lists) > 0) {
            $isCreated = Cart::insert($lists);
            app(AuthLogger::class)->info($addToCartService->fromWho(count($lists).' items added to cart.'));
        }

        return $isCreated;
    }
}
