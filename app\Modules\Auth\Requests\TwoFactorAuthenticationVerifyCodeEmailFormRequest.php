<?php

namespace App\Modules\Auth\Requests;

use App\Rules\Auth\TwoFactorVerifyEmailAuthenticationCodeRule;
use Illuminate\Foundation\Http\FormRequest;

class TwoFactorAuthenticationVerifyCodeEmailFormRequest extends FormRequest
{
    // protected $redirect = 'bad-request';
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'digits:6',
                new TwoFactorVerifyEmailAuthenticationCodeRule($this->token),
            ],
        ];
    }
}
