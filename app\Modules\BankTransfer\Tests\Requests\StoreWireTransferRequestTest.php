<?php

namespace App\Modules\BankTransfer\Tests\Requests;

use App\Modules\BankTransfer\Requests\StoreWireTransferRequest;
use Illuminate\Support\Facades\Validator;

it('fails validation when required fields are missing', function () {
    $validator = Validator::make([], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('amount'))->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
    expect($validator->errors()->has('company'))->toBeTrue();
});

it('passes validation with valid data', function () {
    $validator = Validator::make([
        'amount' => '100.50',
        'name' => 'John Doe',
        'company' => 'Acme Corp Ltd.'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeFalse();
});

it('fails validation with invalid amount format', function () {
    $validator = Validator::make([
        'amount' => '100.999', // More than 2 decimal places
        'name' => 'John Doe',
        'company' => 'Acme Corp'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('amount'))->toBeTrue();
});

it('fails validation with zero amount', function () {
    $validator = Validator::make([
        'amount' => '0',
        'name' => 'John Doe',
        'company' => 'Acme Corp'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('amount'))->toBeTrue();
});

it('fails validation with negative amount', function () {
    $validator = Validator::make([
        'amount' => '-100',
        'name' => 'John Doe',
        'company' => 'Acme Corp'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('amount'))->toBeTrue();
});

it('fails validation with invalid name characters', function () {
    $validator = Validator::make([
        'amount' => '100',
        'name' => 'John123 Doe', // Contains numbers
        'company' => 'Acme Corp'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
});

it('fails validation with name too short', function () {
    $validator = Validator::make([
        'amount' => '100',
        'name' => 'J', // Only 1 character
        'company' => 'Acme Corp'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
});

it('fails validation with name too long', function () {
    $validator = Validator::make([
        'amount' => '100',
        'name' => str_repeat('a', 51), // 51 characters
        'company' => 'Acme Corp'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('name'))->toBeTrue();
});

it('fails validation with invalid company characters', function () {
    $validator = Validator::make([
        'amount' => '100',
        'name' => 'John Doe',
        'company' => 'Acme Corp @#$' // Invalid special characters
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('company'))->toBeTrue();
});

it('fails validation with company too long', function () {
    $validator = Validator::make([
        'amount' => '100',
        'name' => 'John Doe',
        'company' => str_repeat('a', 51) // 51 characters
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('company'))->toBeTrue();
});

it('passes validation with company containing valid special characters', function () {
    $validator = Validator::make([
        'amount' => '100',
        'name' => 'John Doe',
        'company' => 'Johnson & Johnson-Smith Corp.'
    ], (new StoreWireTransferRequest)->rules());

    expect($validator->fails())->toBeFalse();
});