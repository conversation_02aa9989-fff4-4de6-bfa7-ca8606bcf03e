<?php

namespace App\Modules\Push\Requests;

use App\Modules\Push\Constants\Response;
use App\Modules\Push\Services\PushDomainService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PushDomainStatusUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'action' => ['required', 'string', 'max:50', Rule::in(Response::VALID_ACTIONS)],
            'push_type' => ['required', 'string', 'max:30', Rule::in(Response::PUSH_TYPE)],
            'push_ids' => ['required', 'array'],
            'push_ids.*' => ['integer', 'exists:push_domains,id'],
            'is_opt_in' => ['required', 'boolean'],
        ];
    }

    public function update()
    {
        return PushDomainService::instance()->update($this->push_ids, $this->action, $this->is_opt_in);
    }

    public function authConfirm()
    {
        PushDomainService::instance()->authenticateUser($this->password);
    }
}
