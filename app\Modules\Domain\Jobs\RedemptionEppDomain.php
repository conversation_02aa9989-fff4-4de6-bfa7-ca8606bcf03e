<?php

namespace App\Modules\Domain\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\JobServices\JobRecord;
use App\Modules\Domain\Services\JobServices\JobRedemptionEppService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class RedemptionEppDomain implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use UserLoggerTrait;

    private object $domain;
    private object $registeredDomain;
    private int $id;
    private int $userId;
    private string $registry;
    private string $email;
    private $updateType;
    private int $backOffMinutes = 5;
    private JobRecord $jobRecord;
    private array $refundData;

    public $failOnTimeout = true;

    public function __construct(array $payload)
    {
        $this->domain = $payload[JobPayloadKeys::DOMAIN];
        $this->registeredDomain = $payload[JobPayloadKeys::REGISTERED_DOMAIN];
        $this->id = $this->domain->id;
        $this->userId = $payload[JobPayloadKeys::USER_ID];
        $this->registry = $payload[JobPayloadKeys::REGISTRY];
        $this->email = $payload[JobPayloadKeys::EMAIL];
        $this->updateType = $payload[JobPayloadKeys::UPDATE_TYPE];
        $this->refundData = $payload[JobPayloadKeys::REFUND_DATA];

        $this->jobRecord = new JobRecord($payload);

        $this->onConnection(QueueConnection::DOMAIN_REDEMPTION);
        $this->onQueue(QueueTypes::DOMAIN_REDEMPTION[$this->registry]);
    }

    public $tries = 3;

    public function backoff(): array
    {
        return [
            $this->backOffMinutes * 60,
            $this->backOffMinutes * 60 * 2,
            $this->backOffMinutes * 60 * 3,
        ];
    }

    public function retryUntil(): \DateTime
    {
        return now()->addMinutes($this->backOffMinutes * 10);
    }

    public $timeout = 1800;

    public $uniqueFor = 300;

    public function uniqueId(): int
    {
        return intval(Carbon::now()->timestamp.$this->id);
    }

    public function handle(): void
    {
        try {
            JobRedemptionEppService::instance()->handle($this->jobRecord);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $this->email));
            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();
                return;
            }
            $this->fail();
        }
    }

    public function failed(Throwable $exception): void
    {
        // Handle failed redemption job
    }

    public function retry(): void
    {
        $this->jobRecord->setPendingJob();
    }
}
