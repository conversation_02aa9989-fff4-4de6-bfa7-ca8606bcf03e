<?php

namespace App\Modules\Notification\Requests;

use App\Modules\Notification\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;

class UpdateNotificationReadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|exists:notifications,id',
        ];
    }

    public function read(): void
    {
        $notif = NotificationService::instance()->getNotifById($this->id);
        $notif->update(['read_at' => Carbon::now()]);
        $this->applyDomainSearch($notif);
    }

    private function applyDomainSearch(Builder $notif): void
    {
        $notif = $notif->first();

        if ($notif->redirect_url === route('domain')) {
            $domainName = $this->extractDomainName($notif->message);
            session(['searched_domains' => [$domainName]]);
        }
    }

    private function extractDomainName(string $message): ?string
    {
        if (preg_match('/"([^"]+)"/', $message, $matches) && isset($matches[1])) {
            return strtolower($matches[1]);
        }

        return null;
    }
}
