import { router } from '@inertiajs/react'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

export default function ConfirmPopUp({modal, showModal, domain, status, offers, setOffers}) {

    const [isLoading, setIsLoading] = useState(false);

    const getButton = () => {
        return status == 'pay' ? <button type="button" disabled={isLoading} onClick={() => { handlePay() }} className="cursor-pointer disabled:bg-gray-500 py-2 px-4 bg-green-700 text-white rounded-md font-medium mr-2 transition duration-500"><i className="fas fa-plus"></i> {isLoading ? 'Redirecting' : 'Pay Now'} </button>
        : <button onClick={() => { handleClose() }} type="button" className="cursor-pointer disabled:bg-gray-500 py-2 px-4 bg-red-700 text-white rounded-md font-medium mr-2 transition duration-500"><i className="fas fa-plus"></i> Close Deal </button>
    }

    const handlePay = () => {
        setIsLoading(true)
        router.post(route('marketoffer.checkout'), { id: domain.id })
    }

    const handleClose = () => {
        axios.post(route('marketoffer.update'), { id: domain.id})
        .then(() => {
            showModal(false);
            toast.success('Closed deal.')

            setOffers(offers.map((item) => item.id === domain.id ? { ...item, offer_status: 'offer_closed' } : item));
        })
    }

    return (
        <div className={` ${modal ? '' : 'hidden'} fixed z-10 overflow-y-auto top-0 w-full left-0`} id="modal">
            <div className="flex items-center justify-center min-height-100vh pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity">
                    <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
                    <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                    <div className="inline-block align-center bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                        <div className="bg-white px-4 pt-4 pb-4 sm:p-6 sm:pb-4">
                            <label className="text-lg text-gray-800">Confirm {status == 'pay' ? 'payment' : 'close deal'} for <span className="text-primary font-bold">{domain.domain_name}</span></label>
                        </div>
                        <div className="px-4 py-3 text-right">
                            <button type="button" onClick={() => { showModal(false); }} className="cursor-pointer py-2 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-700 mr-2" ><i className="fas fa-times"></i> Close </button>
                            { getButton() }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
