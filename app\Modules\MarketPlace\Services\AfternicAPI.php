<?php

namespace App\Modules\MarketPlace\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Constants\MarketConstants;
use Error;
use Illuminate\Support\Facades\Http;

class AfternicAPI
{
    private static function getURL($domain)
    {
        $arr = explode('.', $domain);
        $tld = array_pop($arr);
        $action = ($tld == 'org') ? 'pir2' : 'verisign2';

        return Env('DEV_DUMMY_API').$action.'/v3_1/domain';
    }

    public static function placeHold($domain)
    {
        try {
            $res = Http::withtoken(Env('DEV_AFTERNIC_API'))->post(self::getURL($domain).'/check', ['name' => $domain]);
            if (! $res['data']['available']) {
                throw new \ErrorException;
            }

            return response([
                'domain' => "$domain",
                'price' => 10000000,
                'currency' => 'USD',
                'fastTransfer' => true,
                'expiresAt' => '2020-11-03T22:03:18Z',
            ], 200);
        } catch (Error $err) {
            throw new \ErrorException;
        }
    }

    public static function createOrder($domain, $price)
    {
        Http::withtoken(Env('DEV_AFTERNIC_API'))->post(self::getURL($domain), ['name' => $domain]);

        return (object) [
            'domain' => "$domain",
            'orderId' => substr(base_convert(sha1(uniqid(mt_rand())), 16, 36), 0, 7),
            'price' => intval($price) * MarketConstants::MICRO,
            'currency' => 'USD',
            'fastTransfer' => true,
            'orderStatus' => 'PENDING',
        ];
    }

    public static function getOrder($data, $from)
    {
        $domain = ($from == 'market') ? MarketDomainService::instance()->getDomainName($data->order_id)->domain : $data->domain_name;

        app(AuthLogger::class)->info('AfternicDomainTask: running in test');

        $authres = Http::withtoken(Env('DEV_AFTERNIC_API'))->post(self::getURL($domain).'/auth-request', ['name' => $domain]);

        $auth = $authres['data']['authentication'];

        $arr = ['neverfail.org', 'am-show.org', 'tasd-mosquitoes.org', 'dmasd.org', '5g-net.com', 'k-9-unet.com', 'rare-wine.org'];
        $test = in_array(strtolower($domain), $arr);

        app(AuthLogger::class)->info('AfternicDomainTask: Check domain '.$domain.' is on fail test: '.$test.' from '.json_encode(explode(',', ENV('TEST_FAIL_DOMAINS'))));

        return (object) [
            'domain' => $domain,
            'orderId' => $data->order_id . "",
            'price' => 10000000,
            'currency' => 'USD',
            'fastTransfer' => true,
            'orderStatus' => $test ? 'CANCELLED' : 'TRANSFER_READY',
            'authCode' => "$auth",
        ];
    }

    public static function getCommission($orderID)
    {
        return (object) [
            'partnerCommission' => 0,
            'commissionReason' => json_encode([
                'input' => [
                    'type' => 'BIN',
                    'boost' => 'DELUXE',
                    'price' => 2000000000,
                    'resellerType' => 16,
                    'ns' => [],
                    'resellerId' => 1394,
                ],
                'steps' => [[
                    'check' => 'Seller Commission: Afternic sale',
                    'output' => ['house' => 500000000],
                ],
                ],
                'effectiveStartDate' => '2024-09-04T00:00:00.000+00:00',
            ]),
        ];
    }

    public static function sendRequested($orderID)
    {
        return Http::response(204);
    }

    public static function sendCompleted($orderID)
    {
        return Http::response(204);
    }

    public static function sendInvalidAuth($orderID)
    {
        return Http::response(204);
    }

    public static function sendDomainLocked($orderID)
    {
        return Http::response(204);
    }

    public static function releaseHold($domain)
    {
        return Http::response(204);
    }

    public static function sendCompletionFailureOther($order_id)
    {
        return Http::response(204);
    }
}
