<?php

namespace App\Modules\PaymentMethod\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\PaymentMethod\Requests\PaymentMethodCreateFormRequest;
use App\Modules\PaymentMethod\Requests\PaymentMethodRemoveFormRequest;
use App\Modules\PaymentMethod\Requests\PaymentMethodSetDefaultFormRequest;
use App\Modules\PaymentMethod\Requests\PaymentMethodVerificationFormRequest;
use App\Modules\PaymentMethod\Services\PaymentMethodService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class PaymentMethodController extends Controller
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Render Success Page
     *
     * @return void
     */
    public function renderListPage()
    {
        return Inertia::render(
            'PaymentMethod/PaymentMethodListPage',
            (new PaymentMethodService)->getListData(Auth::user()->id)
        );
    }

    /**
     * Fetch All Items
     */
    public function fetchAllItems(): string|false
    {
        return (new PaymentMethodService)->getUserPaymentMethods(Auth::user()->id);
    }

    /**
     * Render Success Page
     *
     * @return void
     */
    public function renderActionSuccessPage()
    {
        return Inertia::render(
            'PaymentMethod/PaymentMethodCreateSuccessPage',
            [
                'initialSetup' => false,
                'message' => 'You may create more and manage them in your payment methods page',
            ]
        );
    }

    /**
     * Store
     *
     * @param PaymentMethodCreateFormRequest $request
     * 
     * @return void
     */
    public function store(PaymentMethodCreateFormRequest $request)
    {
        (new PaymentMethodService)->storePaymentMethod(
            $request->only('paymentMethodId', 'cardNickname'),
            Auth::user()->id,
        );
    }

    /**
     * Set Payment as Default
     */
    public function setPaymentAsDefault(PaymentMethodSetDefaultFormRequest $request): void
    {
        (new PaymentMethodService)->setPaymentAsDefault(
            $request->only('paymentMethodId', 'cardName'),
            Auth::user()->id
        );
    }

    /**
     * Delete
     *
     *
     * @return void
     */
    public function remove(PaymentMethodRemoveFormRequest $request)
    {
        (new PaymentMethodService)->removePaymentMethod($request->paymentMethodId);
    }

    /**
     * Create Setup Intent
     *
     * @return void
     */
    public function createSetupIntent(PaymentMethodCreateFormRequest $request)
    {
        return (new PaymentMethodService)->processSetupIntent(Auth::user()->id);
    }

    /**
     * Create Payment Intent
     */
    public function createPaymentIntent(Request $request)
    {
        return (new PaymentMethodService)
            ->createPaymentIntent($request->only('amount', 'paymentMethodId'), Auth::user()->stripe_customer_id);
    }

    /**
     * Verify Payment Method
     */
    public function verify(PaymentMethodVerificationFormRequest $request)
    {
        // ...
    }
}
