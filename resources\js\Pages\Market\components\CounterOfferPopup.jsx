import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify';

export default function CounterOfferPopup({ modal, showModal, domain, offers, setOffers }) {

    const [error, setError] = useState(false);
    const [feedback, setFeedBack] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [offerPrice, setOfferPrice] = useState(100);
    const [isAcceptTerms, setIsAcceptTerms] = useState(false);

    const handleOfferChange = (e) => {
        let val = e.target.value;
        (!Number.isInteger(parseInt(val))) ? setOfferPrice(domain.counter_offer_price) : setOfferPrice(parseInt(val))

        setError(false);
    }

    const submitOffer = () => {
        if (offerPrice < 100) return setError(true)

        setIsLoading(true);

        axios.post(route('marketoffer.counter'), { id: domain.id, counter_offer_price: offerPrice, feedback: feedback })
            .finally(() => {
                showModal(false);
                setIsLoading(false);

                setOfferPrice(100);

                toast.success('Success.')

                setOffers(offers.map((item) => item.id === domain.id ? { ...item, offer_status: 'user_counter_offer' } : item));
            })
    }

    useEffect(() => {
        domain.counter_offer_price ? setOfferPrice(domain.counter_offer_price) : setOfferPrice(100)
    }, [modal])

    return (
        <div className={` ${modal ? '' : 'hidden'} fixed z-10 overflow-y-auto top-0 w-full left-0`} id="modal">
            <div className="flex items-center justify-center min-height-100vh pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity">
                    <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
                    <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                    <div className="inline-block align-center bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                        <div className="bg-white px-4 pt-4 pb-4 sm:p-6 sm:pb-4">
                            <label className="text-lg text-gray-800">Counter offer for: <span className="text-primary font-bold">{domain.domain_name}</span></label>
                            <div className="flex items-center relative">
                                <span className="text-xl absolute left-1 pb-1 pl-2">$</span>
                                <input type="number" value={offerPrice} onChange={(e) => { handleOfferChange(e) }} min={0} className="border-gray-300 w-full items-center outline-none rounded pl-6 mt-2 mb-3" />
                            </div>
                            <div className={`${error ? 'block' : 'h-0 invisible'} text-sm mb-2 -mt-2 pl-0.5 text-red-500`}>Minimum offer should be more than the counter offer price of $<span className='font-bold'>{domain.counter_offer_price ? domain.counter_offer_price : 0}</span> </div>
                            <label className="w-full text-gray-800 pt-3">Send Feedback</label>
                            <div className="flex items-center w-full pt-1 pb-2">
                                <textarea disabled={isLoading} value={feedback} onChange={(e) => { setFeedBack(e.target.value) }} className="rounded-md w-full focus:ring-0 border-gray-300 focus:border-gray-500 resize-none" rows={2}></textarea>
                            </div>
                            <label className="block font-semibold cursor-pointer">
                                <input value={isAcceptTerms} onChange={() => { setIsAcceptTerms(!isAcceptTerms) }} className="mr-2 leading-tight focus:ring-0 " type="checkbox" />
                                <span className="text-xs"> If the offer is accepted, I agree to submit payment for this domain name within 5 days. </span>
                            </label>
                        </div>
                        <div className="bg-gray-200 px-4 py-3 text-right">
                            <button type="button" disabled={isLoading} onClick={() => { setOfferPrice(100); showModal(false); }} className="cursor-pointer py-2 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-700 mr-2" ><i className="fas fa-times"></i> Cancel</button>
                            <button type="button" disabled={!isAcceptTerms || isLoading} onClick={() => { submitOffer() }} className="cursor-pointer disabled:bg-gray-500 py-2 px-4 bg-primary text-white rounded-md font-medium hover:bg-blue-500 mr-2 transition duration-500"><i className="fas fa-plus"></i> Make Offer</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
/**

 */