<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Http;

class MarketSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'domains' => 'required|string',
            'page' => 'integer',
            'perPage' => '',
        ];
    }

    public function doMarketSearch()
    {
        $page = $this->page ? $this->page : 1;
        $limit = $this->perPage ? $this->perPage : 100;

        if ($limit >= 500) {
            usleep(rand(2000000, 3500000));
        }

        return Http::market()->get("$this->domains?extension=com,net,org&limit=$limit&page=$page")->body();
    }
}
