<?php

namespace App\Events;

use App\Modules\Notification\Services\NotificationService;
use App\Modules\Notification\Services\GeneralNotificationService;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NotificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $userId;
    public $unreadCount;
    public $data;
    public $notificationId;
    public $eventType;
    public $type;
    public $shouldShowBadge;
    private $previousUnreadCount;

    /**
     * Create a new event instance.
     */
    public function __construct(string $userId, string $eventType = 'sent', ?string $notificationId = null, string $type = 'domain')
    {
        $this->userId = $userId;
        $this->eventType = $eventType;
        $this->notificationId = $notificationId;
        $this->type = $type;
        $this->shouldShowBadge = ($eventType === 'sent');

        // Store the current unread count before any updates
        $this->previousUnreadCount = $this->type === 'announcement' 
            ? GeneralNotificationService::instance()->getUnreadCount($this->userId)
            : NotificationService::instance()->getUnreadCount($this->userId);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        if ($this->type === 'announcement') {
            if ($this->eventType === 'read') {
                $this->unreadCount = GeneralNotificationService::instance()->getUnreadCount($this->userId);
                $this->data = GeneralNotificationService::instance()->getNotifications($this->userId);
            } else {
                $this->data = GeneralNotificationService::instance()->getNotifications($this->userId);
                $this->unreadCount = GeneralNotificationService::instance()->getUnreadCount($this->userId);
            }
        } else {
            if ($this->eventType === 'read') {
                $this->unreadCount = NotificationService::instance()->getUnreadCount($this->userId);
                // For read events, we want to make sure we're passing the notification ID
                $this->data = [
                    'notificationId' => $this->notificationId,
                    'items' => NotificationService::instance()->get_dropdown($this->userId)
                ];
            } else {
                $this->data = NotificationService::instance()->get_dropdown($this->userId);
                $this->unreadCount = NotificationService::instance()->getUnreadCount($this->userId);
            }
        }

        return [
            new PrivateChannel('Notifications.' . $this->userId),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $currentUnreadCount = $this->unreadCount;
        
        return [
            'unreadCount' => $currentUnreadCount,
            'data' => $this->data,
            'notificationId' => $this->notificationId,
            'type' => $this->type,
            'eventType' => $this->eventType,
            'shouldShowBadge' => $currentUnreadCount > $this->previousUnreadCount // Will be true if unread count increased
        ];
    }

    public function broadcastAs(): string
    {
        return 'notif.' . $this->eventType;
    }
}
