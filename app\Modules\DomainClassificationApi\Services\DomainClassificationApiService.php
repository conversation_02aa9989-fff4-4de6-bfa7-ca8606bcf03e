<?php

namespace App\Modules\DomainClassificationApi\Services;

use App\Models\Domain;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Support\Facades\Http;

class DomainClassificationApiService
{
    /**
     * Api Url
     */
    private string $apiUrl;

    /**
     * Api Key
     */
    private string $apiKey;

    /**
     * Origin
     */
    private string $origin;

    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        $this->apiUrl = config('domain_classification.url');
        $this->origin = config('domain_classification.origin');
        $this->apiKey = config('domain_classification.apiKey');
    }

    /**
     * Process API Headers
     *
     * @param  $response
     */
    private function processHeaders(): array
    {
        return [
            'Origin' => $this->origin,
            'Authorization' => "Bearer {$this->apiKey}",
        ];
    }

    /**
     * Process API Response
     */
    private function processResponse($response)
    {
        switch ($response->status()) {
            case 200:
                return $response->json();
                break;

            case 201:
                return $response->json();
                break;

            case 202:
                return $response->json();
                break;

            case 203:
                return $response->json();
                break;

            case 204:
                return $response->json();
                break;

            case 403:
                return [
                    'success' => false,
                    'message' => 'API Server Access Forbidden',
                ];
                break;

            case 404:
                return [
                    'success' => false,
                    'message' => 'API Endpoint not found',
                ];
                break;

            case 422:
                return [
                    'success' => false,
                    'message' => 'Something went wrong with processing',
                ];
                break;

            case 500:
                return [
                    'success' => false,
                    'message' => 'API Server Down',
                ];
                break;

            default:
                return [
                    'success' => false,
                    'message' => 'Could not retrieve from API',
                ];
                break;
        }
    }

    /**
     * Fetch Domain Data
     */
    public function fetchDomain(int $domainId)
    {
        $domain = Domain::findOrFail($domainId);

        $url = "{$this->apiUrl}/fetchByName/{$domain->name}";

        $headers = $this->processHeaders();

        try {
            $response = Http::withHeaders($headers)
                ->get(
                    $url,
                );

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }

    /**
     * Insert Client
     *
     * @param  string  $userEmail
     * @param  string  $userBodisApiKey
     */
    public function insertClient(string $email, ?string $apiKeyBodis = null)
    {
        $url = "{$this->apiUrl}/insertSDClient";
        $headers = $this->processHeaders();

        $payload =
        [
            'email' => $email,
            'api_key_bodis' => $apiKeyBodis,
        ];

        try {
            $response = Http::withHeaders($headers)
                ->withBody(json_encode($payload), 'application/json')
                ->post($url);

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }

    /**
     * Insert Domain
     *
     * @param  string  $clientEmail  = null
     * @param  bool  $shouldScraped  = true
     * @param  bool  $isAutomated  = true
     */
    public function insertDomain(string $domainName, ?string $clientEmail = null, bool $shouldScraped = true, bool $isAutomated = true)
    {
        $url = "{$this->apiUrl}/insertNewDomain";
        $headers = $this->processHeaders();

        $payload =
        [
            'name' => $domainName,
            'clientEmail' => $clientEmail,
            'shouldScraped' => $shouldScraped,
            'isAutomated' => $isAutomated,
        ];

        try {
            $response = Http::withHeaders($headers)
                ->withBody(json_encode($payload), 'application/json')
                ->post($url);

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }

    /**
     * Bulk Insert Domains
     *
     * @param  array $domains
     * @param  string $clientEmail
     * @param  bool  $shouldScraped  = true
     * @param  bool  $isAutomated  = true
     */
    public function bulkInsertDomain(array $domains, ?string $clientEmail = null, bool $shouldScraped = true, bool $isAutomated = true)
    {
        $url = "{$this->apiUrl}/bulkInsertNewDomains";
        $headers = $this->processHeaders();

        $payload =
        [
            'domainNames' => $domains,
            'clientEmail' => $clientEmail,
            'shouldScraped' => $shouldScraped,
            'isAutomated' => $isAutomated,
        ];

        try {
            $response = Http::withHeaders($headers)
                ->withBody(json_encode($payload), 'application/json')
                ->post($url);

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }

    /**
     * Update Client API Key
     */
    public function updateClientApiKey(string $clientEmail, string $apiKeyBodis)
    {
        $url = "{$this->apiUrl}/insertUpdateSdClientApiKey";
        $headers = $this->processHeaders();

        $payload =
        [
            'clientEmail' => $clientEmail,
            'api_key_bodis' => $apiKeyBodis,
        ];

        try {
            $response = Http::withHeaders($headers)
                ->withBody(json_encode($payload), 'application/json')
                ->post($url);

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }

    /**
     * Update Domain Ownership
     */
    public function updateDomainOwnership(string $domainName, string $newClientEmail)
    {
        $url = "{$this->apiUrl}/updateDomainOwnership";
        $headers = $this->processHeaders();

        $payload =
        [
            'domainName' => $domainName,
            'clientEmail' => $newClientEmail,
        ];

        try {
            // ! NOTE: IF CLIENT EMAIL DOESN'T EXIST, IT WILL BE CREATED
            $response = Http::withHeaders($headers)
                ->withBody(json_encode($payload), 'application/json')
                ->post($url);

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }

    /**
     * Bulk Update Domain Ownership
     */
    public function bulkUpdateDomainOwnership(array $domains, string $newClientEmail)
    {
        $url = "{$this->apiUrl}/bulkUpdateDomainOwnership";
        $headers = $this->processHeaders();

        $payload =
        [
            'domainNames' => $domains,
            'clientEmail' => $newClientEmail,
        ];

        try {
            // ! NOTE: IF CLIENT EMAIL DOESN'T EXIST, IT WILL BE CREATED
            $response = Http::withHeaders($headers)
                ->withBody(json_encode($payload), 'application/json')
                ->post($url);

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }

    /**
     * Remove Client API Key
     */
    public function removeClientApiKey(string $clientEmail)
    {
        $url = "{$this->apiUrl}/removeSdClientBodisApiKey";
        $headers = $this->processHeaders();

        $payload =
        [
            'clientEmail' => $clientEmail,
        ];

        try {
            $response = Http::withHeaders($headers)
                ->withBody(json_encode($payload), 'application/json')
                ->post($url);

            return $this->processResponse($response);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            return [
                'success' => false,
                'message' => 'Could not retrieve from API',
            ];
        }
    }
}
