<?php

namespace App\Modules\Domain\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Domain\Requests\DomainPrivacyRequest;
use Inertia\Inertia;
use Inertia\Response;

class DomainPrivacyController extends Controller
{
    public function updatePrivacy(DomainPrivacyRequest $request, $id): Response
    {
        $request->updatePrivacy($id);

        return Inertia::render('Notice/ConfirmationMessage', ['message' => 'Updating domain privacy is in process.']);
    }
}
