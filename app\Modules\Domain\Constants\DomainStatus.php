<?php

namespace App\Modules\Domain\Constants;

final class DomainStatus
{
    public const ACTIVE = 'ACTIVE';

    public const EXPIRED = 'EXPIRED';

    public const FAILED = 'FAILED';

    public const IN_PROCESS = 'IN_PROCESS';

    public const TRANSFERRED = 'TRANSFERRED';

    public const NOT_AVAILABLE = 'NOT_AVAILABLE';

    public const PENDING = 'PENDING';

    public const REDEMPTION = 'REDEMPTION';

    public const MAX_REGISTRATION_ERROR = 'MAX_REGISTRATION_ERROR';

    public const PARAMETER_POLICY_ERROR = 'PARAMETER_POLICY_ERROR';

    public static function all()
    {
        return [
            self::ACTIVE,
            self::EXPIRED,
            self::FAILED,
            self::IN_PROCESS,
            self::TRANSFERRED,
            self::NOT_AVAILABLE,
            self::PENDING,
            self::REDEMPTION,
        ];
    }

    public static function getDomainsTab()
    {
        return [
            'all',
            self::ACTIVE,
            self::IN_PROCESS,
            self::EXPIRED,
            self::REDEMPTION,
        ];
    }
}
