<?php

namespace App\Rules\Payment;

use App\Modules\Payment\Constants\CheckoutType;
use App\Modules\Payment\Constants\OtherFees;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateOtherFees implements ValidationRule
{
    private $type;

    private $keys;

    public function __construct(string $type)
    {
        $this->type = $type;
        $this->keys = ($type == CheckoutType::ACCOUNT_BALANCE) ? OtherFees::ACCOUNT_BALANCE :
            array_merge(OtherFees::DEFAULT_FEES, OtherFees::CHECKOUT_BY_TYPE[$this->type] ?? []);
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $exists = $this->otherFeesExists($value);

        if (! $exists) {
            $fail('There are some fees missing.');
        }
    }

    public function otherFeesExists(array $otherFees)
    {
        foreach ($this->keys as $key) {
            if (! isset($otherFees[$key])) {
                // Log::info($key);

                return false;
            }

            if (in_array($key, OtherFees::DEFAULT_FEES)) {
                if ($otherFees[$key] <= 0) {
                    return false;
                }
            }
        }

        return true;
    }
}
