<?php

namespace App\Modules\MarketPlace\Services;

use App\Events\EmailSent;
use App\Mail\Constants\MailConstant;
use App\Mail\MarketPaymentInvoiceMail;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Traits\UserContact;
use App\Util\Constant\QueueConnection;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;


class MarketDomainService
{
    use UserContact, UserLoggerTrait;

    public static function instance()
    {
        $marketDomainService = new self;

        return $marketDomainService;
    }

    public function getAllPendingDomains(): Collection
    {
        return DB::table('market_place_domains')
            ->where('status', MarketConstants::DOMAIN_PENDING)
            ->get();
    }

    public function getAllJobRetrys(): Collection
    {
        return DB::table('afternic_job_retries')
            ->where('job_status', MarketConstants::JOB_PENDING)
            ->get();
    }

    public function isInMarketPlace($domain): bool
    {
        $query = DB::table('market_place_domains')
            ->select('market_place_domains.order_id', 'market_place_domains.status', 'domains.name as domain')
            ->join('registered_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->where('domains.name', $domain)
            ->exists();

        return $query;
    }

    public function getAllDomains(): array
    {
        $query = DB::table('market_place_domains')
            ->select('market_place_payment_invoices.paid_amount as fees', 'market_place_domains.order_id', 'market_place_domains.status', 'domains.name as domain', 'market_place_domains.total_amount AS price', 'market_place_domains.updated_at')
            ->join('registered_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('market_place_node_invoices', 'market_place_node_invoices.marketplace_payment_node_id', '=', 'market_place_domains.id')
            ->join('market_place_payment_invoices', 'market_place_payment_invoices.id', '=', 'market_place_node_invoices.marketplace_payment_invoice_id')
            ->where('market_place_domains.user_id', Auth::user()->id)
            ->orderBy('market_place_domains.created_at', 'desc')
            ->get();

        return ['items' => $query];
    }

    public function getProductIndex($request)
    {
        $pageLimit = $request->input('limit', 20);

        $query = DB::table('market_place_domains')
            ->select('market_place_payment_invoices.paid_amount as fees', 'market_place_domains.order_id', 'market_place_domains.status', 'domains.name as domain', 'market_place_domains.total_amount AS price', 'market_place_domains.updated_at', 'market_place_domains.created_at')
            ->join('registered_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('extensions', 'extensions.id', '=', 'registered_domains.extension_id')
            ->leftJoin('market_place_node_invoices', 'market_place_node_invoices.marketplace_payment_node_id', '=', 'market_place_domains.id')
            ->leftJoin('market_place_payment_invoices', 'market_place_payment_invoices.id', '=', 'market_place_node_invoices.marketplace_payment_invoice_id')
            ->where('market_place_domains.user_id', Auth::user()->id);

        $this->whenHasOrderby($query, $request);
        $this->whenHasStatus($query, $request);
        $this->whenHasTld($query, $request);
        $this->whenHasName($query, $request);

        return $query->paginate($pageLimit)->withQueryString();
    }


    public function getDomainName($orderId): object
    {
        $query = DB::table('market_place_domains')
            ->select('domains.name as domain')
            ->join('registered_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->where('market_place_domains.order_id', $orderId)
            ->first();

        return $query;
    }

    public function getDomainNameById($id): object
    {
        $query = DB::table('market_place_domains')
            ->select('domains.name as domain')
            ->join('registered_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->where('market_place_domains.id', $id)
            ->first();

        return $query;
    }

    public function getByRegisteredDomainId(int $registeredDomainId, int $userId)
    {
        return DB::table('market_place_domains')
            ->where('registered_domain_id', $registeredDomainId)
            ->where('user_id', $userId)
            ->get()->first();
    }

    public function updateByRegisteredDomainId(int $registeredDomainId, int $userId, array $payload)
    {
        DB::table('market_place_domains')
            ->where('registered_domain_id', $registeredDomainId)
            ->where('user_id', $userId)
            ->update($payload);
    }

    public function sendInvoice($invoice_id, $user_id, $order_id, $domain)
    {
        $invoice = DB::table('market_place_payment_invoices')->where('id', $invoice_id)->first();

        $user = DB::table('users')->select('email', 'first_name', 'last_name')->where('id', $user_id)->get()->first();

        $email = $user->email;
        $name = $user->first_name . ' ' . $user->last_name;

        $payload = [
            'email' => $email,
            'name' => $name,
            'domain' => $domain,
            'order_id' => $order_id,
            'total' => ($invoice->paid_amount - $invoice->total_amount),
            'data' => $invoice,
        ];

        try {
            $queueMessage = (new MarketPaymentInvoiceMail($payload))->onConnection(QueueConnection::MAIL_JOB)->onQueue(MailConstant::PAYMENT_INVOICE);
            Mail::to($email)->send($queueMessage);

            $payloadString = json_encode($payload);

            event(new EmailSent(
                $user_id,
                $name,
                $email,
                'Payment Invoice from ' . config('app.name'),
                ucwords(strtolower('TRANSFER')) . ' Payment Invoice',
                $payloadString,
                null
            ));
        } catch (Exception $e) {
            app(AuthLogger::class)->info('MarketPaymentInvoice: ' . $e->getMessage());
            throw new Exception($e->getMessage());
        }
    }

    private function whenHasOrderby(Builder &$query, FormRequest $request): void
    {
        $query->when($request->has('orderby'), function (Builder $q) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], ['asc', 'desc'])) {
                switch ($orderby[0]) {
                    case 'created':
                        $q->orderBy('market_place_domains.created_at', $orderby[1]);
                        break;
                    case 'updated':
                        $q->orderBy('market_place_domains.updated_at', $orderby[1]);
                        break;
                    case 'price':
                        $q->orderBy('market_place_domains.total_amount', $orderby[1]);
                        break;
                    case 'domain':
                        $q->orderBy('domains.name', $orderby[1]);
                        break;
                    default:
                        $q->orderBy('domains.id', 'desc');
                }
            } else {
                $q->orderBy('market_place_domains.id', 'desc');
            }
        })
            ->when(!$request->has('orderby'), function (Builder $q) {
                $q->orderBy('market_place_domains.id', 'desc');
            });
    }


    private function whenHasStatus(Builder &$query, FormRequest $request): void
    {
        $query->when($request->has('status'), function (Builder $q) use ($request) {
            $status = strtolower($request->status);

            if (!in_array($status, MarketConstants::getProductStatus())) {
                return;
            }

            $q->whereLike('market_place_domains.status', $status);
        });
    }

    private function whenHasTld(Builder &$query, FormRequest $request): void
    {
        $query->when($request->has('tld'), function (Builder $q) use ($request) {
            $tld = $request->tld;
            if (!in_array($tld, ['com', 'net', 'org'])) {
                return;
            }
            $q->where('extensions.name', $tld);
        });
    }

    private function whenHasName(Builder &$query, FormRequest $request)
    {
        $query->when($request->has('domain'), function (Builder $q) use ($request) {
            $q->where('domains.name', 'ilike', $request->domain . '%');
        });
    }

}
