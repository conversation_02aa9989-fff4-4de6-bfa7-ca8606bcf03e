<?php

namespace App\Modules\AccountSecuritySettings\Services;

use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppRecoveryCodeService;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppService;
use Illuminate\Support\Facades\Auth;

class AccountSecuritySettingsService
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Process Index Data
     */
    public function processIndexData(): array
    {
        $secretKey = (new AuthenticatorAppService)->generateSecretKey();
        $qrCodeImageUrl = (new AuthenticatorAppService)->generateQrCodeImageURL($secretKey, Auth::user()->id);
        $recoveryCodes = (new AuthenticatorAppRecoveryCodeService)->fetchUserRecoveryCodes(Auth::user()->id);

        return
        [
            'secretKey' => $secretKey,
            'qrCodeImageUrl' => $qrCodeImageUrl,
            'recoveryCodeInformation' => [
                'total' => $recoveryCodes->count(),
                'used' => $recoveryCodes->filter(fn ($recoveryCode) => ! is_null($recoveryCode->last_used))->count(),
            ],
        ];
    }
}
