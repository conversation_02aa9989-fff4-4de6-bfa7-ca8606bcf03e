<?php

namespace App\Http\Controllers;

use App\Modules\UserProfile\Requests\AddressUpdateRequest;
use App\Modules\UserProfile\Requests\ProfileUpdateRequest;
use App\Models\AuthenticatorAppRecoveryCode;
use App\Modules\Auth\Services\AuthenticatorApp\AuthenticatorAppService;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        $secretKey      = (new AuthenticatorAppService())->generateSecretKey();
        $qrCodeImageUrl = (new AuthenticatorAppService())->generateQrCodeImageURL($secretKey, Auth::user()->id);
        
        $recoveryCodes = AuthenticatorAppRecoveryCode::where('user_id', '=', Auth::id())->get();
        
        return Inertia::render(
            'Profile/Edit', 
            [
                'mustVerifyEmail'         => $request->user() instanceof MustVerifyEmail,
                'status'                  => session('status'),
                'secretKey'               => $secretKey,
                'qrCodeImageUrl'          => $qrCodeImageUrl,
                'recoveryCodeInformation' => 
                [
                    'total' => $recoveryCodes->count(), 
                    'used'  => $recoveryCodes->filter(fn($recoveryCode) => !is_null($recoveryCode->last_used))->count()
                ]
            ]
        );
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return Redirect::route('profile.edit');
    }

    public function updateAddress(AddressUpdateRequest $request): RedirectResponse
    {
        $request->user()->fill($request->validated());
        $request->user()->save();

        return Redirect::route('profile.edit');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
