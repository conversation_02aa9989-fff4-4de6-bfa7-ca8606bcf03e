import { Link, usePage } from "@inertiajs/react";
import PageFooter from "@/Components/Policy/Footer";
import PageHeader from "@/Components/Policy/Header";
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";

export default function DomainCancellation() {
    const { userOn } = usePage().props;

    const content = (
        <div>
            <div className="mx-auto container max-w-[600px] text-center flex justify-center items-center flex-col-2">
                <div className="flex w-auto h-[20rem]">
                    <img
                        className="h-auto w-100"
                        src="/assets/images/order_confirmed.svg"
                        alt="background"
                    />
                </div>
                <div className="flex flex-col items-start pl-4">
                        
                    <span className="text-l text-start text-gray-700 mt-2">
                       We've received your request to delete your domain and will send a confirmation email as soon as the process is complete.
                    </span>
                </div>
            </div>

            <div className="w-full text-center flex flex-col items-center space-y-4">
                <Link
                    href={route("domain")}
                    className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none"
                >
                    Go to My Domain
                </Link>
            </div>
        </div>
    );

    return (
        <>
            {!userOn ? (
                <AccountCenterLayout
                    isReportAbusePage={true}
                    NewElementClassNamesIfHome="w-full"
                >
                    {content}
                </AccountCenterLayout>
            ) : (
                content
            )}
            <PageFooter />
        </>
    );
}
